const crypto = require('crypto');

// 模拟CryptoJS.SHA1和MD5
const CryptoJS = {
    SHA1: function(data) {
        return {
            toString: function() {
                return crypto.createHash('sha1').update(data).digest('hex');
            }
        };
    },
    MD5: function(data) {
        return {
            toString: function() {
                return crypto.createHash('md5').update(data).digest('hex');
            }
        };
    }
};

// Base64编码
const Base64 = {
    encode: function(str) {
        return Buffer.from(str).toString('base64');
    }
};

// 签名生成对象
var signSynopsis = {
    createTimestamp: function() {
        return parseInt((new Date).getTime());
    },

    paramsMerge: function(params) {
        params = params || {};
        var keys = Object.keys(params).sort();
        var filteredParams = {};

        keys.forEach(function(key) {
            if ("file_path" !== key) {
                filteredParams[key] = params[key];
            }
        });

        var paramStr = "";
        for (var key in filteredParams) {
            paramStr += key + "=" + filteredParams[key];
        }
        return paramStr;
    },

    // 尝试不同的参数合并方式
    paramsMerge2: function(params) {
        params = params || {};
        var keys = Object.keys(params).sort();
        var paramStr = "";
        for (var i = 0; i < keys.length; i++) {
            var key = keys[i];
            if ("file_path" !== key) {
                if (paramStr) paramStr += "&";
                paramStr += key + "=" + params[key];
            }
        }
        return paramStr;
    },

    paramsMerge3: function(params) {
        // 直接使用原始请求体格式
        return "symbol=" + params.symbol + "&password=" + params.password;
    },

    sign: function(params, timestamp) {
        var result = {
            timestamp: timestamp || this.createTimestamp()
        };

        console.log("=== 测试不同的参数组合方式 ===");

        // 方式1: 原始方式
        var mergedParams1 = this.paramsMerge(params);
        console.log("方式1 参数:", mergedParams1);

        // 方式2: 用&连接
        var mergedParams2 = this.paramsMerge2(params);
        console.log("方式2 参数:", mergedParams2);

        // 方式3: 原始请求体格式
        var mergedParams3 = this.paramsMerge3(params);
        console.log("方式3 参数:", mergedParams3);

        // 测试不同的签名前缀组合
        var prefixes = [
            "mini_1.19:" + result.timestamp,
            "mini_1.19" + result.timestamp,
            result.timestamp.toString()
        ];

        var paramVariants = [mergedParams1, mergedParams2, mergedParams3];

        console.log("\n=== 尝试所有组合 ===");

        for (var i = 0; i < prefixes.length; i++) {
            for (var j = 0; j < paramVariants.length; j++) {
                var signString = prefixes[i] + paramVariants[j];
                var md5Hash = CryptoJS.MD5(signString).toString();
                var signature = "mini_1.19:" + md5Hash;
                var base64Sig = Base64.encode(signature);

                console.log(`前缀${i+1}+参数${j+1}: ${signString}`);
                console.log(`MD5: ${md5Hash}`);
                console.log(`Base64: ${base64Sig}`);
                console.log(`匹配: ${base64Sig === "bWluaV8xLjE5OjM5ZmIwYjg2N2U1N2U2MzVlOGExMzQ0MzY0ZjlhMmZjZDQzZWFmOTU="}`);
                console.log("---");
            }
        }

        return result;
    }
};

// 测试数据
const testParams = {
    symbol: "H7Vq89sLHpKJBcyPALZ40g1755837626550",
    password: "OyIeEKnApDquWth4uY8RjA1755837626550"
};

const testTimestamp = 1755837626550;
const expectedSignature = "bWluaV8xLjE5OjM5ZmIwYjg2N2U1N2U2MzVlOGExMzQ0MzY0ZjlhMmZjZDQzZWFmOTU=";

console.log("=== 测试签名生成 ===");
console.log("预期签名:", expectedSignature);
console.log("解码后:", Buffer.from(expectedSignature, 'base64').toString());
console.log("");

const result = signSynopsis.sign(testParams, testTimestamp);

console.log("\n=== 结果对比 ===");
console.log("预期签名:", expectedSignature);
console.log("SHA1结果:", result.signature_sha1);
console.log("MD5结果:", result.signature_md5);
console.log("SHA1匹配:", result.signature_sha1 === expectedSignature);
console.log("MD5匹配:", result.signature_md5 === expectedSignature);
