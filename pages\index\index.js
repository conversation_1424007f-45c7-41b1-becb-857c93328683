! function() {
  "use strict";
  (wx.webpackJsonp = wx.webpackJsonp || []).push([
    [2539], {
      5881: function(e, n, t) {
        var a = t(4886),
          o = t(6234),
          r = t(1678),
          c = t.n(r),
          i = t(2784),
          u = t(3675),
          s = t(3494),
          p = t(372),
          f = t(2322);

        function g() {
          var e = (0, i.useState)(""),
            n = (0, o.Z)(e, 2),
            t = n[0],
            a = n[1],
            r = (0, s.Mi)();
          return (0, i.useEffect)((function() {
            var e, n = ((null === (e = c().getCurrentInstance().router) || void 0 === e ? void 0 : e.params) || {}).hidehomebutton;
            1 === Number(n) && c().hideHomeButton(), c().hideShareMenu({
              menus: ["shareAppMessage"]
            });
            var t = "".concat(r, "/#/index"),
              o = (0, p.ZP)(""),
              i = c().getStorageSync("Authorization");
            if (i) {
              var u = "".concat(o, "/").concat(p.ZS, "/front/uniapp/jump");
              a("".concat(u, "?token=").concat(i, "&address=").concat(t))
            } else a(t)
          }), []), t && (0, f.jsx)(u.kh, {
            src: t
          })
        }
        g.enableShareAppMessage = !0, Page((0, a.createPageConfig)(g, "pages/index/index", {
          root: {
            cn: []
          }
        }, {
          navigationBarTitleText: "希望学·看课",
          backgroundColorTop: "#fff",
          pageOrientation: "portrait",
          enableShareAppMessage: !0,
          enablePullDownRefresh: !1
        } || {}))
      }
    },
    function(e) {
      e.O(0, [2107, 1216, 8592], (function() {
        return function(n) {
          return e(e.s = n)
        }(5881)
      })), e.O()
    }
  ])
}();