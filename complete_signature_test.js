const crypto = require('crypto');

// 模拟CryptoJS
const CryptoJS = {
    AES: {
        encrypt: function(plaintext, key, options) {
            const cipher = crypto.createCipheriv('aes-128-cbc', key, options.iv);
            let encrypted = cipher.update(plaintext, 'utf8', 'base64');
            encrypted += cipher.final('base64');
            return {
                ciphertext: {
                    toString: function() {
                        return encrypted;
                    }
                }
            };
        }
    },
    enc: {
        Utf8: {
            parse: function(str) {
                return Buffer.from(str, 'utf8');
            }
        }
    },
    mode: {
        CBC: 'cbc'
    },
    pad: {
        Pkcs7: 'pkcs7'
    },
    SHA1: function(data) {
        return {
            toString: function() {
                return crypto.createHash('sha1').update(data).digest('hex');
            }
        };
    },
    MD5: function(data) {
        return {
            toString: function() {
                return crypto.createHash('md5').update(data).digest('hex');
            }
        };
    }
};

// MD5函数
function md5(data) {
    return crypto.createHash('md5').update(data).digest('hex');
}

// Base64编码
const Base64 = {
    encode: function(str) {
        return Buffer.from(str).toString('base64');
    }
};

// AES加密函数（解混淆后的真实逻辑）
var setAesField = function(plainText, fieldKey) {
    // 获取加密密钥前缀
    var secretPrefix = "aes_key";
    // 生成当前时间戳（毫秒）
    var timestamp = parseInt((new Date).getTime());
    // 生成16位加密密钥：md5(密钥前缀+时间戳)的第8-23位
    var secretKey = md5(secretPrefix + timestamp).toString().substr(8, 16);
    // 生成加密密钥的UTF8编码
    var key = CryptoJS.enc.Utf8.parse(secretPrefix);
    // 生成初始向量(IV)的UTF8编码
    var iv = CryptoJS.enc.Utf8.parse(secretKey);
    // 将明文转换为UTF8编码的字节数组
    var plainTextBytes = CryptoJS.enc.Utf8.parse(plainText);
    
    // 执行AES加密：CBC模式，Pkcs7填充
    var encrypted = CryptoJS.AES.encrypt(plainTextBytes, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    
    // 处理加密结果：Base64编码去除等号，替换+为-，/为_，最后拼接时间戳
    var encryptedStr = encrypted.ciphertext.toString()
        .replace(/\=/gi, "")
        .replace(/\+/gi, "-")
        .replace(/\//gi, "_") + timestamp;
    
    return {
        encryptFieldKey: fieldKey || "",
        encryptResult: encryptedStr
    };
};

// 签名生成对象（解混淆后的逻辑）
var signSynopsis = {
    createTimestamp: function() {
        return parseInt((new Date).getTime());
    },

    paramsMerge: function(params) {
        params = params || {};
        var keys = Object.keys(params).sort();
        var filteredParams = {};
        
        keys.forEach(function(key) {
            if ("file_path" !== key) {
                filteredParams[key] = params[key];
            }
        });
        
        var paramStr = "";
        for (var key in filteredParams) {
            paramStr += key + "=" + filteredParams[key];
        }
        return paramStr;
    },

    sign: function(params) {
        var result = {
            timestamp: this.createTimestamp()
        };
        
        var signKey = "mini_1.19:" + result.timestamp;
        var mergedParams = this.paramsMerge(params);
        var sha1Hash = CryptoJS.SHA1(signKey + mergedParams).toString();
        var signature = "mini_1.19:" + sha1Hash;
        result.signature = Base64.encode(signature);
        
        return result;
    }
};

console.log("=== 测试AES加密逻辑 ===");

// 从请求数据中提取原始值和时间戳
const knownTimestamp = 1755837626550;
const encryptedSymbol = "H7Vq89sLHpKJBcyPALZ40g1755837626550";
const encryptedPassword = "OyIeEKnApDquWth4uY8RjA1755837626550";

// 分析加密结果的结构
console.log("已知加密结果:");
console.log("symbol:", encryptedSymbol);
console.log("password:", encryptedPassword);
console.log("时间戳:", knownTimestamp);

// 检查加密结果是否以时间戳结尾
const symbolWithoutTimestamp = encryptedSymbol.replace(knownTimestamp.toString(), "");
const passwordWithoutTimestamp = encryptedPassword.replace(knownTimestamp.toString(), "");

console.log("\n去除时间戳后:");
console.log("symbol:", symbolWithoutTimestamp);
console.log("password:", passwordWithoutTimestamp);

// 测试用已知时间戳的AES加密
function testAesWithKnownTimestamp(plainText, timestamp) {
    var secretPrefix = "aes_key";
    var secretKey = md5(secretPrefix + timestamp).toString().substr(8, 16);
    var key = CryptoJS.enc.Utf8.parse(secretPrefix);
    var iv = CryptoJS.enc.Utf8.parse(secretKey);
    var plainTextBytes = CryptoJS.enc.Utf8.parse(plainText);
    
    console.log(`\n测试加密 "${plainText}" (时间戳: ${timestamp}):`);
    console.log("密钥前缀:", secretPrefix);
    console.log("MD5输入:", secretPrefix + timestamp);
    console.log("MD5结果:", md5(secretPrefix + timestamp));
    console.log("16位密钥:", secretKey);
    
    var encrypted = CryptoJS.AES.encrypt(plainTextBytes, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    
    var encryptedStr = encrypted.ciphertext.toString()
        .replace(/\=/gi, "")
        .replace(/\+/gi, "-")
        .replace(/\//gi, "_") + timestamp;
    
    console.log("加密结果:", encryptedStr);
    return encryptedStr;
}

// 尝试不同的原始值
const possibleValues = [
    "test_symbol",
    "test_password", 
    "symbol_value",
    "password_value",
    "13800138000",  // 可能的手机号
    "123456"        // 可能的密码
];

console.log("\n=== 尝试加密不同的原始值 ===");
for (const value of possibleValues) {
    const result = testAesWithKnownTimestamp(value, knownTimestamp);
    console.log(`"${value}" -> "${result}"`);
    console.log(`匹配symbol: ${result === encryptedSymbol}`);
    console.log(`匹配password: ${result === encryptedPassword}`);
    console.log("---");
}

// 现在用加密后的参数测试签名
console.log("\n=== 测试完整签名生成 ===");
const encryptedParams = {
    symbol: encryptedSymbol,
    password: encryptedPassword
};

// 手动构建签名（使用已知时间戳）
const testTimestamp = knownTimestamp;
const signKey = "mini_1.19:" + testTimestamp;
const mergedParams = signSynopsis.paramsMerge(encryptedParams);
const fullSignString = signKey + mergedParams;
const sha1Hash = CryptoJS.SHA1(fullSignString).toString();
const finalSignature = Base64.encode("mini_1.19:" + sha1Hash);

console.log("签名生成过程:");
console.log("1. 时间戳:", testTimestamp);
console.log("2. 签名前缀:", signKey);
console.log("3. 合并参数:", mergedParams);
console.log("4. 完整字符串:", fullSignString);
console.log("5. SHA1哈希:", sha1Hash);
console.log("6. 最终签名:", finalSignature);

const expectedSignature = "bWluaV8xLjE5OjM5ZmIwYjg2N2U1N2U2MzVlOGExMzQ0MzY0ZjlhMmZjZDQzZWFmOTU=";
const expectedHash = "39fb0b867e57e635e8a1344364f9a2fcd43eaf95";

console.log("\n=== 结果对比 ===");
console.log("预期签名:", expectedSignature);
console.log("生成签名:", finalSignature);
console.log("预期哈希:", expectedHash);
console.log("生成哈希:", sha1Hash);
console.log("签名匹配:", finalSignature === expectedSignature);
console.log("哈希匹配:", sha1Hash === expectedHash);
