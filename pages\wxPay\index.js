! function() {
  "use strict";
  (wx.webpackJsonp = wx.webpackJsonp || []).push([
    [7751], {
      3027: function(e, t, n) {
        var a = n(4886),
          o = n(1678),
          r = n.n(o),
          c = n(2784),
          i = n(8659);

        function u() {
          var e = (0, c.useRef)(""),
            t = (0, c.useRef)(""),
            n = (0, c.useRef)("https://m.aidoutang.com/market/train1.html#");
          (0, c.useEffect)((function() {
            var o;
            if (!r().getStorageSync("oauth_open_id")) return r().showToast({
              title: "直播间太火爆啦，快联系辅导老师抢占优惠吧！",
              icon: "none",
              duration: 2e3
            }), void setTimeout((function() {
              r().navigateBack({
                delta: 1
              })
            }), 2e3);
            var c = (null === (o = r().getCurrentInstance().router) || void 0 === o ? void 0 : o.params) || {};
            (0, i.tK)({
              key: c.key
            }).then((function(n) {
              var o = n.code,
                c = n.data;
              if (0 === o) {
                var i = c.storage_data,
                  u = JSON.parse(i.json_str),
                  s = u.course_id,
                  d = u.course_package_id,
                  p = u.uuid,
                  g = u.page_key,
                  f = u.source_id;
                e.current = g, t.current = f;
                var l = {
                  uuid: p,
                  page_key: g,
                  source_id: f,
                  course_id: s,
                  group_id: "",
                  payment_mode: 2,
                  course_package_id: d,
                  app_id: "wxd2606008715f7ba5",
                  open_id: r().getStorageSync("oauth_open_id")
                };
                a(l)
              }
            })), (0, i.XW)().then((function(e) {
              console.log("getPageUrl", e);
              var t = e.code,
                a = e.data;
              if (0 === t) {
                var o = a.config.url;
                n.current = o
              }
            }))
          }), []);
          var a = function(e) {
              (0, i.Tz)(e).then((function(e) {
                var t = e.code,
                  n = e.data,
                  a = e.msg;
                if (0 === t) {
                  var c = n.wechat_order,
                    i = c.nonce_tr,
                    u = c.prepay_id,
                    s = c.sign,
                    d = c.timestamp,
                    p = c.order_id;
                  o({
                    nonce_tr: i,
                    prepay_id: u,
                    sign: s,
                    timestamp: d,
                    order_id: p
                  })
                } else r().hideLoading(), r().showToast({
                  title: a,
                  icon: "none",
                  duration: 1e3
                }), setTimeout((function() {
                  r().navigateBack({
                    delta: 1
                  })
                }), 1500)
              }))
            },
            o = function(e) {
              var t = e.nonce_tr,
                n = e.prepay_id,
                a = e.sign,
                o = e.timestamp,
                c = e.order_id;
              r().requestPayment({
                paySign: a,
                signType: "MD5",
                nonceStr: t,
                timeStamp: o,
                package: "prepay_id=".concat(n)
              }).then((function() {
                r().hideLoading(), r().showToast({
                  title: "支付成功",
                  icon: "success",
                  duration: 1e3
                }), u(c)
              })).catch((function() {
                r().showToast({
                  icon: "none",
                  duration: 1e3,
                  title: "支付失败"
                }), setTimeout((function() {
                  r().navigateBack({
                    delta: 1
                  })
                }), 1500)
              }))
            },
            u = function(e) {
              (0, i.Wc)({
                order_id: e
              }).then((function(t) {
                var n = t.data,
                  a = n.plant_status,
                  o = n.earliest_order_id;
                1 === a ? s(e, 1, o) : 0 === a ? setTimeout((function() {
                  u(e)
                }), 200) : s(e, 3 === a ? 3 : 2)
              }))
            },
            s = function(a, o, c) {
              console.log("handleGoResultPage", a, o, c, e, t);
              var i = "tplId=6&pagekey=".concat(e.current, "&orderNum=").concat(a, "&courseId=").concat(c || "", "&sourceId=").concat(t.current),
                u = "";
              1 === o && (u = "".concat(n.current, "/completion?").concat(i)), 2 === o && (u = "".concat(n.current, "/trainPayFail?").concat(i)), 3 === o && (u = "".concat(n.current, "/groupPurchaseResult?").concat(i)), r().redirectTo({
                url: "/pages/webViewPay/index?url=".concat(encodeURIComponent(u))
              })
            };
          return null
        }
        u.enableShareAppMessage = !0, Page((0, a.createPageConfig)(u, "pages/wxPay/index", {
          root: {
            cn: []
          }
        }, {
          navigationBarTitleText: "希望学·看课",
          backgroundColorTop: "#fff",
          pageOrientation: "portrait",
          enableShareAppMessage: !0,
          enablePullDownRefresh: !1
        } || {}))
      }
    },
    function(e) {
      e.O(0, [2107, 1216, 8592], (function() {
        return function(t) {
          return e(e.s = t)
        }(3027)
      })), e.O()
    }
  ])
}();