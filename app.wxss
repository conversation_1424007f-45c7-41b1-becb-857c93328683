page {
    font-family: <PERSON>Fang SC,-apple-system,Helvetica Neue,Helvetica,Arial,Hiragino Sans GB,<PERSON> <PERSON>,<PERSON><PERSON>,Microsoft YaHei,sans-serif
}

::-webkit-scrollbar {
    color: transparent;
    display: none;
    height: 0;
    width: 0
}

.fixed {
    background-color: #fff;
    bottom: 0rpx;
    padding: 16rpx 0 84rpx;
    position: fixed;
    width: 100%
}

.fixed .button {
    background: -webkit-gradient(linear,left top,right top,from(#ff664f),to(#ff3627));
    background: -webkit-linear-gradient(left,#ff664f,#ff3627);
    background: linear-gradient(90deg,#ff664f,#ff3627);
    border-radius: 40rpx;
    color: #fff;
    font-size: 30rpx;
    font-weight: 500;
    height: 80rpx;
    line-height: 80rpx;
    margin: 0rpx auto;
    text-align: center;
    width: 60%
}

.icon {
    display: inline-block
}

.page-title {
    background-attachment: fixed;
    background-position: top;
    background-repeat: no-repeat;
    background-size: cover;
    left: 0;
    position: fixed;
    text-align: center;
    top: 0;
    width: 100%;
    z-index: 999
}

.page-title .title-wrapper {
    -ms-flex-pack: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: 64rpx;
    -webkit-justify-content: center;
    justify-content: center;
    margin-bottom: 12rpx;
    margin-top: 56rpx;
    padding: 0 32rpx
}

.page-title .title-wrapper .nav-icon {
    display: -webkit-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-flex;
    height: 48rpx;
    width: 48rpx;
    z-index: 10
}

.page-title .title-wrapper .text {
    color: #333;
    display: inline-block;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    font-size: 34rpx;
    font-weight: 500;
    left: -48rpx;
    position: relative;
    text-align: center
}

.page-title .title-wrapper .home-icon {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-flex;
    height: 64rpx;
    width: 92rpx
}

.page-title .title-wrapper .home-text {
    color: #333;
    display: inline-block;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    font-size: 34rpx;
    font-weight: 500;
    left: -72rpx;
    position: relative;
    text-align: center
}

.button-disable {
    background: #ccc!important
}

.modal {
    background: #fff;
    border-radius: 24rpx;
    padding: 64rpx 36rpx 44rpx;
    position: relative;
    width: 560rpx
}

.modal .close {
    display: block;
    height: 40rpx;
    position: absolute;
    right: 28rpx;
    top: 28rpx;
    width: 40rpx
}

.modal .modal-container {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    text-align: center
}

.modal .modal-container,.modal .modal-container .title {
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}

.modal .modal-container .title {
    -ms-flex-pack: center;
    color: #333;
    font-size: 32rpx;
    font-weight: 500;
    -webkit-justify-content: center;
    justify-content: center;
    margin-bottom: 16rpx
}

.modal .modal-container .title .icon {
    display: inline-block;
    height: 40rpx;
    margin-right: 12rpx;
    width: 40rpx
}

.modal .modal-container .desc {
    color: #666;
    font-size: 28rpx;
    font-weight: 400;
    margin-bottom: 32rpx
}

.modal .modal-container .demoImg {
    display: block;
    height: 440rpx;
    margin-bottom: 32rpx;
    width: 488rpx
}

.modal .native-button {
    background: -webkit-linear-gradient(315deg,#ff664f,#ff3627);
    background: linear-gradient(135deg,#ff664f,#ff3627);
    border: none;
    border-radius: 36rpx;
    border-radius: 40rpx;
    -webkit-box-shadow: 0rpx 8rpx 16rpx -4rpx rgba(191,38,38,.47);
    box-shadow: 0rpx 8rpx 16rpx -4rpx rgba(191,38,38,.47);
    color: #fff;
    font-size: 28rpx;
    font-weight: 500;
    line-height: 28rpx;
    padding: 26rpx 0;
    text-align: center;
    width: 440rpx
}

.robot-container {
    -ms-flex-pack: center;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: 100%;
    -webkit-justify-content: center;
    justify-content: center;
    width: 100%
}

.robot-container .robot {
    margin: 260rpx auto
}

.nut-overlay {
    background: var(--nutui-overlay-bg-color,var(--nutui-black-10,rgba(0,0,0,.7)));
    height: 100%;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: var(--nutui-overlay-zIndex,1000)
}

.nut-overlay .wrapper .content {
    background-color: var(--nutui-overlay-content-bg-color,var(--nutui-gray-1,#fff));
    color: var(--nutui-overlay-content-color,var(--nutui-gray-7,#1a1a1a))
}

.nut-rtl .nut-overlay,[dir=rtl] .nut-overlay {
    left: auto;
    right: 0
}

.nut-overflow-hidden {
    overflow: hidden!important
}

@keyframes nut-fade-in {
    0% {
        opacity: 0
    }

    1% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes nut-fade-out {
    0% {
        opacity: 1
    }

    1% {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

.nut-overlay-slide-appear-active,.nut-overlay-slide-enter-active {
    animation-duration: var(--nutui-overlay-animation-duration,.3s);
    animation-fill-mode: both;
    animation-name: nut-fade-in
}

.nut-overlay-slide-exit-active {
    animation-duration: var(--nutui-overlay-animation-duration,.3s);
    animation-fill-mode: both;
    animation-name: nut-fade-out
}

.nut-popup {
    -webkit-overflow-scrolling: touch;
    background-color: var(--nutui-overlay-content-bg-color,var(--nutui-gray-1,#fff));
    color: var(--nutui-gray-7,#1a1a1a);
    font-size: var(--nutui-font-size-3,28rpx);
    max-height: 100%;
    min-height: 26%;
    overflow-y: auto;
    position: fixed
}

.nut-popup-title {
    align-items: center;
    border-bottom: var(--nutui-popup-title-border-bottom,0);
    display: flex;
    justify-content: center;
    padding: var(--nutui-popup-title-padding,32rpx);
    position: relative
}

.nut-popup-title-left {
    left: var(--nutui-popup-title-padding,32rpx);
    position: absolute
}

.nut-popup-title-title {
    font-size: var(--nutui-popup-title-font-size,var(--nutui-font-size-5,36rpx));
    font-weight: var(--nutui-font-weight-bold,500)
}

.nut-popup-title-description {
    color: var(--nutui-gray-5,#8c8c8c);
    font-size: var(--nutui-popup-description-font-size,var(--nutui-font-size-1,20rpx));
    font-weight: var(--nutui-font-weight,400)
}

.nut-popup-title-right {
    align-items: center;
    color: var(--nutui-gray-5,#8c8c8c);
    display: flex;
    height: var(--nutui-popup-icon-size,var(--nutui-font-size-5,36rpx));
    justify-content: center;
    position: absolute!important;
    right: var(--nutui-popup-title-padding,32rpx);
    top: var(--nutui-popup-title-padding,32rpx);
    width: var(--nutui-popup-icon-size,var(--nutui-font-size-5,36rpx));
    z-index: 1
}

.nut-popup-title-right:active {
    opacity: .7
}

.nut-popup-title-right-top-left {
    left: var(--nutui-popup-title-padding,32rpx);
    top: var(--nutui-popup-title-padding,32rpx)
}

.nut-popup-title-right-bottom-left {
    bottom: var(--nutui-popup-title-padding,32rpx);
    left: var(--nutui-popup-title-padding,32rpx)
}

.nut-popup-title-right-bottom-right {
    bottom: var(--nutui-popup-title-padding,32rpx);
    right: var(--nutui-popup-title-padding,32rpx)
}

.nut-popup-center {
    left: 50%;
    min-height: 10%;
    top: 50%;
    transform: translate(-50%,-50%)
}

.nut-popup-center.nut-popup-round {
    border-radius: var(--nutui-popup-border-radius,48rpx)
}

.nut-popup-bottom,.nut-popup-top {
    max-height: 83%
}

.nut-popup-bottom {
    bottom: 0;
    left: 0;
    width: 100%
}

.nut-popup-bottom.nut-popup-round {
    border-radius: var(--nutui-popup-border-radius,48rpx) var(--nutui-popup-border-radius,48rpx) 0 0
}

.nut-popup-right {
    right: 0;
    top: 0
}

.nut-popup-right.nut-popup-round {
    border-radius: var(--nutui-popup-border-radius,48rpx) 0 0 var(--nutui-popup-border-radius,48rpx)
}

.nut-popup-left {
    left: 0;
    top: 0
}

.nut-popup-left.nut-popup-round {
    border-radius: 0 var(--nutui-popup-border-radius,48rpx) var(--nutui-popup-border-radius,48rpx) 0
}

.nut-popup-top {
    left: 0;
    top: 0;
    width: 100%
}

.nut-popup-top.nut-popup-round {
    border-radius: 0 0 var(--nutui-popup-border-radius,48rpx) var(--nutui-popup-border-radius,48rpx)
}

@keyframes popup-scale-fade-in {
    0% {
        opacity: 0;
        transform: scale(.8)
    }

    to {
        opacity: 1;
        transform: scale(1)
    }
}

@keyframes popup-scale-fade-out {
    0% {
        opacity: 1;
        transform: scale(1)
    }

    to {
        opacity: 0;
        transform: scale(.8)
    }
}

.nut-popup-slide-default-enter-active {
    animation-duration: var(--nutui-popup-animation-duration,.3s);
    animation-fill-mode: both;
    animation-name: popup-scale-fade-in
}

.nut-popup-slide-default-exit-active {
    animation-duration: var(--nutui-popup-animation-duration,.3s);
    animation-fill-mode: both;
    animation-name: popup-scale-fade-out
}

@keyframes popup-fade-in {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes popup-fade-out {
    0% {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

.nut-popup-slide-center-enter-active {
    animation-duration: var(--nutui-popup-animation-duration,.3s);
    animation-fill-mode: both;
    animation-name: popup-fade-in
}

.nut-popup-slide-center-exit-active {
    animation-duration: var(--nutui-popup-animation-duration,.3s);
    animation-fill-mode: both;
    animation-name: popup-fade-out
}

@keyframes popup-slide-top-enter {
    0% {
        transform: translate3d(0,-100%,0)
    }

    to {
        transform: translateZ(0)
    }
}

@keyframes popup-slide-top-exit {
    to {
        transform: translate3d(0,-100%,0)
    }
}

.nut-popup-slide-top-appear-active,.nut-popup-slide-top-enter-active {
    animation-duration: var(--nutui-popup-animation-duration,.3s);
    animation-fill-mode: both;
    animation-name: popup-slide-top-enter;
    transform: translateZ(0)
}

.nut-popup-slide-top-exit-active {
    animation-duration: var(--nutui-popup-animation-duration,.3s);
    animation-fill-mode: both;
    animation-name: popup-slide-top-exit
}

@keyframes popup-slide-right-enter {
    0% {
        transform: translate3d(100%,0,0)
    }

    to {
        transform: translateZ(0)
    }
}

@keyframes popup-slide-right-exit {
    to {
        transform: translate3d(100%,0,0)
    }
}

.nut-popup-slide-right-appear-active,.nut-popup-slide-right-enter-active {
    animation-duration: var(--nutui-popup-animation-duration,.3s);
    animation-fill-mode: both;
    animation-name: popup-slide-right-enter;
    transform: translateZ(0)
}

.nut-popup-slide-right-exit {
    animation-duration: var(--nutui-popup-animation-duration,.3s);
    animation-fill-mode: both;
    animation-name: popup-slide-right-exit
}

@keyframes popup-slide-bottom-enter {
    0% {
        transform: translate3d(0,100%,0)
    }

    to {
        transform: translateZ(0)
    }
}

@keyframes slide-bottom-exit {
    to {
        transform: translate3d(0,100%,0)
    }
}

.nut-popup-slide-bottom-appear-active,.nut-popup-slide-bottom-enter-active {
    animation-duration: var(--nutui-popup-animation-duration,.3s);
    animation-fill-mode: both;
    animation-name: popup-slide-bottom-enter;
    transform: translate(0)
}

.nut-popup-slide-bottom-exit {
    animation-duration: var(--nutui-popup-animation-duration,.3s);
    animation-fill-mode: both;
    animation-name: slide-bottom-exit
}

@keyframes popup-slide-left-enter {
    0% {
        transform: translate3d(-100%,0,0)
    }

    to {
        transform: translateZ(0)
    }
}

@keyframes popup-slide-left-exit {
    to {
        transform: translate3d(-100%,0,0)
    }
}

.nut-popup-slide-left-appear-active,.nut-popup-slide-left-enter-active {
    animation-duration: var(--nutui-popup-animation-duration,.3s);
    animation-fill-mode: both;
    animation-name: popup-slide-left-enter;
    transform: translate(0)
}

.nut-popup-slide-left-exit-active,.nut-popup-slide-left-exit-done {
    animation-duration: var(--nutui-popup-animation-duration,.3s);
    animation-fill-mode: both;
    animation-name: popup-slide-left-exit
}

.nut-popup-slide-bottom-exit-done.nut-popup,.nut-popup-slide-center-exit-done.nut-popup,.nut-popup-slide-default-exit-done.nut-popup,.nut-popup-slide-left-exit-done.nut-popup,.nut-popup-slide-right-exit-done.nut-popup,.nut-popup-slide-top-exit-done.nut-popup {
    display: none
}

.nut-popup .nut-overflow-hidden {
    overflow: hidden!important
}

.nut-rtl .nut-popup-title-left,[dir=rtl] .nut-popup-title-left {
    left: auto;
    right: var(--nutui-popup-title-padding,32rpx)
}

.nut-rtl .nut-popup-title-right,[dir=rtl] .nut-popup-title-right {
    left: var(--nutui-popup-title-padding,32rpx);
    right: auto
}

.nut-rtl .nut-popup-title-right-bottom-left,.nut-rtl .nut-popup-title-right-top-left,[dir=rtl] .nut-popup-title-right-bottom-left,[dir=rtl] .nut-popup-title-right-top-left {
    left: auto;
    right: var(--nutui-popup-title-padding,32rpx)
}

.nut-rtl .nut-popup-title-right-bottom-right,[dir=rtl] .nut-popup-title-right-bottom-right {
    left: var(--nutui-popup-title-padding,32rpx);
    right: auto
}

.nut-rtl .nut-popup-title .nut-icon-ArrowLeft,[dir=rtl] .nut-popup-title .nut-icon-ArrowLeft {
    transform: rotate(180deg)
}

.nut-rtl .nut-popup-center,[dir=rtl] .nut-popup-center {
    left: auto;
    right: 50%;
    transform: translate(50%,-50%)
}

.nut-rtl .nut-popup-bottom,.nut-rtl .nut-popup-top,[dir=rtl] .nut-popup-bottom,[dir=rtl] .nut-popup-top {
    left: auto;
    right: 0
}

.nut-input {
    align-items: center;
    background-color: var(--nutui-input-background-color,var(--nutui-gray-1,#fff));
    border-bottom: var(--nutui-input-border-bottom-width,0rpx) solid var(--nutui-input-border-bottom,var(--nutui-black-3,rgba(0,0,0,.06)));
    border-radius: var(--nutui-input-border-radius,0);
    box-sizing: border-box;
    display: flex;
    flex: 1;
    font-size: var(--nutui-input-font-size,var(--nutui-font-size-3,28rpx));
    line-height: 48rpx;
    padding: var(--nutui-input-padding,20rpx 50rpx);
    position: relative;
    width: 100%
}

.nut-input .nut-icon {
    color: #c8c9cc
}

.nut-input .nut-input-native {
    background-color: initial;
    border: 0;
    color: var(--nutui-input-color,var(--nutui-gray-7,#1a1a1a));
    flex: 1;
    font: inherit;
    outline: 0 none;
    padding: 0;
    text-decoration: none;
    width: 100%
}

.nut-input-disabled {
    color: var(--nutui-input-disabled-color,var(--nutui-color-text-disabled,#bfbfbf))!important
}

.nut-input-disabled .h5-input:disabled {
    -webkit-text-fill-color: var(--nutui-input-disabled-color,var(--nutui-color-text-disabled,#bfbfbf));
    background: none;
    color: var(--nutui-input-disabled-color,var(--nutui-color-text-disabled,#bfbfbf));
    opacity: 1
}

.protocol {
    color: #999;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    font-family: PingFangSC-Regular,PingFang SC;
    font-size: 24rpx;
    font-weight: 400;
    width: 100%
}

.protocol__radio {
    height: 48rpx;
    position: relative;
    width: 48rpx
}

.protocol__radio-agree {
    margin-right: 6rpx;
    position: absolute;
    top: -6rpx;
    -webkit-transform: scale(.5);
    -ms-transform: scale(.5);
    transform: scale(.5)
}

.protocol__container {
    width: 570rpx
}

.protocol__text {
    color: #ff3627
}
{--nut-icon-height:32rpx;--nut-icon-width:32rpx;--nut-icon-line-height:32rpx;--animate-duration:1s;--animate-delay:0s
}

page {
--nut-icon-height: 40rpx;
--nut-icon-width: 40rpx;
--nut-icon-line-height: 40rpx;
--animate-duration: 1s;
--animate-delay: 0s
}

.nut-icon {
display: inline-block;
font-size: var(--nut-icon-width);
height: var(--nut-icon-height);
line-height: var(--nut-icon-line-height);
position: relative;
text-align: right;
width: var(--nut-icon-width)
}

.nut-icon:before {
left: 50%;
position: absolute;
top: 50%;
transform: translate(-50%,-50%)
}

.nut-icon-img {
height: var(--nut-icon-height);
-o-object-fit: contain;
object-fit: contain;
width: var(--nut-icon-width)
}

.nut-icon-Loading,.nut-icon-Loading1,.nut-icon-loading,.nut-icon-loading1 {
animation: rotation 1s linear infinite;
display: inline-block
}

.nut-icon-am-infinite {
-webkit-animation-direction: alternate;
animation-direction: alternate;
-webkit-animation-iteration-count: infinite;
animation-iteration-count: infinite
}

.nut-icon-am-jump {
-webkit-animation-delay: var(--animate-delay);
animation-delay: var(--animate-delay);
-webkit-animation-duration: var(--animate-duration);
animation-duration: var(--animate-duration);
-webkit-animation-name: nutJumpOne;
animation-name: nutJumpOne;
-webkit-animation-timing-function: ease;
animation-timing-function: ease
}

.nut-icon-am-jump.nut-icon-am-infinite {
animation-name: nutJump
}

.nut-icon-am-rotate {
-webkit-animation-delay: var(--animate-delay);
animation-delay: var(--animate-delay);
-webkit-animation-duration: var(--animate-duration);
animation-duration: var(--animate-duration);
-webkit-animation-name: rotation;
animation-name: rotation;
-webkit-animation-timing-function: linear;
animation-timing-function: linear
}

.nut-icon-am-rotate.nut-icon-am-infinite {
animation-direction: normal
}

.nut-icon-am-blink {
-webkit-animation-delay: var(--animate-delay);
animation-delay: var(--animate-delay);
-webkit-animation-duration: var(--animate-duration);
animation-duration: var(--animate-duration);
-webkit-animation-name: nutBlink;
animation-name: nutBlink;
-webkit-animation-timing-function: ease-in-out;
animation-timing-function: linear
}

.nut-icon-am-breathe {
-webkit-animation-name: nutBreathe;
animation-name: nutBreathe;
-webkit-animation-timing-function: ease-in-out;
animation-timing-function: ease-in-out
}

.nut-icon-am-breathe,.nut-icon-am-flash {
-webkit-animation-delay: var(--animate-delay);
animation-delay: var(--animate-delay);
-webkit-animation-duration: var(--animate-duration);
animation-duration: var(--animate-duration)
}

.nut-icon-am-flash {
-webkit-animation-name: nutFlash;
animation-name: nutFlash;
-webkit-animation-timing-function: ease-in-out;
animation-timing-function: ease-in-out
}

.nut-icon-am-bounce {
-webkit-animation-delay: var(--animate-delay);
animation-delay: var(--animate-delay);
-webkit-animation-duration: var(--animate-duration);
animation-duration: var(--animate-duration);
-webkit-animation-name: nutBounce;
animation-name: nutBounce;
-webkit-animation-timing-function: ease-in-out;
animation-timing-function: ease-in-out
}

.nut-icon-am-bounce.nut-icon-am-infinite {
animation-direction: normal
}

.nut-icon-am-shake {
-webkit-animation-delay: var(--animate-delay);
animation-delay: var(--animate-delay);
-webkit-animation-duration: var(--animate-duration);
animation-duration: var(--animate-duration);
-webkit-animation-name: nutShake;
animation-name: nutShake;
-webkit-animation-timing-function: ease-in-out;
animation-timing-function: ease-in-out
}

@keyframes rotation {
0% {
    -webkit-transform: rotate(0deg)
}

to {
    -webkit-transform: rotate(1turn)
}
}

@keyframes nutJump {
    to {
        transform: scale3d(.8,1,.9) translateY(-20rpx)
    }
}

@keyframes nutJumpOne {
    50% {
        transform: scale3d(.8,1,.9) translateY(-20rpx)
    }

    to {
        transform: scaleZ(1) translateY(0)
    }
}

@keyframes nutBlink {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes nutBreathe {
    0%,to {
        transform: scale(1)
    }

    50% {
        transform: scale(1.2)
    }
}

@keyframes nutFlash {
    0%,50%,to {
        opacity: 1
    }

    25%,75% {
        opacity: 0
    }
}

@keyframes nutBounce {
    0%,20%,53%,to {
        animation-timing-function: cubic-bezier(.215,.61,.355,1);
        transform: translateZ(0)
    }

    40%,43% {
        animation-timing-function: cubic-bezier(.755,.05,.855,.06);
        transform: translate3d(0,-60rpx,0) scaleY(1.1)
    }

    70% {
        animation-timing-function: cubic-bezier(.755,.05,.855,.06);
        transform: translate3d(0,-30rpx,0) scaleY(1.05)
    }

    80% {
        transform: translateZ(0) scaleY(.95);
        transition-timing-function: cubic-bezier(.215,.61,.355,1)
    }

    90% {
        transform: translate3d(0,-8rpx,0) scaleY(1.02)
    }
}

@keyframes nutShake {
    0% {
        transform: translate(0)
    }

    6.5% {
        transform: translate(-12rpx) rotateY(-9deg)
    }

    18.5% {
        transform: translate(10rpx) rotateY(7deg)
    }

    31.5% {
        transform: translate(-6rpx) rotateY(-5deg)
    }

    43.5% {
        transform: translate(4rpx) rotateY(3deg)
    }

    50% {
        transform: translate(0)
    }
}