.nut-pulltorefresh-head {
    font-size: 24rpx;
    overflow: hidden;
    position: relative
}

.nut-pulltorefresh-head-content {
    align-items: center;
    bottom: 0;
    color: var(--nutui-gray-7,#1a1a1a);
    display: flex;
    flex-direction: column;
    justify-content: center;
    left: 0;
    position: absolute;
    width: 100%
}

.nut-pulltorefresh-head-content-icons {
    height: var(--nutui-pulltorefresh-icon-height,52rpx);
    margin-bottom: 8rpx;
    width: var(--nutui-pulltorefresh-icon-width,72rpx)
}

.nut-pulltorefresh-primary {
    background: var(--nutui-pulltorefresh-color-primary,var(--nutui-color-primary,#fa2c19))
}

.nut-pulltorefresh-primary .nut-pulltorefresh-content,.nut-pulltorefresh-primary .nut-pulltorefresh-head-content {
    color: var(--nutui-white-12)
}

.nut-rtl .nut-pulltorefresh-head-content,[dir=rtl] .nut-pulltorefresh-head-content {
    left: auto;
    right: 0
}

.qrcode-mask {
    -ms-flex-pack: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    background-color: rgba(0,0,0,.8);
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: 100%;
    -webkit-justify-content: center;
    justify-content: center;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%
}

.qrcode-text {
    color: #333;
    font-family: PingFang SC;
    font-size: 28rpx;
    font-weight: 400;
    text-align: center
}

.qrcode-wrapper {
    background: url(https://m.xiwang.com/resource/zj86y9aXtVRu5CAghVWnA-1735031632427.png) no-repeat;
    background-color: #fff;
    background-size: cover;
    border-radius: 32rpx;
    height: 644rpx;
    position: relative;
    text-align: center;
    width: 560rpx
}

.qrcode-wrapper__img {
    position: relative;
    top: -64rpx;
    width: 128rpx
}

.qrcode-wrapper__title {
    margin-bottom: 40rpx;
    margin-top: -40rpx
}

.qrcode-wrapper__tips {
    color: #666;
    font-size: 24rpx;
    margin-bottom: 24rpx;
    margin-top: 40rpx
}

.qrcode-wrapper__logo {
    width: 96rpx
}

.qrcode-wrapper__close {
    bottom: -80rpx;
    height: 52rpx;
    left: 50%;
    position: absolute;
    -webkit-transform: translate(-50%);
    -ms-transform: translate(-50%);
    transform: translate(-50%);
    width: 52rpx
}

.image-popup {
    background: url(https://m.xiwang.com/resource/EIyFoqZSU0vhxA0_XVX9f-1735021626083.png) top no-repeat;
    background-size: 50%;
    text-align: center;
    width: 100%
}

.image-popup .qrcode-img {
    height: 240rpx;
    margin-top: 20rpx;
    width: 240rpx
}

.home-container {
    background-color: #f7f7f7;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 100vh;
    overflow: auto;
    padding-bottom: 104rpx;
    width: 100%
}

.home-container .home-no-login {
    padding: 28rpx
}

.home-container .home-no-login__banner {
    border-radius: 28rpx;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%
}

.home-container .home-card-yindao {
    position: relative
}

.home-container .home-card-yindao__img {
    width: 100%
}

.home-container .home-card-yindao__btn {
    -webkit-animation: bounce 1s ease 0s infinite backwards;
    animation: bounce 1s ease 0s infinite backwards;
    -webkit-animation-iteration-count: 2;
    animation-iteration-count: 2;
    bottom: 80rpx;
    height: 100rpx;
    left: 80rpx;
    position: absolute;
    width: 336rpx
}

.home-container .home-card-yindao .detail_mask {
    height: 140rpx;
    left: 60rpx;
    position: absolute;
    top: calc(50% - 60rpx);
    width: 440rpx
}

.home-container .home-card-yindao2 {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0 24rpx;
    width: 100%
}

.home-container .home-card-yindao2__img {
    width: 100%
}

@-webkit-keyframes bounce {
    0% {
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        opacity: 1;
        -webkit-transform: translateY(0rpx);
        transform: translateY(0rpx)
    }

    24% {
        opacity: 1
    }

    40% {
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        -webkit-transform: translateY(-64rpx);
        transform: translateY(-64rpx)
    }

    62% {
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        -webkit-transform: translateY(-32rpx);
        transform: translateY(-32rpx)
    }

    82% {
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        -webkit-transform: translateY(-16rpx);
        transform: translateY(-16rpx)
    }

    92% {
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        -webkit-transform: translateY(-8rpx);
        transform: translateY(-8rpx)
    }

    25%,55%,75%,90% {
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }

    100% {
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out;
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

@keyframes bounce {
    0% {
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        opacity: 1;
        -webkit-transform: translateY(0rpx);
        transform: translateY(0rpx)
    }

    24% {
        opacity: 1
    }

    40% {
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        -webkit-transform: translateY(-64rpx);
        transform: translateY(-64rpx)
    }

    62% {
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        -webkit-transform: translateY(-32rpx);
        transform: translateY(-32rpx)
    }

    82% {
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        -webkit-transform: translateY(-16rpx);
        transform: translateY(-16rpx)
    }

    92% {
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        -webkit-transform: translateY(-8rpx);
        transform: translateY(-8rpx)
    }

    25%,55%,75%,90% {
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }

    100% {
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out;
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

.home-container .protocol_privacy {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    -webkit-align-items: center;
    align-items: center;
    background: #fff;
    bottom: 0;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    height: 100rpx;
    -webkit-justify-content: center;
    justify-content: center;
    line-height: 38rpx;
    position: fixed;
    width: 100%
}

.home-container .protocol_privacy .protocol__container {
    font-size: 24rpx;
    width: 580rpx
}

.home-container .protocol_privacy .protocol__container__text {
    color: #ff3627
}

.home {
    width: 100%
}

.home,.home .home-scoll-box {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.home .home-scoll-box {
    margin: 0rpx 32rpx;
    padding-top: 24rpx
}

.home .home-scoll-box .home-scoll-wrapper {
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    background: #fff;
    border-radius: 36rpx;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: 64rpx;
    line-height: 64rpx;
    overflow: hidden;
    padding: 0 24rpx
}

.home .home-scoll-box .home-scoll-wrapper .time-icon {
    -ms-flex-negative: 0;
    background: url(https://m.xiwang.com/resource/YeX9Os07flvqyTfF_zlsU-1735029159630.png) no-repeat;
    background-size: contain;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    height: 40rpx;
    margin-right: 10rpx;
    width: 40rpx
}

.home .home-course-card {
    background-color: #fff;
    border-radius: 32rpx;
    margin: 32rpx;
    padding-bottom: 20rpx
}

.home .home-course-card .course-card-top {
    -ms-flex-align: center;
    -ms-flex-pack: justify;
    -webkit-align-items: center;
    align-items: center;
    background: -webkit-gradient(linear,left top,right top,color-stop(2.2%,rgba(248,204,255,.6)),color-stop(33.34%,rgba(205,241,255,.6)),color-stop(111.76%,rgba(201,255,220,.6)));
    background: -webkit-linear-gradient(left,rgba(248,204,255,.6) 2.2%,rgba(205,241,255,.6) 33.34%,rgba(201,255,220,.6) 111.76%);
    background: linear-gradient(90deg,rgba(248,204,255,.6) 2.2%,rgba(205,241,255,.6) 33.34%,rgba(201,255,220,.6) 111.76%);
    border-radius: 32rpx;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: 142rpx;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    padding: 0 44rpx;
    width: 100%
}

.home .home-course-card .course-card-top .home-top-con {
    background: hsla(0,0%,100%,.6);
    border: 2rpx solid #fff;
    border-radius: 20rpx;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-left: 32rpx
}

.home .home-course-card .course-card-top .home-top-con > .home-top-text {
    color: #000;
    font-family: PingFang SC;
    font-size: 28rpx;
    font-weight: 400
}

.home .home-course-card .course-card-top .home-top-left {
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: 88rpx;
    margin-right: 30rpx;
    width: 220rpx
}

.home .home-course-card .course-card-top .home-top-left > .home-top-icon {
    height: 44rpx;
    margin-right: 36rpx;
    width: 44rpx
}

.home .home-course-card .course-card-top .home-top-right {
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: 88rpx;
    width: 272rpx
}

.home .home-course-card .course-card-top .home-top-right > .home-top-icon {
    height: 44rpx;
    margin-right: 24rpx;
    width: 44rpx
}

.home .home-course-card .course-card-top-tx {
    background: -webkit-gradient(linear,left top,right top,from(#ffb8b8),color-stop(28%,#ffdac9),color-stop(99%,#fff0c9));
    background: -webkit-linear-gradient(left,#ffb8b8,#ffdac9 28%,#fff0c9 99%);
    background: linear-gradient(90deg,#ffb8b8,#ffdac9 28%,#fff0c9 99%)
}

.home .home-course-card .course-container {
    font-family: PingFang SC;
    font-style: normal;
    padding-left: 40rpx;
    text-align: left
}

.home .home-course-card .course-container .course-title {
    -ms-flex-align: baseline;
    -webkit-align-items: baseline;
    align-items: baseline;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 12rpx;
    margin-top: 28rpx
}

.home .home-course-card .course-container .course-title .course-subject-name {
    -ms-flex-negative: 0;
    background: #e6b666;
    border-radius: 2rpx 8rpx;
    color: #fff;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    font-family: PingFang SC;
    font-size: 20rpx;
    margin-right: 12rpx;
    padding: 4rpx 6rpx;
    position: relative;
    top: -4rpx
}

.home .home-course-card .course-container .course-title .course-name {
    color: #333;
    font-size: 28rpx;
    font-weight: 500;
    padding-top: 4rpx
}

.home .home-course-card .course-container .course-info-text {
    color: #666;
    font-size: 24rpx;
    font-weight: 400;
    margin-bottom: 28rpx
}

.home .home-course-card .course-container .course-btn-time-box {
    -ms-flex-align: center;
    -ms-flex-pack: justify;
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between
}

.home .home-course-card .course-container .course-btn-time-box .course-time {
    border: 0.8rpx solid #e0e0e0;
    border-radius: 8rpx;
    color: #999;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    font-size: 24rpx;
    font-weight: 400;
    padding: 4rpx 16rpx
}

.home .home-course-card .course-container .course-btn-time-box .btn-box {
    margin-right: 40rpx
}

.home .home-course-card .course-container .course-btn-time-box .btn-box .btn-course-status {
    background: -webkit-linear-gradient(353deg,#ff664f 4.33%,#fa3b2d 90.21%);
    background: linear-gradient(97deg,#ff664f 4.33%,#fa3b2d 90.21%);
    border-radius: 36rpx;
    color: #fff;
    font-size: 24rpx;
    padding: 16rpx 32rpx
}

.home .home-course-card .course-container .course-btn-time-box .btn-box .course-btn-disabled {
    background: #ffafa9
}

.home .home-course-card .course-container .course-btn-time-box .btn-box .btn-course-status-tx {
    background: -webkit-linear-gradient(315deg,#ff9456,#fa782d);
    background: linear-gradient(135deg,#ff9456,#fa782d)
}

.home .home-course-card .course-container .course-btn-time-box .btn-box .course-btn-disabled-tx {
    background: #ffc6a9
}

.home .home-course-card .course-container .preview-all {
    color: #666;
    font-size: 24rpx;
    margin-top: 54rpx;
    text-align: center
}

.home .home-course-card .course-container .preview-all__icon {
    margin-left: 10rpx
}

.home-qrcode-wrapper {
    background: -webkit-linear-gradient(295deg,#ff8d5c 13.54%,#ff5574 97.9%);
    background: linear-gradient(155deg,#ff8d5c 13.54%,#ff5574 97.9%);
    position: relative;
    width: 560rpx
}

.home-qrcode-wrapper .qrcode-head-img {
    height: 128rpx;
    left: 50%;
    position: absolute;
    top: 0rpx;
    -webkit-transform: translate(-50%);
    -ms-transform: translate(-50%);
    transform: translate(-50%);
    width: 128rpx
}

.home-qrcode-wrapper .home-qrcode-title {
    font-size: 36rpx;
    text-align: center
}

.home-qrcode-wrapper .qrcode-img-box {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    -webkit-align-items: center;
    align-items: center;
    background-color: #fff;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: center;
    justify-content: center;
    text-align: center
}

.home-qrcode-wrapper .qrcode-img-box .qrcode-img-bg {
    padding: 20rpx
}

.home-qrcode-wrapper .qrcode-img-box .qrcode-img-bg .qrcode-img {
    height: 240rpx;
    width: 240rpx
}

.wrong-tips {
    background-color: #f3f3f4;
    font-family: PingFang SC;
    margin-bottom: 36rpx;
    overflow-y: auto
}

.wrong-tips__content {
    background: #fff;
    border-radius: 28rpx;
    margin: 140rpx auto auto;
    padding-bottom: 40rpx;
    padding-top: 20rpx;
    text-align: center;
    width: 686rpx
}

.wrong-tips__img {
    height: 276rpx;
    margin-bottom: 24rpx;
    vertical-align: bottom;
    width: 276rpx
}

.wrong-tips__text {
    color: #666;
    font-size: 28rpx;
    font-weight: 400;
    margin-bottom: 48rpx;
    margin-top: 0rpx;
    text-align: center
}

.wrong-tips__btn {
    background: -webkit-linear-gradient(353deg,#ff664f 4.33%,#fa3b2d 90.21%);
    background: linear-gradient(97deg,#ff664f 4.33%,#fa3b2d 90.21%);
    border-radius: 44rpx;
    color: #fff;
    font-size: 28rpx;
    font-weight: 500;
    height: 80rpx;
    line-height: 80rpx;
    margin: 0 auto;
    text-align: center;
    width: 254rpx
}

.text-container {
    overflow: hidden;
    white-space: nowrap;
    width: 100vw
}

.text-content {
    display: inline-block;
    white-space: nowrap
}

.text {
    background-color: #fff;
    color: #ff3627;
    font-family: PingFang SC;
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-align: center;
    z-index: 999
}

.scroll-animation {
    -webkit-animation: scroll 10s linear infinite;
    animation: scroll 10s linear infinite
}

@-webkit-keyframes scroll {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    100% {
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%)
    }
}

@keyframes scroll {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    100% {
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%)
    }
}

.privacy-modal {
    background: #fff;
    border-radius: 24rpx;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
    padding: 48rpx 40rpx;
    width: 560rpx
}

.privacy-title {
    background-color: initial;
    color: #333;
    font-size: 32rpx;
    font-weight: 500;
    line-height: 44rpx;
    margin-bottom: 24rpx;
    text-align: center
}

.privacy-content {
    color: #666;
    font-size: 28rpx;
    font-weight: 400;
    line-height: 44rpx
}

.privacy-content wx-text {
    color: #ff3627
}

.privacy-button-group {
    -ms-flex-pack: justify;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin-top: 48rpx
}

.cancel-button {
    background: hsla(0,0%,60%,.1);
    border-color: transparent;
    color: #333
}

.cancel-button,.confirm-button {
    -ms-flex-pack: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    border-radius: 40rpx;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    font-size: 28rpx;
    height: 80rpx;
    -webkit-justify-content: center;
    justify-content: center;
    width: 224rpx
}

.confirm-button {
    background: -webkit-linear-gradient(135deg,#ff3627,#ff664f);
    background: linear-gradient(315deg,#ff3627,#ff664f);
    color: #fff;
    font-weight: 500
}

.h5-button:after {
    content: none
}

.h5-button::after {
    border: none
}

.activity-video .activity-list .activity-title {
    -ms-flex-align: center;
    -ms-flex-pack: justify;
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    padding: 0 32rpx 20rpx
}

.activity-video .activity-list .activity-title .title-my-activitie {
    color: #333;
    font-size: 28rpx;
    font-weight: 500
}

.activity-video .activity-list .activity-title .title-more {
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    color: #999;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    font-size: 22rpx
}

.activity-video .activity-list .activity-title .title-more .title-more-icon {
    height: 14rpx;
    margin-left: 4rpx;
    width: 8rpx
}

.activity-video .activity-list .activityCade {
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    background-color: #fff;
    border-radius: 24rpx;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin: 0 32rpx 24rpx;
    position: relative
}

.activity-video .activity-list .activityCade .activityTitle {
    background: #f3f3f3;
    border-radius: 24rpx 0rpx 24rpx 0;
    color: #999;
    font-size: 20rpx;
    height: 44rpx;
    left: 0;
    line-height: 44rpx;
    position: absolute;
    text-align: center;
    top: 0;
    width: 162rpx
}

.activity-video .activity-list .activityCade .signLine {
    background: #7cbeff;
    border-radius: 0rpx 4rpx 4rpx 0rpx;
    height: 60rpx;
    margin-right: 32rpx;
    width: 6rpx
}

.activity-video .activity-list .activityCade .activityInfo {
    padding-bottom: 48rpx;
    padding-top: 68rpx
}

.activity-video .activity-list .activityCade .activityInfo .activityName {
    color: #333;
    font-size: 30rpx;
    font-weight: 600;
    line-height: 44rpx;
    margin-bottom: 8rpx;
    padding-right: 180rpx
}

.activity-video .activity-list .activityCade .activityInfo .activityGradeName {
    color: #666;
    font-size: 28rpx;
    line-height: 40rpx
}

.activity-video .activity-list .activityCade .activityInfo .activityEndTime {
    background: #fff;
    border: 0.8rpx solid #e0e0e0;
    border-radius: 8rpx;
    color: #999;
    font-size: 24rpx;
    height: 36rpx;
    line-height: 36rpx;
    margin-top: 8rpx;
    text-align: center;
    width: 280rpx
}

.activity-video .activity-list .activityCade .typeImg {
    bottom: 40rpx;
    height: 106rpx;
    position: absolute;
    right: 40rpx;
    width: 106rpx
}

.activity-video .activity-list .activityCade .typeImg .underwayImg {
    height: 106rpx;
    width: 112rpx
}

.activity-video .activity-list .activityCade .activity-mark {
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    position: absolute;
    right: 0;
    top: 0
}

.activity-video .activity-list .activityCade .activity-mark .mark-answer {
    height: 48rpx;
    width: 116rpx
}

.activity-video .activity-list .activityCade .activity-mark .mark-sign-up {
    height: 44rpx;
    margin-right: 12rpx;
    width: 116rpx
}
