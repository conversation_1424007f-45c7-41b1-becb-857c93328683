! function() {
  "use strict";
  (wx.webpackJsonp = wx.webpackJsonp || []).push([
    [4309], {
      8802: function(e, t, n) {
        var o = n(4886),
          r = n(6234),
          a = n(1678),
          c = n.n(a),
          i = n(2784),
          u = n(3675),
          s = n(2017),
          p = n(6224),
          f = n(2322);
        Page((0, o.createPageConfig)((function() {
          var e = (0, i.useState)(""),
            t = (0, r.Z)(e, 2),
            n = t[0],
            o = t[1];
          return c().useError((function(e) {
            p.Z.error(e)
          })), (0, i.useEffect)((function() {
            var e, t = (null === (e = c().getCurrentInstance().router) || void 0 === e ? void 0 : e.params) || {},
              n = t.hidehomebutton;
            1 === Number(n) && c().hideHomeButton();
            var r = t.url || "",
              a = r && decodeURIComponent(r);
            if (c().hideShareMenu({
                menus: ["shareAppMessage"]
              }), (0, s.Z)({
                click_id: "webview_view",
                url: a
              }), "1" === t.isLogin) {
              var i = c().getStorageSync("Authorization"),
                u = "".concat("https://w.xiwang.com", "/commonapi/wechat/api/tal_token");
              o(i ? "".concat(u, "?tal_token=").concat(i, "&redirect=").concat(a) : "".concat(u, "?tal_token=").concat(i, "&clear=1&redirect=").concat(a))
            } else o(a)
          }), []), n && (0, f.jsx)(u.kh, {
            src: n
          })
        }), "pages/webView/index", {
          root: {
            cn: []
          }
        }, {
          navigationBarTitleText: "希望学·看课",
          pageOrientation: "portrait"
        } || {}))
      }
    },
    function(e) {
      e.O(0, [2107, 1216, 8592], (function() {
        return function(t) {
          return e(e.s = t)
        }(8802)
      })), e.O()
    }
  ])
}();