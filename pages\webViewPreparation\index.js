! function() {
  "use strict";
  (wx.webpackJsonp = wx.webpackJsonp || []).push([
    [6056], {
      4721: function(n, e, t) {
        var r = t(4886),
          a = t(2723),
          o = t(4795),
          c = t(6234),
          i = t(1678),
          u = t.n(i),
          s = t(2784),
          p = t(3675),
          f = t(5185),
          l = t(2322),
          w = function() {
            var n, e = (0, f.I0)(),
              t = (null === (n = u().getCurrentInstance().router) || void 0 === n ? void 0 : n.params) || {},
              r = (0, s.useRef)(!0),
              i = (0, s.useState)(""),
              w = (0, c.Z)(i, 2),
              g = w[0],
              v = w[1],
              d = function() {
                var n = (0, o.Z)((0, a.Z)().mark((function n() {
                  var e, r;
                  return (0, a.Z)().wrap((function(n) {
                    for (;;) switch (n.prev = n.next) {
                      case 0:
                        e = (t || {}).perUrl, r = "https://app.bcc.xiwang.com/h5-live/#/wx-preparation?url=".concat(decodeURIComponent(e)), v(r);
                      case 3:
                      case "end":
                        return n.stop()
                    }
                  }), n)
                })));
                return function() {
                  return n.apply(this, arguments)
                }
              }(),
              x = function() {
                var n = (0, o.Z)((0, a.Z)().mark((function n(t) {
                  return (0, a.Z)().wrap((function(n) {
                    for (;;) switch (n.prev = n.next) {
                      case 0:
                        console.log("H5PostMessage返回的数据:", t.detail), e({
                          type: "SET_ANSWER_DATA",
                          payload: t.detail
                        }), r.current = !1;
                      case 3:
                      case "end":
                        return n.stop()
                    }
                  }), n)
                })));
                return function(e) {
                  return n.apply(this, arguments)
                }
              }();
            return (0, s.useEffect)((function() {
              d()
            }), []), g && (0, l.jsx)(p.kh, {
              src: g,
              id: "myWebView",
              onMessage: x,
              onError: function() {
                console.log("webView加载失败"), u().navigateBack()
              }
            })
          };
        Page((0, r.createPageConfig)(w, "pages/webViewPreparation/index", {
          root: {
            cn: []
          }
        }, {
          navigationBarTitleText: "希望学·看课",
          pageOrientation: "landscape",
          navigationStyle: "custom"
        } || {}))
      }
    },
    function(n) {
      n.O(0, [2107, 1216], (function() {
        return function(e) {
          return n(n.s = e)
        }(4721)
      })), n.O()
    }
  ])
}();