(wx.webpackJsonp = wx.webpackJsonp || []).push([
  [1311], {
    9247: function(e, n, t) {
      "use strict";
      var c = t(4886),
        o = t(2723),
        s = t(6234),
        i = t(2290),
        r = t(2784),
        a = t(2524),
        l = t.n(a),
        u = t(8685),
        d = t(6894),
        h = t(1678),
        p = t.n(h),
        m = t(4990),
        x = t(9043),
        g = t(4748);

      function f(e, n, t) {
        var c = e;
        return c = Math.max(e, n), c = Math.min(c, t)
      }

      function v(e, n, t) {
        return e * n * t / (n + t * e)
      }

      function _(e, n, t, c) {
        var o = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : .15;
        return 0 === o ? f(e, n, t) : e < n ? -v(n - e, c, o) + n : e > t ? +v(e - t, c, o) + t : e
      }
      var w = function(e) {
          return new Promise((function(n) {
            return setTimeout(n, e)
          }))
        },
        j = Object.assign(Object.assign({}, g.C), {
          type: "default",
          pullingText: "",
          canReleaseText: "",
          refreshingText: "",
          completeText: "",
          completeDelay: 500,
          disabled: !1,
          headHeight: 50,
          threshold: 60,
          scrollTop: 0,
          onRefresh: function() {}
        }),
        y = function(e) {
          var n = "nut-pulltorefresh",
            t = (0, m.u)().locale,
            c = (0, x.u)(),
            a = Object.assign(Object.assign(Object.assign({}, j), e), {
              pullingText: e.pullingText || t.pullToRefresh.pullingText,
              canReleaseText: e.canReleaseText || t.pullToRefresh.canReleaseText,
              refreshingText: e.refreshingText || t.pullToRefresh.refreshingText,
              completeText: e.completeText || t.pullToRefresh.completeText
            }),
            h = l()(n, a.className, "".concat(n, "-").concat(a.type)),
            g = a.headHeight,
            f = a.threshold,
            v = (0, r.useRef)(!1),
            y = (0, r.useState)("pulling"),
            b = (0, s.Z)(y, 2),
            T = b[0],
            N = b[1],
            k = (0, r.useState)(0),
            I = (0, s.Z)(k, 2),
            A = I[0],
            G = I[1],
            S = "android" === (p().canIUse("getDeviceInfo") ? p().getDeviceInfo() : p().getSystemInfoSync()).platform && "WEAPP" === p().getEnv(),
            C = Object.assign({
              height: "".concat(A, "px")
            }, !v.current || S ? {
              transition: "height .3s ease"
            } : {});
          return r.createElement(u.G7, {
            className: h,
            style: a.style,
            onTouchStart: function(e) {
              a.disabled || c.start(e)
            },
            onTouchMove: function(e) {
              a.scrollTop > 0 || a.disabled || "refreshing" !== T && "complete" !== T && (c.move(e), c.isVertical() && (v.current = !0, G(Math.max(_(c.deltaY.current, 0, 0, 5 * g, .5), 0)), N(A > f ? "canRelease" : "pulling")))
            },
            onTouchEnd: function() {
              a.disabled || (v.current = !1, "canRelease" === T ? function() {
                (0, i.a)(this, void 0, void 0, (0, o.Z)().mark((function e() {
                  return (0, o.Z)().wrap((function(e) {
                    for (;;) switch (e.prev = e.next) {
                      case 0:
                        return G(g), N("refreshing"), e.prev = 2, e.next = 5, a.onRefresh();
                      case 5:
                        N("complete"), e.next = 13;
                        break;
                      case 8:
                        throw e.prev = 8, e.t0 = e.catch(2), G(0), N("pulling"), e.t0;
                      case 13:
                        if (!(a.completeDelay > 0)) {
                          e.next = 16;
                          break
                        }
                        return e.next = 16, w(a.completeDelay);
                      case 16:
                        G(0), N("pulling");
                      case 18:
                      case "end":
                        return e.stop()
                    }
                  }), e, null, [
                    [2, 8]
                  ])
                })))
              }() : (G(0), N("pulling")))
            }
          }, r.createElement(u.G7, {
            style: C,
            className: "".concat(n, "-head")
          }, r.createElement("div", {
            className: "".concat(n, "-head-content"),
            style: {
              height: g
            }
          }, r.createElement("div", null, function() {
            var e;
            return a.renderIcon ? null === (e = a.renderIcon) || void 0 === e ? void 0 : e.call(a, T) : function(e) {
              return r.createElement(r.Fragment, null, ("pulling" === e || "complete" === e) && r.createElement(d.gbz, null), ("canRelease" === e || "refreshing" === e) && r.createElement(d.Tkc, null))
            }(T)
          }()), r.createElement("div", null, function() {
            var e;
            return a.renderText ? null === (e = a.renderText) || void 0 === e ? void 0 : e.call(a, T) : "pulling" === T ? a.pullingText : "canRelease" === T ? a.canReleaseText : "refreshing" === T ? a.refreshingText : "complete" === T ? a.completeText : ""
          }()))), r.createElement("div", {
            className: "".concat(n, "-content")
          }, a.children))
        };
      y.displayName = "NutPullToRefresh";
      var b = t(3028),
        T = t(4795),
        N = t(8659),
        k = t(3675),
        I = t(2322),
        A = function(e) {
          var n = e.visible,
            t = e.onClose,
            c = e.imageUrl,
            o = e.zIndex,
            s = void 0 === o ? 999 : o,
            i = e.isTx,
            r = void 0 !== i && i;
          return n ? (0, I.jsx)(k.G7, {
            className: "qrcode-mask",
            style: {
              zIndex: s
            },
            onClick: function(e) {
              e.target === e.currentTarget && t()
            },
            children: (0, I.jsxs)(k.G7, {
              className: "qrcode-wrapper",
              children: [(0, I.jsx)(k.Ee, {
                mode: "widthFix",
                src: "https://m.xiwang.com/resource/ULX2rF08v2-A_84zHgaJN-1735021398192.png",
                className: "qrcode-wrapper__img"
              }), (0, I.jsx)(k.G7, {
                className: "qrcode-wrapper__title qrcode-text",
                children: r ? "添加班主任老师微信，高效练习" : "添加辅导老师微信，高效练习"
              }), (0, I.jsxs)(k.G7, {
                className: "image-popup",
                children: [c && (0, I.jsx)(k.Ee, {
                  "fade-in": !0,
                  className: "qrcode-img",
                  mode: "widthFix",
                  src: c,
                  "show-menu-by-longpress": !0
                }), (0, I.jsx)(k.G7, {
                  className: "qrcode-wrapper__tips qrcode-text",
                  children: "长按添加微信"
                })]
              }), (0, I.jsx)(k.Ee, {
                className: "qrcode-wrapper__logo",
                mode: "widthFix",
                src: "https://m.xiwang.com/resource/8S_5AQJwU37gFLUyzxveK-*************.png"
              }), (0, I.jsx)(k.Ee, {
                mode: "widthFix",
                className: "qrcode-wrapper__close",
                src: "https://m.xiwang.com/resource/VOTO9FW284oQT9TzvGVqm-*************.svg",
                onClick: t
              })]
            })
          }) : null
        },
        G = t(5185),
        S = t(7048),
        C = t(2017),
        Z = t(6224),
        R = t(2725),
        E = t(3517),
        z = (t(8762), p().getAccountInfoSync().miniProgram.envVersion),
        F = "";
      F = "release" === z ? "https://w.xiwang.com" : "https://wgray.xue.xiwang.com";
      var U = function(e) {
          var n = e.imgUrl,
            t = e.btnText,
            c = e.tipsText,
            o = e.onClick,
            s = void 0 === o ? function() {} : o;
          return (0, I.jsx)(k.G7, {
            className: "wrong-tips",
            children: (0, I.jsxs)(k.G7, {
              className: "wrong-tips__content",
              children: [(0, I.jsx)(k.Ee, {
                src: n,
                mode: "widthFix",
                className: "wrong-tips__img"
              }), (0, I.jsx)(k.G7, {
                className: "wrong-tips__text",
                children: c
              }), t && (0, I.jsx)(k.G7, {
                onClick: s,
                className: "wrong-tips__btn",
                children: t
              })]
            })
          })
        },
        M = function(e) {
          var n = e.text,
            t = (0, r.useRef)(null),
            c = (0, r.useState)(!1),
            o = (0, s.Z)(c, 2),
            i = o[0],
            a = o[1];
          (0, r.useEffect)((function() {
            if (t.current) {
              var e = p().getSystemInfoSync().windowWidth;
              console.log("[ containerWidth ]-16", e);
              var c = l(n);
              console.log("[ textWidth ]-17", c), a(c >= e)
            }
          }), [n]);
          var l = function(e) {
            return 10 * e.length
          };
          return (0, I.jsx)(k.G7, {
            ref: t,
            className: "text-container",
            children: (0, I.jsx)(k.G7, {
              className: "text-content ".concat(i ? "scroll-animation" : ""),
              children: (0, I.jsx)(k.G7, {
                className: "text",
                children: n
              })
            })
          })
        },
        P = (t(4289), t(4309)),
        B = function(e) {
          var n = e.visible,
            t = e.onRefuse,
            c = e.onConfirm,
            o = function(e, n) {
              e.stopPropagation(), p().navigateTo({
                url: "/pages/webView/index?url=".concat(encodeURIComponent(n))
              })
            };
          return (0, I.jsxs)(P.P, {
            visible: n,
            closeOnOverlayClick: !1,
            className: "privacy-modal",
            round: !0,
            children: [(0, I.jsx)(k.G7, {
              className: "privacy-title",
              children: "用户隐私保护指引"
            }), (0, I.jsxs)(k.G7, {
              className: "privacy-content",
              children: [(0, I.jsxs)(k.G7, {
                children: ["感谢您信任并使用【希望学练习】。我们将通过", (0, I.jsx)(k.xv, {
                  onClick: function(e) {
                    return o(e, "https://xue.xiwang.com/touch-protocol?app_blid=30#/register")
                  },
                  children: "《希望学用户协议》"
                }), (0, I.jsx)(k.xv, {
                  onClick: function(e) {
                    return o(e, "https://xue.xiwang.com/touch-protocol?app_blid=30#/privacy")
                  },
                  children: "《用户个人信息保护政策》"
                }), (0, I.jsx)(k.xv, {
                  onClick: function(e) {
                    return o(e, "https://xue.xiwang.com/touch-protocol?app_blid=30#/children")
                  },
                  children: "《儿童个人信息保护规则》"
                }), "帮助您了解我们收集、使用和共享个人信息的情况。"]
              }), (0, I.jsxs)(k.G7, {
                style: {
                  paddingTop: "4px"
                },
                children: ["未经您的同意，我们不会主动向任何第三方共享您的个人信息。当您使用一些功能服务时，我们可能会在获得您的明示同意后，从授权第三方处获取、共享或向其提供信息。请仔细阅读我们的", (0, I.jsx)(k.xv, {
                  onClick: function(e) {
                    return o(e, "https://xue.xiwang.com/touch-protocol?app_blid=30#/register")
                  },
                  children: "《希望学用户协议》"
                }), (0, I.jsx)(k.xv, {
                  onClick: function(e) {
                    return o(e, "https://xue.xiwang.com/touch-protocol?app_blid=30#/privacy")
                  },
                  children: "《用户个人信息保护政策》"
                }), (0, I.jsx)(k.xv, {
                  onClick: function(e) {
                    return o(e, "https://xue.xiwang.com/touch-protocol?app_blid=30#/children")
                  },
                  children: "《儿童个人信息保护规则》"
                }), "，如您同意本政策内容，请点击“同意”开始使用我们的产品与服务，我们尽全力保护您的个人信息。"]
              })]
            }), (0, I.jsxs)(k.G7, {
              className: "privacy-button-group",
              children: [(0, I.jsx)(k.zx, {
                className: "cancel-button",
                onClick: function() {
                  p().exitMiniProgram(), t && t()
                },
                children: "不同意"
              }), (0, I.jsx)(k.zx, {
                id: "agree-btn",
                className: "confirm-button",
                "open-type": "agreePrivacyAuthorization",
                onAgreePrivacyAuthorization: function() {
                  c && c()
                },
                children: "同意"
              })]
            })]
          })
        },
        O = t(3494),
        L = t(4557),
        V = t(524),
        q = t(226),
        D = t(8585),
        H = t(2451),
        Q = t(2081),
        K = t(4302),
        W = p().getAccountInfoSync().miniProgram.envVersion,
        Y = function(e) {
          var n = e.activityList,
            t = {
              1: "素质活动",
              2: "趣味运算",
              3: "数独活动",
              4: "语音朗读",
              5: "口算练习",
              7: "数字华容道",
              8: "七巧板",
              10: "赛前练习",
              11: "赛事评比",
              12: "ai作文批改",
              18: "闯关活动",
              19: "校园评测"
            },
            c = (0, r.useMemo)((function() {
              var e = n[0];
              return !e || 1 != e.status && 0 != e.status ? [] : [e]
            }), [n]),
            o = function(e) {
              var n = e.status;
              return 0 === n ? (0, I.jsx)(k.Ee, {
                src: V,
                className: "underwayImg"
              }) : 1 === n ? (0, I.jsx)(k.Ee, {
                src: L,
                className: "underwayImg"
              }) : 2 === n ? (0, I.jsx)(k.Ee, {
                src: q,
                className: "underwayImg"
              }) : void 0
            };
          return (0, I.jsxs)(k.G7, {
            className: "activity-video",
            children: [c.length ? (0, I.jsxs)(k.G7, {
              className: "activity-list",
              children: [(0, I.jsxs)(k.G7, {
                className: "activity-title",
                children: [(0, I.jsx)(k.G7, {
                  className: "title-my-activitie",
                  children: "我的活动"
                }), (0, I.jsxs)(k.G7, {
                  className: "title-more",
                  onClick: function() {
                    p().navigateToMiniProgram({
                      appId: "wx1efe4e9d2c6377cb",
                      path: "/pages/activity/index?authorization=".concat(p().getStorageSync("Authorization")),
                      envVersion: "release" === W ? "release" : "trial"
                    })
                  },
                  children: [(0, I.jsx)(k.G7, {
                    children: "查看更多"
                  }), (0, I.jsx)(k.Ee, {
                    className: "title-more-icon",
                    src: D
                  })]
                })]
              }), c.map((function(e) {
                var n = e.name,
                  c = e.end_time,
                  s = e.type,
                  i = e.grade,
                  r = e.subject_text,
                  a = e.status,
                  l = e.label_status,
                  u = e.answer_status;
                return (0, I.jsxs)(k.G7, {
                  className: "activityCade",
                  style: {
                    backgroundColor: 2 === a ? "#F5F5F5" : "#FFF"
                  },
                  onClick: function() {
                    return function(e) {
                      p().navigateToMiniProgram({
                        appId: "wx1efe4e9d2c6377cb",
                        path: "/pages/activity/index?authorization=".concat(p().getStorageSync("Authorization"), "&url=").concat(encodeURIComponent(e.url), "&jumpType=jumpActivity&status=").concat(e.status, "&start_time=").concat(e.start_time, "&type=").concat(e.type),
                        envVersion: "release" === W ? "release" : "trial"
                      })
                    }(e)
                  },
                  children: [(0, I.jsx)(k.G7, {
                    className: "activityTitle",
                    children: t[s]
                  }), (0, I.jsx)(k.G7, {
                    className: "signLine"
                  }), (0, I.jsxs)(k.G7, {
                    className: "activityInfo",
                    children: [(0, I.jsx)(k.G7, {
                      className: "activityName",
                      children: n
                    }), (0, I.jsxs)(k.G7, {
                      className: "activityGradeName",
                      children: ["参赛年级：", i]
                    }), 11 != s && 12 != s && (0, I.jsxs)(k.G7, {
                      className: "activityGradeName",
                      children: ["学科：", r]
                    }), (0, I.jsx)(k.G7, {
                      className: "activityEndTime",
                      children: c
                    })]
                  }), (0, I.jsx)(k.G7, {
                    className: "typeImg",
                    children: o(e)
                  }), (0, I.jsxs)(k.G7, {
                    className: "activity-mark",
                    children: [1 == l && (0, I.jsx)(k.Ee, {
                      src: 1 == u && 2 != a ? Q : K,
                      className: "".concat(1 == u ? "mark-sign-up" : "mark-answer")
                    }), 1 == u && 2 != a && (0, I.jsx)(k.Ee, {
                      src: H,
                      className: "mark-answer"
                    })]
                  })]
                }, e.id)
              }))]
            }) : null, (0, I.jsx)(k.G7, {
              className: "video-list"
            })]
          })
        },
        J = t(4886).document,
        X = "https://app.bcc.xiwang.com/",
        $ = function() {
          var e, n = (0, G.I0)(),
            t = (0, r.useState)(!0),
            c = (0, s.Z)(t, 2),
            i = c[0],
            a = c[1],
            u = (0, r.useState)(!0),
            h = (0, s.Z)(u, 2),
            m = h[0],
            x = h[1],
            g = (0, r.useState)(""),
            f = (0, s.Z)(g, 2),
            v = f[0],
            _ = f[1],
            w = (0, r.useState)(!1),
            j = (0, s.Z)(w, 2),
            z = j[0],
            P = j[1],
            L = (0, r.useState)(null),
            V = (0, s.Z)(L, 2),
            q = V[0],
            D = V[1],
            H = (0, r.useState)(!1),
            Q = (0, s.Z)(H, 2),
            K = Q[0],
            W = Q[1],
            $ = (0, r.useState)(!1),
            ee = (0, s.Z)($, 2),
            ne = ee[0],
            te = ee[1],
            ce = (null === (e = p().getCurrentInstance().router) || void 0 === e ? void 0 : e.params) || {},
            oe = (0, G.v9)((function(e) {
              return e.user.userInfo
            })),
            se = (0, r.useState)(!1),
            ie = (0, s.Z)(se, 2),
            re = ie[0],
            ae = ie[1],
            le = (0, r.useState)([]),
            ue = (0, s.Z)(le, 2),
            de = ue[0],
            he = ue[1];
          (0, r.useEffect)((function() {
            J.title = "希望学·看课"
          }), []), p().useError((function(e) {
            Z.Z.error(e)
          })), p().useLoad((function() {
            ! function() {
              var e = p().getAppBaseInfo(); - 1 === (0, O.yC)("2.32.2", e.SDKVersion || "0") ? p().getPrivacySetting({
                success: function(e) {
                  null != e && e.needAuthorization && ae(!0)
                }
              }) : p().showModal({
                content: "您的客户端版本过低，暂不支持用户隐私协议授权",
                confirmText: "确定",
                showCancel: !1
              })
            }()
          }));
          var pe = function() {
            var e = (0, T.Z)((0, o.Z)().mark((function e() {
              var n;
              return (0, o.Z)().wrap((function(e) {
                for (;;) switch (e.prev = e.next) {
                  case 0:
                    return e.next = 2, (0, N.FD)({});
                  case 2:
                    n = e.sent, he(n.data || []);
                  case 4:
                  case "end":
                    return e.stop()
                }
              }), e)
            })));
            return function() {
              return e.apply(this, arguments)
            }
          }();
          p().useDidShow((0, T.Z)((0, o.Z)().mark((function e() {
            return (0, o.Z)().wrap((function(e) {
              for (;;) switch (e.prev = e.next) {
                case 0:
                  Z.Z.pageShow(), p().showLoading({
                    title: "加载中"
                  }), (0, C.Z)({
                    click_id: "home_page_show"
                  }), (0, R.TZ)().then((function() {
                    n((0, S.c6)()).then((function(e) {
                      p().hideLoading(), 1009 === e.code ? x(!1) : (x(!0), p().getStorageSync("oauth_open_id") && p().login({
                        success: function() {
                          var e = (0, T.Z)((0, o.Z)().mark((function e(n) {
                            var t, c;
                            return (0, o.Z)().wrap((function(e) {
                              for (;;) switch (e.prev = e.next) {
                                case 0:
                                  return t = n.code, e.next = 3, (0, N.AI)({
                                    code: t,
                                    type: 2,
                                    origin: 2,
                                    appid: "wxd2606008715f7ba5"
                                  });
                                case 3:
                                  c = e.sent, (0, C.Z)({
                                    click_id: "wxBind_info",
                                    openInfo: c
                                  });
                                case 5:
                                case "end":
                                  return e.stop()
                              }
                            }), e)
                          })));
                          return function(n) {
                            return e.apply(this, arguments)
                          }
                        }()
                      })), 0 === e.code && (me(), je(), pe())
                    })).catch((function(e) {
                      p().hideLoading(), x(!1), (0, C.Z)({
                        click_id: "catch_getUserInfo_error",
                        err: e
                      })
                    }))
                  }));
                case 4:
                case "end":
                  return e.stop()
              }
            }), e)
          })))), p().useDidHide((function() {
            Z.Z.pageHide()
          })), p().useShareAppMessage((function(e) {
            return {
              title: "立即登录，来希望学学习啦",
              path: "/pages/home/<USER>",
              imageUrl: "https://m.xiwang.com/resources/share--Mf7sIpBbLZiPDIedZszz.png"
            }
          }));
          var me = function() {
              var e = p().getStorageSync("stuId");
              e && p().login().then((function(n) {
                return p().request({
                  url: "".concat(F, "/school/mini_program/bind_user_info"),
                  data: {
                    code: n.code,
                    user_id: e
                  }
                })
              })).then((function() {
                _e()
              })).catch((function(e) {
                (0, C.Z)({
                  click_id: "catch_notice_error",
                  err: e
                })
              })).finally((function() {}))
            },
            xe = (0, r.useState)([]),
            ge = (0, s.Z)(xe, 2),
            fe = ge[0],
            ve = ge[1],
            _e = function() {
              (0, N.t4)().then((function(e) {
                if (0 === e.code) {
                  var n = e.data.data;
                  ve(n.map((function(e) {
                    return e.priTmplId
                  })))
                } else(0, C.Z)({
                  click_id: "catch_no0_error",
                  res: e
                })
              })).catch((function(e) {
                (0, C.Z)({
                  click_id: "catch_notice_error",
                  err: e
                })
              }))
            },
            we = function() {
              p().requestSubscribeMessage({
                tmplIds: fe,
                success: function(e) {
                  delete e.errMsg, Object.values(e).every((function(e) {
                    return "accept" === e
                  })) ? (p().nextTick((function() {
                    p().showToast({
                      title: "订阅成功"
                    })
                  })), (0, C.Z)({
                    click_id: "notice_success"
                  }), je()) : p().showModal({
                    content: "存在已拒绝的消息通知，是否前往通知管理页面开启消息通知？",
                    confirmText: "开启通知",
                    showCancel: !0,
                    success: function(e) {
                      e.confirm && p().openSetting({
                        withSubscriptions: !0
                      }).then((function(e) {
                        console.log("subres:", e)
                      }))
                    }
                  })
                },
                fail: function(e) {
                  p().showToast({
                    title: E.fQ[e.errCode],
                    icon: "none"
                  }), (0, C.Z)({
                    click_id: "notice_error_requestSubscribeMessage",
                    err: e
                  }), setTimeout((function() {
                    p().reLaunch({
                      url: "/pages/home/<USER>"
                    })
                  }), 1500)
                }
              })
            },
            je = function() {
              var e = (0, T.Z)((0, o.Z)().mark((function e() {
                return (0, o.Z)().wrap((function(e) {
                  for (;;) switch (e.prev = e.next) {
                    case 0:
                      a(!0), p().showLoading({
                        title: "加载中"
                      }), (0, N.zf)().then((function(e) {
                        if (0 === e.code && e.data) {
                          var n, t;
                          D(e.data), (0, C.Z)({
                            click_id: "view_78_06_01_01"
                          }), p().setStorageSync("gradeId", (null == e || null === (n = e.data) || void 0 === n ? void 0 : n.gradeId) || 0);
                          var c = 1 === (null == e || null === (t = e.data) || void 0 === t ? void 0 : t.is_tx);
                          if (console.log("is_tx", c), Ne(c), "baoming" === ce.type) {
                            (0, C.Z)({
                              click_id: "click_baoming"
                            });
                            var o = ce.course_id;
                            o || (0, C.Z)({
                              click_id: "click_no_txcourse_id"
                            }), ke({
                              isTx: 1,
                              course_id: o
                            })
                          }
                        } else D(null)
                      })).catch((function() {
                        p().showToast({
                          title: "课程信息获取失败，请稍后重试！",
                          icon: "none"
                        })
                      })).finally((function() {
                        a(!1), p().hideLoading()
                      }));
                    case 3:
                    case "end":
                      return e.stop()
                  }
                }), e)
              })));
              return function() {
                return e.apply(this, arguments)
              }
            }(),
            ye = (0, r.useState)(!1),
            be = (0, s.Z)(ye, 2),
            Te = be[0],
            Ne = be[1],
            ke = function() {
              var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
              W(!0);
              var n = (0, b.Z)({
                user_id: null == oe ? void 0 : oe.user_id,
                course_id: null == q ? void 0 : q.courseId
              }, e);
              (0, N.KQ)(n).then((function(n) {
                if (0 === n.code && n.data) {
                  var t = n.data;
                  _(t), P(!0), 1 === e.isTx && (ce.type = ""), Ne(1 === e.isTx)
                } else _(""), P(!1), p().showToast({
                  title: "二维码获取失败，请稍后重试！",
                  icon: "none"
                });
                W(!1)
              })).catch((function() {
                _(""), P(!1), p().showToast({
                  title: "二维码获取失败，请稍后重试！",
                  icon: "none"
                }), W(!1)
              }))
            },
            Ie = function() {
              p().navigateTo({
                url: "/pages/login/index?backPage=".concat(encodeURIComponent("/pages/home/<USER>"))
              })
            },
            Ae = function(e) {
              p().navigateTo({
                url: "/pages/webView/index?url=".concat(encodeURIComponent(e))
              })
            };
          return (0, I.jsxs)(k.G7, {
            className: "home-container",
            children: [!m && (0, I.jsxs)(k.G7, {
              children: [(0, I.jsx)(k.G7, {
                className: "home-no-login",
                onClick: function() {
                  (0, C.Z)({
                    click_id: "click_78_14_01_01"
                  }), p().navigateTo({
                    url: "/pages/webView/index?url=".concat(encodeURIComponent("https://m.xiwang.com/657c195c1458ab675df98485.html"))
                  })
                },
                children: (0, I.jsx)(k.Ee, {
                  mode: "widthFix",
                  className: "home-no-login__banner",
                  src: "https://m.xiwang.com/resource/sbU7kPaAktR5FRv6i42jq-1735265830583.jpg"
                })
              }), (0, I.jsxs)(k.G7, {
                className: "home-card-yindao",
                children: [(0, I.jsx)(k.Ee, {
                  className: "home-card-yindao__img",
                  mode: "widthFix",
                  src: "https://m.xiwang.com/resource/6reA5k9h98lWpxRzTA2Lj-1755686927427.png"
                }), (0, I.jsx)(k.Ee, {
                  className: "home-card-yindao__btn",
                  mode: "heightFix",
                  src: "https://m.xiwang.com/resource/uM-Db6dqoE-wL3cQkYYR5-1755686927430.png",
                  onClick: Ie
                }), (0, I.jsx)(k.G7, {
                  className: "detail_mask",
                  onClick: function() {
                    p().navigateTo({
                      url: "".concat(E.V6, "?url=").concat(encodeURIComponent("https://m.xiwang.com/655defb0719d12715d6a7f07.html"))
                    })
                  }
                })]
              })]
            }), m && (0, I.jsxs)(I.Fragment, {
              children: [(0, I.jsx)(y, {
                style: {
                  color: "#666"
                },
                onRefresh: (0, T.Z)((0, o.Z)().mark((function e() {
                  return (0, o.Z)().wrap((function(e) {
                    for (;;) switch (e.prev = e.next) {
                      case 0:
                        je();
                      case 1:
                      case "end":
                        return e.stop()
                    }
                  }), e)
                }))),
                children: (null == q ? void 0 : q.courseId) && (0, I.jsxs)(k.G7, {
                  className: "home",
                  children: [(0, I.jsx)(k.G7, {
                    className: "home-scoll-box",
                    children: (null == q ? void 0 : q.popTimeMsg) && (0, I.jsxs)(k.G7, {
                      className: "home-scoll-wrapper",
                      children: [(0, I.jsx)(k.G7, {
                        className: "time-icon"
                      }), (0, I.jsx)(M, {
                        text: "最近上课时间：".concat(q.popTimeMsg)
                      })]
                    })
                  }), (0, I.jsxs)(k.G7, {
                    className: "home-course-card",
                    children: [(0, I.jsxs)(k.G7, {
                      className: l()("course-card-top", {
                        "course-card-top-tx": Te
                      }),
                      children: [(0, I.jsxs)(k.G7, {
                        className: "home-top-con home-top-left",
                        onClick: function() {
                          te(!0), (0, N.ty)({
                            course_id: null == q ? void 0 : q.courseId
                          }).then((function(e) {
                            if (te(!1), 0 === e.code) {
                              var n = e.data || {},
                                t = n.token,
                                c = void 0 === t ? "" : t,
                                o = n.exam_id,
                                s = void 0 === o ? "" : o,
                                i = n.course_id;
                              if (!s) return void p().showToast({
                                title: "暂无入门测",
                                icon: "none"
                              });
                              var r = "".concat("https://xueyan.bcc.xiwang.com/", "standardExam/index.html#/examIntro"),
                                a = "".concat(r, "?courseId=").concat(i, "&examId=").concat(s, "&token=").concat(c);
                              p().navigateTo({
                                url: "/pages/webView/index?url=".concat(encodeURIComponent(a), "&isLogin=1")
                              })
                            } else p().showToast({
                              title: (null == e ? void 0 : e.msg) || "好像出了点问题，请稍后重试！",
                              icon: "none"
                            })
                          })).catch((function() {
                            te(!1)
                          }))
                        },
                        children: [(0, I.jsx)(k.Ee, {
                          mode: "widthFix",
                          src: "https://m.xiwang.com/resource/N_rmgscDGmlsmLxaEGGls-1735028732016.svg",
                          className: "home-top-icon"
                        }), ne ? (0, I.jsx)(d.gbz, {}) : (0, I.jsx)(k.xv, {
                          className: "home-top-text",
                          children: "入门测"
                        })]
                      }), (0, I.jsxs)(k.G7, {
                        className: "home-top-con home-top-right",
                        onClick: function() {
                          ke({
                            isTx: q.is_tx
                          })
                        },
                        children: [(0, I.jsx)(k.Ee, {
                          mode: "widthFix",
                          src: "https://m.xiwang.com/resource/Qn-l0f-taLrp42rSS4tXz-1735028687813.svg",
                          className: "home-top-icon"
                        }), K ? (0, I.jsx)(d.gbz, {}) : (0, I.jsx)(k.xv, {
                          className: "home-top-text",
                          children: Te ? "联系班主任" : "联系辅导老师"
                        })]
                      })]
                    }), (0, I.jsxs)(k.G7, {
                      className: "course-container",
                      children: [(0, I.jsxs)(k.G7, {
                        className: "course-title",
                        children: [q.subjectName && (0, I.jsx)(k.G7, {
                          className: "course-subject-name",
                          children: q.subjectName
                        }), (0, I.jsx)(k.G7, {
                          className: "course-name",
                          children: null == q ? void 0 : q.courseName
                        })]
                      }), (0, I.jsxs)(k.G7, {
                        className: "course-info-text",
                        children: [null == q ? void 0 : q.planNum, " ", null == q ? void 0 : q.planName]
                      }), (0, I.jsxs)(k.G7, {
                        className: "course-btn-time-box",
                        children: [(0, I.jsxs)(k.G7, {
                          className: "course-time",
                          children: [(0, I.jsx)(k.G7, {
                            style: {
                              marginRight: "4px"
                            },
                            children: q.day
                          }), (0, I.jsxs)(k.G7, {
                            children: [q.startTime, "-"]
                          }), (0, I.jsxs)(k.G7, {
                            children: [q.endTime, " 上课"]
                          })]
                        }), (0, I.jsx)(k.G7, {
                          className: "btn-box",
                          children: (0, I.jsx)(k.G7, {
                            className: l()("btn-course-status", {
                              "btn-course-status-tx": Te,
                              "course-btn-disabled": 1 === q.is_show,
                              "course-btn-disabled-tx": 1 === q.is_show && Te
                            }),
                            onClick: function() {
                              (0, C.Z)({
                                click_id: "click_78_06_03_01"
                              }),
                              function() {
                                var e = q || {},
                                  n = e.stuId,
                                  t = e.stuCouId,
                                  c = e.planId,
                                  o = e.courseId,
                                  s = e.catalog,
                                  i = e.planNum,
                                  r = e.is_all_order,
                                  a = e.show_button_text,
                                  l = e.is_show;
                                if (1 === e.is_tx && ("去上课" === a ? (0, C.Z)({
                                    click_id: "kanke_tx_go_class"
                                  }) : "看回放" === a && (0, C.Z)({
                                    click_id: "kanke_tx_go_playback"
                                  })), "第1讲" === i && "激活课程" === a)(0, C.Z)({
                                  click_id: "home_activate_course",
                                  data: {
                                    show_button_text: a,
                                    stuId: n,
                                    stuCouId: t,
                                    planId: c,
                                    courseId: o
                                  }
                                }), (0, N.SA)({
                                  stu_cou_id: +t,
                                  plan_id: +c,
                                  catalog_id: +s
                                }), 1 === r ? p().showToast({
                                  title: "您已成功激活课程，请留意辅导老师通知或服务通知",
                                  icon: "none"
                                }) : 0 === r && we();
                                else if ("预约上课" === a)(0, C.Z)({
                                  click_id: "click_78_11_04_01",
                                  data: {
                                    is_all_order: r,
                                    show_button_text: a,
                                    is_show: l,
                                    stuId: n,
                                    stuCouId: t,
                                    planId: c,
                                    courseId: o,
                                    catalog: s
                                  }
                                }), (0, N.SA)({
                                  stu_cou_id: +t,
                                  plan_id: +c,
                                  catalog_id: +s
                                }), 1 === r ? p().showToast({
                                  title: "您已成功预约上课，请留意辅导老师通知或服务通知",
                                  icon: "none"
                                }) : 0 === r && we();
                                else {
                                  if (1 === l) return;
                                  (0, C.Z)({
                                    click_id: "click_kanke_go_class"
                                  });
                                  var u = "".concat(X, "h5-live/live-main/index.html?bizId=3&stuCouId=").concat(t, "&classId=-1&fromType=3&app_blid=30&planId=").concat(c, "&from=miniapp");
                                  p().navigateTo({
                                    url: "/pages/landScape/index?url=".concat(encodeURIComponent(u))
                                  })
                                }
                              }()
                            },
                            children: q.show_button_text
                          })
                        })]
                      }), (0, I.jsx)(k.G7, {
                        className: "preview-all",
                        children: (0, I.jsxs)(k.G7, {
                          onClick: function() {
                            (0, C.Z)({
                              click_id: "click_78_06_04_01"
                            }),
                            function() {
                              var e = q || {},
                                n = e.courseId,
                                t = e.stuCouId,
                                c = e.type,
                                o = "".concat(X, "learn/#/plans?courseId=").concat(n, "&stuCouId=").concat(t, "&type=").concat(c);
                              p().navigateTo({
                                url: "/pages/webView/index?url=".concat(encodeURIComponent(o), "&isLogin=1")
                              })
                            }()
                          },
                          children: ["查看全部课节或回放", (0, I.jsx)(d.olP, {
                            className: "preview-all__icon",
                            size: "10px"
                          })]
                        })
                      })]
                    })]
                  }), (0, I.jsx)(A, {
                    isTx: Te,
                    visible: z,
                    onClose: function() {
                      P(!1), Te || Ne(!1)
                    },
                    imageUrl: v
                  })]
                })
              }), !i && !(null != q && q.courseId) && (0, I.jsx)(U, {
                btnText: "切换账号",
                tipsText: "当前无上课课程，请切换账号试试～",
                onClick: function() {
                  if ((0, C.Z)({
                      click_id: "click_78_10_01_01"
                    }), p().getStorageSync("oauth_open_id")) {
                    var e = {
                      client_id: "514255",
                      bind_key: "wxd2606008715f7ba5",
                      openid: p().getStorageSync("oauth_open_id")
                    };
                    (0, N.k4)(e).then((function(e) {
                      0 === e.code && (0, R.rB)(p().getStorageSync("Authorization")).then((function() {
                        Ie()
                      }))
                    }))
                  } else(0, R.rB)(p().getStorageSync("Authorization")).then((function() {
                    Ie()
                  }))
                },
                imgUrl: "https://m.xiwang.com/resource/G2ypUnkss_dn0bc7A37hW-1735031965911.png"
              })]
            }), m && (0, I.jsx)(Y, {
              activityList: de
            }), (0, I.jsx)(k.G7, {
              className: "protocol_privacy",
              children: (0, I.jsxs)(k.G7, {
                className: "protocol__container",
                children: [(0, I.jsx)(k.xv, {
                  children: "我已阅读并同意"
                }), (0, I.jsx)(k.xv, {
                  className: "protocol__text",
                  onClick: function() {
                    return Ae("https://xue.xiwang.com/touch-protocol?app_blid=30#/register")
                  },
                  children: "《希望学用户协议》"
                }), (0, I.jsx)(k.xv, {
                  className: "protocol__text",
                  onClick: function() {
                    return Ae("https://xue.xiwang.com/touch-protocol?app_blid=30#/privacy")
                  },
                  children: "《用户个人信息保护政策》"
                }), (0, I.jsx)(k.xv, {
                  className: "protocol__text",
                  onClick: function() {
                    return Ae("https://xue.xiwang.com/touch-protocol?app_blid=30#/children")
                  },
                  children: "《儿童个人信息保护规则》"
                })]
              })
            }), (0, I.jsx)(B, {
              visible: re,
              onRefuse: function() {
                ae(!1), p().exitMiniProgram()
              },
              onConfirm: function() {
                ae(!1)
              }
            })]
          })
        };
      $.enableShareAppMessage = !0, Page((0, c.createPageConfig)($, "pages/home/<USER>", {
        root: {
          cn: []
        }
      }, {
        navigationBarTitleText: "希望学·看课",
        backgroundColorTop: "#fff",
        pageOrientation: "portrait",
        enableShareAppMessage: !0,
        enablePullDownRefresh: !1
      } || {}))
    },
    5792: function(e) {
      var n = {
        utf8: {
          stringToBytes: function(e) {
            return n.bin.stringToBytes(unescape(encodeURIComponent(e)))
          },
          bytesToString: function(e) {
            return decodeURIComponent(escape(n.bin.bytesToString(e)))
          }
        },
        bin: {
          stringToBytes: function(e) {
            for (var n = [], t = 0; t < e.length; t++) n.push(255 & e.charCodeAt(t));
            return n
          },
          bytesToString: function(e) {
            for (var n = [], t = 0; t < e.length; t++) n.push(String.fromCharCode(e[t]));
            return n.join("")
          }
        }
      };
      e.exports = n
    },
    9562: function(e) {
      var n, t;
      n = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", t = {
        rotl: function(e, n) {
          return e << n | e >>> 32 - n
        },
        rotr: function(e, n) {
          return e << 32 - n | e >>> n
        },
        endian: function(e) {
          if (e.constructor == Number) return 16711935 & t.rotl(e, 8) | 4278255360 & t.rotl(e, 24);
          for (var n = 0; n < e.length; n++) e[n] = t.endian(e[n]);
          return e
        },
        randomBytes: function(e) {
          for (var n = []; e > 0; e--) n.push(Math.floor(256 * Math.random()));
          return n
        },
        bytesToWords: function(e) {
          for (var n = [], t = 0, c = 0; t < e.length; t++, c += 8) n[c >>> 5] |= e[t] << 24 - c % 32;
          return n
        },
        wordsToBytes: function(e) {
          for (var n = [], t = 0; t < 32 * e.length; t += 8) n.push(e[t >>> 5] >>> 24 - t % 32 & 255);
          return n
        },
        bytesToHex: function(e) {
          for (var n = [], t = 0; t < e.length; t++) n.push((e[t] >>> 4).toString(16)), n.push((15 & e[t]).toString(16));
          return n.join("")
        },
        hexToBytes: function(e) {
          for (var n = [], t = 0; t < e.length; t += 2) n.push(parseInt(e.substr(t, 2), 16));
          return n
        },
        bytesToBase64: function(e) {
          for (var t = [], c = 0; c < e.length; c += 3)
            for (var o = e[c] << 16 | e[c + 1] << 8 | e[c + 2], s = 0; s < 4; s++) 8 * c + 6 * s <= 8 * e.length ? t.push(n.charAt(o >>> 6 * (3 - s) & 63)) : t.push("=");
          return t.join("")
        },
        base64ToBytes: function(e) {
          e = e.replace(/[^A-Z0-9+\/]/gi, "");
          for (var t = [], c = 0, o = 0; c < e.length; o = ++c % 4) 0 != o && t.push((n.indexOf(e.charAt(c - 1)) & Math.pow(2, -2 * o + 8) - 1) << 2 * o | n.indexOf(e.charAt(c)) >>> 6 - 2 * o);
          return t
        }
      }, e.exports = t
    },
    3335: function(e) {
      function n(e) {
        return !!e.constructor && "function" == typeof e.constructor.isBuffer && e.constructor.isBuffer(e)
      }
      e.exports = function(e) {
        return null != e && (n(e) || function(e) {
          return "function" == typeof e.readFloatLE && "function" == typeof e.slice && n(e.slice(0, 0))
        }(e) || !!e._isBuffer)
      }
    },
    8762: function(e, n, t) {
      ! function() {
        var n = t(9562),
          c = t(5792).utf8,
          o = t(3335),
          s = t(5792).bin,
          i = function(e, t) {
            e.constructor == String ? e = t && "binary" === t.encoding ? s.stringToBytes(e) : c.stringToBytes(e) : o(e) ? e = Array.prototype.slice.call(e, 0) : Array.isArray(e) || e.constructor === Uint8Array || (e = e.toString());
            for (var r = n.bytesToWords(e), a = 8 * e.length, l = 1732584193, u = -271733879, d = -1732584194, h = 271733878, p = 0; p < r.length; p++) r[p] = 16711935 & (r[p] << 8 | r[p] >>> 24) | 4278255360 & (r[p] << 24 | r[p] >>> 8);
            r[a >>> 5] |= 128 << a % 32, r[14 + (a + 64 >>> 9 << 4)] = a;
            var m = i._ff,
              x = i._gg,
              g = i._hh,
              f = i._ii;
            for (p = 0; p < r.length; p += 16) {
              var v = l,
                _ = u,
                w = d,
                j = h;
              l = m(l, u, d, h, r[p + 0], 7, -680876936), h = m(h, l, u, d, r[p + 1], 12, -389564586), d = m(d, h, l, u, r[p + 2], 17, 606105819), u = m(u, d, h, l, r[p + 3], 22, -1044525330), l = m(l, u, d, h, r[p + 4], 7, -176418897), h = m(h, l, u, d, r[p + 5], 12, 1200080426), d = m(d, h, l, u, r[p + 6], 17, -1473231341), u = m(u, d, h, l, r[p + 7], 22, -45705983), l = m(l, u, d, h, r[p + 8], 7, 1770035416), h = m(h, l, u, d, r[p + 9], 12, -1958414417), d = m(d, h, l, u, r[p + 10], 17, -42063), u = m(u, d, h, l, r[p + 11], 22, -1990404162), l = m(l, u, d, h, r[p + 12], 7, 1804603682), h = m(h, l, u, d, r[p + 13], 12, -40341101), d = m(d, h, l, u, r[p + 14], 17, -1502002290), l = x(l, u = m(u, d, h, l, r[p + 15], 22, 1236535329), d, h, r[p + 1], 5, -165796510), h = x(h, l, u, d, r[p + 6], 9, -1069501632), d = x(d, h, l, u, r[p + 11], 14, 643717713), u = x(u, d, h, l, r[p + 0], 20, -373897302), l = x(l, u, d, h, r[p + 5], 5, -701558691), h = x(h, l, u, d, r[p + 10], 9, 38016083), d = x(d, h, l, u, r[p + 15], 14, -660478335), u = x(u, d, h, l, r[p + 4], 20, -405537848), l = x(l, u, d, h, r[p + 9], 5, 568446438), h = x(h, l, u, d, r[p + 14], 9, -1019803690), d = x(d, h, l, u, r[p + 3], 14, -187363961), u = x(u, d, h, l, r[p + 8], 20, 1163531501), l = x(l, u, d, h, r[p + 13], 5, -1444681467), h = x(h, l, u, d, r[p + 2], 9, -51403784), d = x(d, h, l, u, r[p + 7], 14, 1735328473), l = g(l, u = x(u, d, h, l, r[p + 12], 20, -1926607734), d, h, r[p + 5], 4, -378558), h = g(h, l, u, d, r[p + 8], 11, -2022574463), d = g(d, h, l, u, r[p + 11], 16, 1839030562), u = g(u, d, h, l, r[p + 14], 23, -35309556), l = g(l, u, d, h, r[p + 1], 4, -1530992060), h = g(h, l, u, d, r[p + 4], 11, 1272893353), d = g(d, h, l, u, r[p + 7], 16, -155497632), u = g(u, d, h, l, r[p + 10], 23, -1094730640), l = g(l, u, d, h, r[p + 13], 4, 681279174), h = g(h, l, u, d, r[p + 0], 11, -358537222), d = g(d, h, l, u, r[p + 3], 16, -722521979), u = g(u, d, h, l, r[p + 6], 23, 76029189), l = g(l, u, d, h, r[p + 9], 4, -640364487), h = g(h, l, u, d, r[p + 12], 11, -421815835), d = g(d, h, l, u, r[p + 15], 16, 530742520), l = f(l, u = g(u, d, h, l, r[p + 2], 23, -995338651), d, h, r[p + 0], 6, -198630844), h = f(h, l, u, d, r[p + 7], 10, 1126891415), d = f(d, h, l, u, r[p + 14], 15, -1416354905), u = f(u, d, h, l, r[p + 5], 21, -57434055), l = f(l, u, d, h, r[p + 12], 6, 1700485571), h = f(h, l, u, d, r[p + 3], 10, -1894986606), d = f(d, h, l, u, r[p + 10], 15, -1051523), u = f(u, d, h, l, r[p + 1], 21, -2054922799), l = f(l, u, d, h, r[p + 8], 6, 1873313359), h = f(h, l, u, d, r[p + 15], 10, -30611744), d = f(d, h, l, u, r[p + 6], 15, -1560198380), u = f(u, d, h, l, r[p + 13], 21, 1309151649), l = f(l, u, d, h, r[p + 4], 6, -145523070), h = f(h, l, u, d, r[p + 11], 10, -1120210379), d = f(d, h, l, u, r[p + 2], 15, 718787259), u = f(u, d, h, l, r[p + 9], 21, -343485551), l = l + v >>> 0, u = u + _ >>> 0, d = d + w >>> 0, h = h + j >>> 0
            }
            return n.endian([l, u, d, h])
          };
        i._ff = function(e, n, t, c, o, s, i) {
          var r = e + (n & t | ~n & c) + (o >>> 0) + i;
          return (r << s | r >>> 32 - s) + n
        }, i._gg = function(e, n, t, c, o, s, i) {
          var r = e + (n & c | t & ~c) + (o >>> 0) + i;
          return (r << s | r >>> 32 - s) + n
        }, i._hh = function(e, n, t, c, o, s, i) {
          var r = e + (n ^ t ^ c) + (o >>> 0) + i;
          return (r << s | r >>> 32 - s) + n
        }, i._ii = function(e, n, t, c, o, s, i) {
          var r = e + (t ^ (n | ~c)) + (o >>> 0) + i;
          return (r << s | r >>> 32 - s) + n
        }, i._blocksize = 16, i._digestsize = 16, e.exports = function(e, t) {
          if (null == e) throw new Error("Illegal argument " + e);
          var c = n.wordsToBytes(i(e, t));
          return t && t.asBytes ? c : t && t.asString ? s.bytesToString(c) : n.bytesToHex(c)
        }
      }()
    },
    8585: function(e) {
      "use strict";
      e.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAOCAYAAAASVl2WAAAAAXNSR0IArs4c6QAAAPpJREFUKFOVka1OA0EQx/8j7oJAIHHUlXcoAodCIBAVCMztJaf6BidIsE1O3Ef2PAKBqKhoAklFEbzH3gNUNLmdDDdwlEKKYNzu/HY+fkt1XQ+Z+a5t29skSdb4FVQUxTMRnQNYOOcu0zTd7DKU5/mAiBQaAJg75652IVK6h1ZEdCwis6ZpFPKa+wA0siw7DcNwBeAIwIMxZvwD0IO1dsTMcwCHHTg1xky2Fb4qVVV1ISJPAA66u/R/QN9ioa9FZBrH8XcLFea9f9P+RPQYRdH1dkhr7Yn3/lXX7F2osM81NcnMLwD2iyrLctlpPvtTtQoKguCemW/2fdY748x+o/2Pns8AAAAASUVORK5CYII="
    },
    226: function(e, n, t) {
      "use strict";
      e.exports = t.p + "assets/images/finished.png"
    },
    4302: function(e, n, t) {
      "use strict";
      e.exports = t.p + "assets/images/hd-jinqibaoming-jiao.png"
    },
    2081: function(e, n, t) {
      "use strict";
      e.exports = t.p + "assets/images/hd-jinqibaoming.png"
    },
    2451: function(e, n, t) {
      "use strict";
      e.exports = t.p + "assets/images/hd-yizuoda.png"
    },
    4557: function(e, n, t) {
      "use strict";
      e.exports = t.p + "assets/images/underway.png"
    },
    524: function(e, n, t) {
      "use strict";
      e.exports = t.p + "assets/images/weikaishi.png"
    }
  },
  function(e) {
    e.O(0, [2107, 1216, 8592], (function() {
      return function(n) {
        return e(e.s = n)
      }(9247)
    })), e.O()
  }
]);