! function() {
  "use strict";
  require("./common"), require("./vendors"), require("./taro"), require("./runtime"), (wx.webpackJsonp = wx.webpackJsonp || []).push([
    [2143], {
      4728: function(e, a, n) {
        n(5743), n(1924);
        var i = n(4886),
          t = n(5045),
          o = n(1678),
          s = n.n(o),
          r = n(2017),
          p = n(2784),
          d = n.t(p, 2),
          g = n(5185),
          c = n(7581),
          x = n(2725),
          l = n(7298),
          u = n(6224),
          b = n(2322);
        (0, l.jW)();
        var f = function(e) {
            return s().useError((function(e) {
              u.Z.error(e), (0, r.Z)({
                click_id: "app_error",
                error: e
              })
            })), (0, p.useEffect)((function() {
              (0, x.xP)()
            }), []), (0, b.jsx)(g.zt, {
              store: c.Z,
              children: e.children
            })
          },
          h = n(8424),
          w = {
            pages: ["pages/home/<USER>", "pages/webViewPreparation/index", "pages/index/index", "pages/mine/index", "pages/course/index", "pages/webView/index", "pages/landScape/index", "pages/activity/index", "pages/userinfo/index", "pages/login/index", "pages/phoneLogin/index", "pages/webViewPay/index", "pages/wxPay/index"],
            subpackages: [{
              root: "pages/subpackageClassRoom",
              pages: ["classroom/index", "webViewClassroom/index"]
            }, {
              root: "pages/homework",
              pages: ["integratedSkills/index", "result/index", "AnsweringRules/index", "GoldCoinRewardDetails/index", "rejected/index"]
            }, {
              root: "pages/preparation",
              pages: ["Index/index"]
            }],
            preloadRule: {
              "pages/home/<USER>": {
                network: "all",
                packages: ["pages/subpackageClassRoom"]
              }
            },
            tabBar: {
              color: "#666666",
              backgroundColor: "#ffffff",
              selectedColor: "#ff3627",
              list: [{
                pagePath: "pages/home/<USER>",
                text: "首页",
                iconPath: "assets/tabbar/tb-home.png",
                selectedIconPath: "assets/tabbar/tb-home-active.png"
              }, {
                pagePath: "pages/course/index",
                text: "学习",
                iconPath: "assets/tabbar/tb-learn.png",
                selectedIconPath: "assets/tabbar/tb-learn-active.png"
              }, {
                pagePath: "pages/mine/index",
                text: "我的",
                iconPath: "assets/tabbar/tb-mine.png",
                selectedIconPath: "assets/tabbar/tb-mine-active.png"
              }]
            },
            window: {
              backgroundTextStyle: "light",
              navigationBarBackgroundColor: "#fff",
              navigationBarTitleText: "WeChat",
              navigationBarTextStyle: "black"
            },
            lazyCodeLoading: "requiredComponents",
            resizable: !0,
            __usePrivacyCheck__: !0
          };
        i.window.__taroAppConfig = w, App((0, t.Ox)(f, d, h.ZP, w)), (0, o.initPxTransform)({
          designWidth: 375,
          deviceRatio: {
            375: 2,
            640: 1.17,
            750: 1,
            828: .905
          }
        })
      },
      3122: function(e, a) {
        a.DefaultEventPriority = 16
      },
      1801: function(e, a, n) {
        e.exports = n(3122)
      }
    },
    function(e) {
      e.O(0, [2107, 1216, 8592], (function() {
        return function(a) {
          return e(e.s = a)
        }(4728)
      })), e.O()
    }
  ])
}();