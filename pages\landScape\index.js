! function() {
  "use strict";
  (wx.webpackJsonp = wx.webpackJsonp || []).push([
    [9239], {
      1817: function(e, n, o) {
        var t = o(4886),
          a = o(6234),
          i = o(1678),
          r = o.n(i),
          c = o(2784),
          u = o(3675),
          s = o(4488),
          p = o(2322);
        Page((0, t.createPageConfig)((function() {
          var e = (0, s.Z)(),
            n = e.url,
            o = e.getUrl,
            t = (0, c.useState)(""),
            i = (0, a.Z)(t, 2),
            d = i[0],
            g = i[1];
          return r().useDidShow((function() {
            var e, n = (null === (e = r().getCurrentInstance().router) || void 0 === e ? void 0 : e.params) || {},
              t = n.hidehomebutton;
            1 === Number(t) && r().hideHomeButton(), r().hideShareMenu({
              menus: ["shareAppMessage"]
            });
            var a = n.url || "",
              i = a && decodeURIComponent(a),
              c = r().getStorageSync("fe_log_uvid") || "";
            o("https://app.bcc.xiwang.com/h5-live/#/wx-wait?logUid=".concat(c));
            var u = i.split("?")[1];
            u ? g("/pages/subpackageClassRoom/classroom/index?".concat(u)) : r().showToast({
              title: "参数未找到",
              icon: "none",
              duration: 1e5
            })
          })), (0, p.jsx)(u.kh, {
            src: n,
            onLoad: function() {
              setTimeout((function() {
                r().redirectTo({
                  url: d
                })
              }), 0)
            }
          })
        }), "pages/landScape/index", {
          root: {
            cn: []
          }
        }, {
          navigationBarTitleText: "",
          pageOrientation: "landscape",
          backgroundColorTop: "#fff",
          navigationStyle: "custom"
        } || {}))
      }
    },
    function(e) {
      e.O(0, [2107, 1216, 8592], (function() {
        return function(n) {
          return e(e.s = n)
        }(1817)
      })), e.O()
    }
  ])
}();