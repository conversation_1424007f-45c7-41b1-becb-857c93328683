! function() {
  "use strict";
  (wx.webpackJsonp = wx.webpackJsonp || []).push([
    [3530], {
      8411: function(n, e, t) {
        var c = t(4886),
          i = t(1678),
          o = t.n(i),
          a = t(3675),
          r = t(2784),
          u = "index-module__container___AOm3Z",
          s = "index-module__ctn-btn___pDZJq",
          l = t(2322);
        Page((0, c.createPageConfig)((function() {
          var n = (0, r.useRef)({});
          return (0, r.useEffect)((function() {
            var e, t = null === (e = (0, i.getCurrentInstance)()) || void 0 === e || null === (e = e.router) || void 0 === e ? void 0 : e.params;
            t && 0 !== Object.keys(t).length ? n.current = t : o().showToast({
              title: "参数错误",
              icon: "error"
            })
          }), []), (0, l.jsx)(a.G7, {
            children: (0, l.jsx)(a.G7, {
              className: u,
              children: (0, l.jsx)(a.G7, {
                className: s,
                onClick: function() {
                  var e = n.current,
                    t = o().getAccountInfoSync().miniProgram.envVersion,
                    c = "/pages/activity/index" + "?".concat(Object.keys(e).map((function(n) {
                      return n.includes("taro") ? "" : "".concat(n, "=").concat(e[n])
                    })).join("&"));
                  c += "authorization=".concat(o().getStorageSync("Authorization")), o().navigateToMiniProgram({
                    appId: "wx1efe4e9d2c6377cb",
                    path: c,
                    envVersion: t
                  })
                },
                children: "参与活动"
              })
            })
          })
        }), "pages/activity/index", {
          root: {
            cn: []
          }
        }, {
          navigationBarTitleText: "",
          navigationStyle: "custom",
          disableScroll: !0
        } || {}))
      }
    },
    function(n) {
      n.O(0, [2107, 1216], (function() {
        return function(e) {
          return n(n.s = e)
        }(8411)
      })), n.O()
    }
  ])
}();