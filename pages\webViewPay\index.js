! function() {
  "use strict";
  (wx.webpackJsonp = wx.webpackJsonp || []).push([
    [7423], {
      8688: function(e, n, t) {
        var i = t(4886),
          o = t(6234),
          a = t(1678),
          r = t.n(a),
          u = t(2784),
          c = t(3675),
          s = t(6224),
          g = t(2322);
        Page((0, i.createPageConfig)((function() {
          var e = (0, u.useState)(""),
            n = (0, o.Z)(e, 2),
            t = n[0],
            i = n[1];
          return r().useError((function(e) {
            s.Z.error(e)
          })), r().useDidShow((function() {
            s.Z.pageShow()
          })), r().useDidHide((function() {
            r().showLoading({
              title: "处理中..."
            }), s.Z.pageHide()
          })), (0, u.useEffect)((function() {
            var e;
            r().hideHomeButton();
            var n = ((null === (e = r().getCurrentInstance().router) || void 0 === e ? void 0 : e.params) || {}).url || "",
              t = n && decodeURIComponent(n);
            r().hideShareMenu({
              menus: ["shareAppMessage"]
            });
            var o = r().getStorageSync("Authorization");
            if (o) {
              var a = "".concat("https://api.aidoutang.com/geminiapi/user/redirect", "?token=").concat(o, "&redirect=").concat(t);
              i(a)
            } else r().navigateTo({
              url: "/pages/login/index"
            })
          }), []), t && (0, g.jsx)(c.kh, {
            src: t
          })
        }), "pages/webViewPay/index", {
          root: {
            cn: []
          }
        }, {
          navigationBarTitleText: "希望学·看课",
          navigationStyle: "custom",
          pageOrientation: "portrait"
        } || {}))
      }
    },
    function(e) {
      e.O(0, [2107, 1216, 8592], (function() {
        return function(n) {
          return e(e.s = n)
        }(8688)
      })), e.O()
    }
  ])
}();