! function() {
  "use strict";
  (wx.webpackJsonp = wx.webpackJsonp || []).push([
    [4411], {
      2623: function(e, n, t) {
        var r = t(4886),
          c = (t(5803), t(1964)),
          a = t(2723),
          o = t(4795),
          s = t(6234),
          i = t(3675),
          l = t(1678),
          u = t.n(l),
          p = t(2784),
          d = t(2524),
          g = t.n(d),
          h = t(2725),
          f = t(6982),
          x = t(2017),
          w = function() {
            var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 60,
              n = (0, p.useState)(0),
              t = (0, s.Z)(n, 2),
              r = t[0],
              c = t[1],
              a = function() {
                c(e);
                var n = setInterval((function() {
                  c((function(e) {
                    return e > 0 ? e - 1 : (clearInterval(n), 0)
                  }))
                }), 1e3)
              };
            return {
              startDownCount: a,
              downCount: r
            }
          },
          k = t(586),
          m = t(3057),
          b = t(8371),
          v = t(2322),
          Z = [{
            id: "86",
            text: "中国 (+86)"
          }, {
            id: "852",
            text: "中国香港 (+852)"
          }],
          N = {
            86: {
              length: 11,
              rule: /^1[3456789]\d{9}$/
            },
            852: {
              length: 8,
              rule: /^([5|6|8|9])\d{7}$/
            }
          },
          A = function(e) {
            return e.replace(/\D/g, "")
          },
          j = function() {
            var e = (0, m.Z)(["backPage", "backPageType"]).params,
              n = (0, p.useState)("86"),
              t = (0, s.Z)(n, 2),
              r = t[0],
              l = t[1],
              d = (0, p.useState)(""),
              j = (0, s.Z)(d, 2),
              C = j[0],
              G = j[1],
              T = (0, p.useState)(""),
              _ = (0, s.Z)(T, 2),
              y = _[0],
              S = _[1],
              E = (0, p.useState)("获取验证码"),
              I = (0, s.Z)(E, 2),
              P = I[0],
              M = I[1],
              Q = (0, p.useState)(!1),
              R = (0, s.Z)(Q, 2),
              U = R[0],
              V = R[1],
              B = (0, p.useRef)(null),
              J = (0, p.useState)("86"),
              $ = (0, s.Z)(J, 2),
              W = $[0],
              D = $[1],
              L = (0, p.useState)(""),
              Y = (0, s.Z)(L, 2),
              q = Y[0],
              F = Y[1],
              H = (0, p.useState)(!1),
              O = (0, s.Z)(H, 2),
              X = O[0],
              z = O[1],
              K = (0, p.useState)(""),
              ee = (0, s.Z)(K, 2),
              ne = ee[0],
              te = ee[1],
              re = (0, p.useState)("code"),
              ce = (0, s.Z)(re, 2),
              ae = ce[0],
              oe = ce[1];
            (0, p.useEffect)((function() {
              (0, h.TZ)().then((function() {
                console.log("初始化成功")
              })), (0, x.Z)({
                click_id: "show_phonelogin_page"
              })
            }), []);
            var se = function(n) {
                var t = "/pages/home/<USER>",
                  r = e.backPage ? decodeURIComponent(e.backPage) : t;
                try {
                  switch (console.log("[ routerParams ]-85", e), e.backPageType) {
                    case b.cV.navigateBack:
                      u().navigateBack({
                        delta: 2
                      });
                      break;
                    case b.cV.navigateToMiniProgram:
                      u().switchTab({
                        url: t
                      });
                      break;
                    case b.cV.navigateTo:
                      u().navigateTo({
                        url: r
                      });
                      break;
                    case b.cV.redirectTo:
                      u().redirectTo({
                        url: r
                      });
                      break;
                    case b.cV.reLaunch:
                      u().reLaunch({
                        url: r
                      });
                      break;
                    case b.cV.switchTab:
                      u().switchTab({
                        url: r
                      });
                      break;
                    default:
                      u().switchTab({
                        url: t
                      })
                  }
                } catch (n) {
                  u().showToast({
                    title: "Navigation error",
                    icon: "none"
                  }), console.error("Navigation error:", n)
                }
              },
              ie = (0, p.useMemo)((function() {
                return !!N[r].rule.test(A(C))
              }), [C, r]),
              le = (0, p.useMemo)((function() {
                return "code" === ae ? ie && !(!y || 6 !== y.length) : "pwd" === ae ? !!(q && ne && ne.length >= 6) : void 0
              }), [ie, y, ne, q, ae]),
              ue = w(60),
              pe = ue.startDownCount,
              de = ue.downCount,
              ge = function() {
                var e = (0, o.Z)((0, a.Z)().mark((function e() {
                  return (0, a.Z)().wrap((function(e) {
                    for (;;) switch (e.prev = e.next) {
                      case 0:
                        if (ie) {
                          e.next = 2;
                          break
                        }
                        return e.abrupt("return", u().showToast({
                          title: "请输入正确的手机号",
                          icon: "none"
                        }));
                      case 2:
                        return u().showToast({
                          title: "验证码已发送",
                          icon: "none"
                        }), e.next = 5, (0, h.x4)(A(C));
                      case 5:
                        e.sent && (pe(), M("重新发送"));
                      case 7:
                      case "end":
                        return e.stop()
                    }
                  }), e)
                })));
                return function() {
                  return e.apply(this, arguments)
                }
              }(),
              he = function() {
                var e = (0, o.Z)((0, a.Z)().mark((function e() {
                  return (0, a.Z)().wrap((function(e) {
                    for (;;) switch (e.prev = e.next) {
                      case 0:
                        "code" === ae ? fe() : xe();
                      case 1:
                      case "end":
                        return e.stop()
                    }
                  }), e)
                })));
                return function() {
                  return e.apply(this, arguments)
                }
              }(),
              fe = function() {
                var e = (0, o.Z)((0, a.Z)().mark((function e() {
                  var n, t, r, c, o;
                  return (0, a.Z)().wrap((function(e) {
                    for (;;) switch (e.prev = e.next) {
                      case 0:
                        if (t = null == B || null === (n = B.current) || void 0 === n ? void 0 : n.agreeProtocol, ie) {
                          e.next = 3;
                          break
                        }
                        return e.abrupt("return", u().showToast({
                          title: "请输入正确的手机号",
                          icon: "none"
                        }));
                      case 3:
                        if (y && !(y.length < 6)) {
                          e.next = 5;
                          break
                        }
                        return e.abrupt("return", u().showToast({
                          title: "请输入正确的验证码",
                          icon: "none"
                        }));
                      case 5:
                        if (t) {
                          e.next = 7;
                          break
                        }
                        return e.abrupt("return", u().showToast({
                          title: "请同意用户协议，同意后可授权登录!",
                          icon: "none"
                        }));
                      case 7:
                        return r = A(C), (0, x.Z)({
                          click_id: "click_78_16_01_01"
                        }), e.next = 11, (0, h.um)(r, y);
                      case 11:
                        (c = e.sent) && (console.log("[ res ]-164", c), o = c.stuId, (0, x.Z)({
                          click_id: "login_code_success",
                          stuId: o
                        }), se(c));
                      case 13:
                      case "end":
                        return e.stop()
                    }
                  }), e)
                })));
                return function() {
                  return e.apply(this, arguments)
                }
              }(),
              xe = function() {
                var e = (0, o.Z)((0, a.Z)().mark((function e() {
                  var n, t, r;
                  return (0, a.Z)().wrap((function(e) {
                    for (;;) switch (e.prev = e.next) {
                      case 0:
                        if (q) {
                          e.next = 2;
                          break
                        }
                        return e.abrupt("return", u().showToast({
                          title: "请输入账号",
                          icon: "none"
                        }));
                      case 2:
                        if (ne && !(ne.length < 6)) {
                          e.next = 4;
                          break
                        }
                        return e.abrupt("return", u().showToast({
                          title: "请输入正确的密码",
                          icon: "none"
                        }));
                      case 4:
                        if (null == B || null === (n = B.current) || void 0 === n ? void 0 : n.agreeProtocol) {
                          e.next = 7;
                          break
                        }
                        return e.abrupt("return", u().showToast({
                          title: "请同意用户协议，同意后可授权登录!",
                          icon: "none"
                        }));
                      case 7:
                        return (0, x.Z)({
                          click_id: "click_78_17_01_01"
                        }), e.next = 10, (0, h.yl)(q, ne);
                      case 10:
                        (t = e.sent) && (r = t.stuId, (0, x.Z)({
                          click_id: "login_pwd_success",
                          stuId: r
                        }), se(t));
                      case 12:
                      case "end":
                        return e.stop()
                    }
                  }), e)
                })));
                return function() {
                  return e.apply(this, arguments)
                }
              }();
            return (0, v.jsxs)(i.G7, {
              className: "p-login",
              onClick: function() {
                V(!1)
              },
              children: [(0, v.jsxs)(i.G7, {
                className: "title",
                children: ["新用户登录可领取", (0, v.jsx)(i.xv, {
                  children: "免费新人礼"
                })]
              }), (0, v.jsx)(i.G7, {
                className: "login-tips",
                children: "新用户登录后将自动创建账号"
              }), (0, v.jsxs)(i.G7, {
                className: g()("login-container", {
                  "login-container-bg-code": "code" === ae,
                  "login-container-bg-pwd": "pwd" === ae
                }),
                children: [(0, v.jsxs)(i.G7, {
                  className: "login-type-con",
                  children: [(0, v.jsx)(i.G7, {
                    className: g()("login-type", {
                      "login-type-active": "code" === ae
                    }),
                    onClick: function() {
                      (0, x.Z)({
                        click_id: "code_login_click"
                      }), oe("code")
                    },
                    children: "验证码登录"
                  }), (0, v.jsx)(i.G7, {
                    className: g()("login-type", {
                      "login-type-active": "pwd" === ae
                    }),
                    onClick: function() {
                      (0, x.Z)({
                        click_id: "pwd_login_click"
                      }), oe("pwd")
                    },
                    children: "密码登录"
                  })]
                }), (0, v.jsxs)(i.G7, {
                  className: "login-input",
                  children: ["code" === ae && (0, v.jsxs)(v.Fragment, {
                    children: [(0, v.jsxs)(i.G7, {
                      className: "phone-input",
                      children: [(0, v.jsxs)(i.G7, {
                        className: "phone-code",
                        children: [(0, v.jsxs)(i.G7, {
                          className: "phone-code-box",
                          onClick: function(e) {
                            ! function(e) {
                              e.stopPropagation(), V(!U)
                            }(e)
                          },
                          children: [(0, v.jsxs)(i.xv, {
                            children: ["+", r]
                          }), (0, v.jsx)(i.Ee, {
                            className: "updown-icon",
                            src: f
                          })]
                        }), U && (0, v.jsx)(i.G7, {
                          className: "triangle"
                        }), U && (0, v.jsx)(i.G7, {
                          className: "phone-code-popup",
                          children: Z.map((function(e) {
                            return (0, v.jsx)(i.G7, {
                              className: g()("phone-code-popup-span", {
                                "phone-code-active": e.id === W
                              }),
                              onClick: function() {
                                ! function(e) {
                                  l(e.id), V(!U), D(e.id), G("")
                                }(e)
                              },
                              children: e.text
                            }, e.id)
                          }))
                        })]
                      }), (0, v.jsx)(c.I, {
                        type: "tel",
                        clearable: !0,
                        controlled: !0,
                        className: "tel-input",
                        placeholder: "请输入手机号",
                        value: C,
                        style: {
                          backgroundColor: "#f5f5f6"
                        },
                        onChange: function(e) {
                          var n = "86" === r ? 2 : 1,
                            t = function(e) {
                              var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "86";
                              if ("string" != typeof e) throw new Error("Input must be a string");
                              if ("" === e) return "";
                              var t = e.replace(/\D/g, "");
                              return "86" === n ? t.length > 7 ? t = t.replace(/^(.{3})(.{4})(.*)$/, "$1 $2 $3") : t.length > 3 && (t = t.replace(/^(.{3})(.*)$/, "$1 $2")) : t.length > 4 && (t = t.replace(/^(.{4})(.*)$/, "$1 $2")), t
                            }(e.substring(0, N[r].length + n).replace(/[^\d]/g, ""), r);
                          G(t)
                        }
                      })]
                    }), (0, v.jsxs)(i.G7, {
                      className: "code-wrapper",
                      children: [(0, v.jsx)(c.I, {
                        type: "number",
                        clearable: !0,
                        className: "no-arrow",
                        placeholder: "请输入验证码",
                        value: y,
                        onChange: function(e) {
                          var n = e.substring(0, 6);
                          S(n)
                        }
                      }), 0 !== de ? (0, v.jsx)(i.G7, {
                        className: "code-pending",
                        children: de
                      }) : (0, v.jsx)(i.G7, {
                        className: g()("code-btn", {
                          "code-not-ok": !ie,
                          "code-ok": ie
                        }),
                        onClick: ge,
                        children: P
                      })]
                    })]
                  }), "pwd" === ae && (0, v.jsxs)(v.Fragment, {
                    children: [(0, v.jsx)(i.G7, {
                      className: "phone-input",
                      children: (0, v.jsx)(c.I, {
                        type: "text",
                        clearable: !0,
                        className: "tel-input no-arrow tel-input-pwd",
                        placeholder: "请输入手机号/通行证/邮箱",
                        value: q,
                        onChange: function(e) {
                          F(e)
                        }
                      })
                    }), (0, v.jsxs)(i.G7, {
                      className: "code-wrapper pwd-wrapper",
                      children: [(0, v.jsx)(c.I, {
                        type: X ? "text" : "password",
                        clearable: !0,
                        className: "no-arrow",
                        placeholder: "请输入密码",
                        value: ne,
                        onChange: function(e) {
                          var n = e.substring(0, 16);
                          te(n)
                        }
                      }), (0, v.jsx)(i.G7, {
                        className: "eye",
                        onClick: function() {
                          return z(!X)
                        },
                        children: (0, v.jsx)(i.Ee, {
                          className: "icon-img",
                          src: X ? "https://m.xiwang.com/resource/309fYK2Jk0UqUOxYa3kEc-1733912722289.png" : "https://m.xiwang.com/resource/EU4Wmp3uWfeyeMcEpKfwN-1733912715072.png"
                        })
                      })]
                    })]
                  })]
                }), (0, v.jsx)(i.G7, {
                  className: g()("submit-btn", {
                    "submit-ok": le
                  }),
                  onClick: he,
                  children: "登录"
                }), (0, v.jsx)(k.Z, {
                  className: "login__protocol",
                  ref: B
                })]
              })]
            })
          };
        Page((0, r.createPageConfig)(j, "pages/phoneLogin/index", {
          root: {
            cn: []
          }
        }, {
          navigationBarTitleText: "登录"
        } || {}))
      },
      6982: function(e) {
        e.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAASCAYAAAA+PQxvAAAAAXNSR0IArs4c6QAAASZJREFUSEvN1bFqwzAQBuA7mw4d5TUPkqFbtoxdvGRoCRiBp+YN/ALNGs4YujRDoQ/StWOGZAx+CJkrhhiM0tg6WUO8ykiffnR3CHfy4Z044ApSVdVT0zQrAHjXWh9DQonoAQA2URSdsiz77u99BSGiHwCYI+LZGLPI8/wQAtMiEPGLmZ+Z2dR1/VgUhen2/g+iEXHHzBgK00dcDv7QWq8HE2kXiegNEbchMDaCmfdJkrykadqMQkJhXBHteYNVMyUZCWIU4puMFOEEkWJ8EM4QV4wvQgQZw0xBiCG3MHEcn7pmdSnJT6XUq12iQ43Ra9bY1cTMvwCw9EV4JdLdqo/p3VScxM0WL5krFsYbMSmRDlyWZTvEZkqpneRN2Bf2eiOS1Fz//QMWURsiwN4PCQAAAABJRU5ErkJggg=="
      }
    },
    function(e) {
      e.O(0, [2107, 1216, 8592], (function() {
        return function(n) {
          return e(e.s = n)
        }(2623)
      })), e.O()
    }
  ])
}();