! function() {
  "use strict";
  (wx.webpackJsonp = wx.webpackJsonp || []).push([
    [6786], {
      6499: function(e, n, i) {
        var c = i(4886),
          s = i(2723),
          a = i(4795),
          t = i(6234),
          o = i(1678),
          r = i.n(o),
          l = i(3675),
          u = i(8659),
          p = i(2725),
          d = i(909),
          m = i.n(d),
          g = i(5185),
          R = i(7048),
          x = i(2017),
          A = i(6224),
          h = i(8371),
          f = i(2784),
          k = i(4383),
          _ = i(46),
          j = i(1352),
          v = i(2492),
          N = i(2356),
          E = i(2322);
        Page((0, c.createPageConfig)((function() {
          var e = (0, f.useState)(!0),
            n = (0, t.Z)(e, 2),
            i = n[0],
            c = n[1],
            d = (0, g.I0)(),
            M = (0, g.v9)((function(e) {
              return e.user.userInfo
            }));
          r().useError((function(e) {
            A.Z.error(e)
          })), r().useDidShow((function() {
            A.Z.pageShow(), (0, x.Z)({
              click_id: "view_78_09_01_01"
            }), (0, p.TZ)().then((function() {
              d((0, R.c6)()).then((function(e) {
                1009 === e.code ? c(!1) : c(!0)
              })).catch((function(e) {
                c(!1), (0, x.Z)({
                  click_id: "catch_getUserInfo_error",
                  err: e
                })
              }))
            }))
          })), r().useDidHide((function() {
            A.Z.pageHide()
          })), r().useTabItemTap((function(e) {
            (0, x.Z)({
              click_id: "click_78_10_01_01"
            }), console.log(e)
          }));
          var b = function() {
              r().navigateTo({
                url: "/pages/login/index?backPage=".concat(encodeURIComponent("/pages/home/<USER>"))
              })
            },
            G = function() {
              r().navigateTo({
                url: "/pages/login/index?backPage=".concat(encodeURIComponent("/pages/mine/index"), "&backPageType=switchTab")
              })
            },
            V = function() {
              var e = (0, a.Z)((0, s.Z)().mark((function e(n) {
                var c, a, t, l, d, g, R;
                return (0, s.Z)().wrap((function(e) {
                  for (;;) switch (e.prev = e.next) {
                    case 0:
                      return e.prev = 0, i || G(), c = {
                        1: "click_78_19_02_02",
                        2: "click_78_19_02_03",
                        3: "click_78_19_03_02",
                        4: "click_78_19_03_03",
                        5: "click_78_19_03_04"
                      }, (0, x.Z)({
                        click_id: c[n]
                      }), r().showLoading(), e.next = 8, (0, u.fA)({
                        type: n
                      });
                    case 8:
                      if (0 === (a = e.sent).code) {
                        e.next = 12;
                        break
                      }
                      return r().hideLoading(), e.abrupt("return");
                    case 12:
                      if (1 !== n) {
                        e.next = 33;
                        break
                      }
                      if (t = r().getStorageSync("oauth_open_id"), console.log(t, "myopenid"), t && "" !== t && void 0 !== t) {
                        e.next = 24;
                        break
                      }
                      return e.next = 18, r().login();
                    case 18:
                      return l = e.sent, console.log(l), e.next = 22, (0, u.wr)({
                        code: l.code
                      });
                    case 22:
                      0 === (d = e.sent).code ? ((0, o.setStorageSync)("oauth_open_id", d.data.openid), t = d.data.openid) : t = r().getStorageSync("oauth_open_id");
                    case 24:
                      if (g = "https://pointmall.100tal.com/api/sso/authorize?channel=wxxcx&client_group=51&appid=".concat("wxd2606008715f7ba5", "&openid=", t), r().getStorageSync("tal-passport-minisdk-tal-token")) {
                        e.next = 30;
                        break
                      }
                      return R = r().getStorageSync("Authorization"), e.next = 30, m().writeBackTalToken(R);
                    case 30:
                      (0, p.Td)("101202", g).then((function(e) {
                        0 === e.errcode ? r().navigateTo({
                          url: "/pages/webView/index?url=".concat(encodeURIComponent(e.data.redirect))
                        }) : (console.log(e.errmsg), G(), r().showToast({
                          title: e.errmsg,
                          icon: "none"
                        }))
                      })).catch((function(e) {
                        console.log(e)
                      })), e.next = 34;
                      break;
                    case 33:
                      r().navigateTo({
                        url: "/pages/webView/index?url=".concat(encodeURIComponent(a.data), "&isLogin=").concat(i ? "1" : "0")
                      });
                    case 34:
                      r().hideLoading(), e.next = 41;
                      break;
                    case 37:
                      e.prev = 37, e.t0 = e.catch(0), console.log("err:", e.t0), r().hideLoading();
                    case 41:
                    case "end":
                      return e.stop()
                  }
                }), e, null, [
                  [0, 37]
                ])
              })));
              return function(n) {
                return e.apply(this, arguments)
              }
            }();
          return (0, E.jsxs)(l.G7, {
            className: "center-user",
            children: [(0, E.jsxs)(l.G7, {
              className: "center-user-info",
              children: [(0, E.jsx)(l.G7, {
                className: "center-user-info__avatar",
                children: (0, E.jsx)(l.Ee, {
                  className: "center-user-info__img",
                  src: i ? null == M ? void 0 : M.avatar_path : "https://m.xiwang.com/resource/nnIU1DlaK9ReAEm-zicoO-1733294287039.png"
                })
              }), (0, E.jsxs)(l.G7, {
                className: "user-info-detail",
                children: [!i && (0, E.jsxs)(l.G7, {
                  className: "user-info-detail-name not-login",
                  onClick: function() {
                    return r().navigateTo({
                      url: "/pages/login/index?backPageType=".concat(h.cV.navigateBack)
                    })
                  },
                  children: [(0, E.jsx)(l.xv, {
                    className: "user-info-detail-name__text ",
                    children: "登录/注册"
                  }), (0, E.jsx)(l.G7, {
                    className: "center-user-info__icon"
                  })]
                }), i && (0, E.jsxs)(E.Fragment, {
                  children: [(0, E.jsxs)(l.G7, {
                    className: "user-info-detail-name",
                    onClick: function() {
                      return r().navigateTo({
                        url: "/pages/userinfo/index"
                      })
                    },
                    children: [(0, E.jsx)(l.xv, {
                      className: "user-info-detail-name__text",
                      children: null != M && M.realname ? null == M ? void 0 : M.realname : null == M ? void 0 : M.nickname
                    }), (0, E.jsx)(l.G7, {
                      className: "center-user-info__icon"
                    })]
                  }), (0, E.jsx)(l.G7, {
                    className: "user-info-detail__phone",
                    children: null == M ? void 0 : M.phone
                  }), (0, E.jsxs)(l.G7, {
                    className: "user-info-detail__grade",
                    children: [(0, E.jsx)(l.xv, {
                      children: null == M ? void 0 : M.grade_name
                    }), (null == M ? void 0 : M.school) && (0, E.jsxs)(l.G7, {
                      children: ["-", null == M ? void 0 : M.school]
                    })]
                  })]
                })]
              })]
            }), (0, E.jsxs)(l.G7, {
              className: "out-link-box-1",
              children: [(0, E.jsxs)(l.G7, {
                className: "out-link-item",
                onClick: function() {
                  return V(1)
                },
                children: [(0, E.jsxs)(l.G7, {
                  className: "left-box",
                  children: [(0, E.jsx)(l.G7, {
                    className: "title",
                    children: "金币商场"
                  }), (0, E.jsx)(l.G7, {
                    className: "subtitle",
                    children: "金币兑好礼"
                  })]
                }), (0, E.jsx)(l.G7, {
                  className: "right-icon",
                  children: (0, E.jsx)(l.Ee, {
                    src: k,
                    className: "img"
                  })
                })]
              }), (0, E.jsxs)(l.G7, {
                className: "out-link-item out-link-item_kapai",
                onClick: function() {
                  return V(2)
                },
                children: [(0, E.jsxs)(l.G7, {
                  className: "left-box",
                  children: [(0, E.jsx)(l.G7, {
                    className: "title",
                    children: "卡牌中心"
                  }), (0, E.jsx)(l.G7, {
                    className: "subtitle",
                    children: "做任务集卡"
                  })]
                }), (0, E.jsx)(l.G7, {
                  className: "right-icon",
                  children: (0, E.jsx)(l.Ee, {
                    src: _,
                    className: "img"
                  })
                })]
              })]
            }), (0, E.jsxs)(l.G7, {
              className: "out-link-box",
              children: [(0, E.jsxs)(l.G7, {
                className: "out-link-item",
                onClick: function() {
                  return V(3)
                },
                children: [(0, E.jsx)(l.Ee, {
                  className: "out-link-item__img",
                  src: j
                }), (0, E.jsx)(l.xv, {
                  className: "out-link-item__text",
                  children: "必背古诗"
                })]
              }), (0, E.jsxs)(l.G7, {
                className: "out-link-item",
                onClick: function() {
                  return V(4)
                },
                children: [(0, E.jsx)(l.Ee, {
                  className: "out-link-item__img",
                  src: v
                }), (0, E.jsx)(l.xv, {
                  className: "out-link-item__text",
                  children: "数学口算"
                })]
              }), (0, E.jsxs)(l.G7, {
                className: "out-link-item",
                onClick: function() {
                  return V(5)
                },
                children: [(0, E.jsx)(l.Ee, {
                  className: "out-link-item__img",
                  src: N
                }), (0, E.jsx)(l.xv, {
                  className: "out-link-item__text",
                  children: "背单词"
                })]
              })]
            }), (0, E.jsxs)(l.G7, {
              className: "center-user-container",
              children: [(0, E.jsxs)(l.G7, {
                className: "center-user-service",
                onClick: function() {
                  (0, x.Z)({
                    click_id: "service_click"
                  }), r().navigateTo({
                    url: "/pages/webView/index?url=".concat(encodeURIComponent("https://app.xue.xiwang.com/polymerh5/app/#/chat?brand=100&key=280"), "&isLogin=").concat(i ? "1" : "0")
                  })
                },
                children: [(0, E.jsxs)(l.G7, {
                  className: "center-user-service__text",
                  children: [(0, E.jsx)(l.Ee, {
                    mode: "widthFix",
                    className: "img-icon",
                    src: "https://m.xiwang.com/resource/6lUKfhFrOQV14vkkJRYPT-1733912411431.svg"
                  }), "联系客服"]
                }), (0, E.jsx)(l.G7, {
                  className: "center-user-service__icon"
                })]
              }), (0, E.jsx)(l.G7, {
                className: "center-user-sline"
              }), (0, E.jsxs)(l.G7, {
                className: "center-user-about",
                onClick: function() {
                  (0, x.Z)({
                    click_id: "about_us_click"
                  }), r().navigateTo({
                    url: "/pages/webView/index?url=".concat(encodeURIComponent("https://mp.weixin.qq.com/s?__biz=MzkwODY5NjM1OQ==&mid=2247483835&idx=1&sn=60806a1ee0e06d85db6c4e4d3772e548&chksm=c0c74ff3f7b0c6e50abda805e046f7e1075dcbc930c101eaf67816e627e57ac41b114aedcef1#rd"), "&isLogin=0")
                  })
                },
                children: [(0, E.jsxs)(l.G7, {
                  className: "center-user-about__text",
                  children: [(0, E.jsx)(l.Ee, {
                    mode: "widthFix",
                    className: "img-icon",
                    src: "https://m.xiwang.com/resource/z5BDC-OYwFJsudMJLBA-n-1733912363907.svg"
                  }), "了解我们"]
                }), (0, E.jsx)(l.G7, {
                  className: "center-user-about__icon"
                })]
              })]
            }), i && (0, E.jsx)(l.G7, {
              className: "wrong-tips__btn",
              onClick: function() {
                if ((0, x.Z)({
                    click_id: "click_78_10_01_01"
                  }), r().getStorageSync("oauth_open_id")) {
                  var e = {
                    client_id: "514255",
                    bind_key: "wxd2606008715f7ba5",
                    openid: r().getStorageSync("oauth_open_id")
                  };
                  (0, u.k4)(e).then((function(e) {
                    0 === e.code && (0, p.rB)(r().getStorageSync("Authorization")).then((function() {
                      b()
                    }))
                  }))
                } else(0, p.rB)(r().getStorageSync("Authorization")).then((function() {
                  b()
                }))
              },
              children: "切换账号"
            })]
          })
        }), "pages/mine/index", {
          root: {
            cn: []
          }
        }, {
          navigationBarTitleText: "希望学",
          pageOrientation: "portrait"
        } || {}))
      },
      2356: function(e) {
        e.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADQAAAA0CAMAAADypuvZAAAA6lBMVEUAAAA9R1Q/RlM+RlL/pmP/kFD+pWM+RlD/pWT+pWT9pmM/RVM/RVI/RlP/pmM+RlI9RFM+RFM/RlE/RlI/RlJAQEA/RlM/RVM/RlP+pWM/RlM+RlJARFE/RVM+QFE/RVI/RlM/RVL/pWQ+RVM+RlNAQE1AQFD+pWP+pGM/RVM/RlM+RlI+RlM+RlP+pWM/RlP+pGQ/RVL/pmT+pGP/pWQ9R1P+pWL+pmT4oWH1oWH/n2A+RVP/pWP/pmP/pWX/pGP5omNARlNAQFU/RlP+pGT9pWT/pmM+RVL7oWL/qmL/lVVAQEA/RlP+pWRB2HLoAAAATHRSTlMATq9/XwXfIW/PUPfw2a9hR0At37oI0L/37+d1OTYZ67Odf29aFBD35cekkYuH1cvHw6qfklNLNyEZEJeFeVtDLCgM4LeXZ2c8JwwMhDpqRAAAAnVJREFUSMetltt6ojAQgAda1K1QAUFYD6jrubq1trZWt+c975L3f53NIHHAD8he9L/iy+Q3yWSSCO+C9mopKTpvMqXc18Njqqpd6NSqYRbVnwWOEuZh5jpuE+NNT03SM7DRyJ8cj/pDDY4YotXJycGiy4OLjIiKy3KUjKl9DiMoRMz3oVkZ0gTePuBTgDBFEs30OI0Q0R0zO6v9eoZ1gU1faxrk0lGjX01M5BYbnDEU0oo6UbnhVixd/JRaVrIQfBukOLxfL7miOchph5wR7NHFjsvAHN/GpYBpgf8BC+CE9s6TCqIKVarTC4pIFrVMDyon4D27lLxa1m2x2UHEy71oCTlunH46mdurUswP+M6mUe87xjZxGEvwFRCsB7G1Z0xwDR8Yu8JqrDB2GodnvGsbEDzkAUnfPiGPW5RYJS31Dicu5GgkYT8kkia/U1JfLP8vHhTIlCY4wb1EZ3iAH6MojyRdnnOuHzQulW4Ye0hKJ7g7oorqJAleUNpN2WQjl3AkZB2NhOm+fJRLtCaUgE9wmiHZ/KORK+24kiMZuRI8Z0rBUfYmHyMqsQTrLEnD50XcRL+ojIT0p8TYXRxe8K4tcYjpknk+3fO0hbPzJ0Dub9aiYNTDgfhC974M41CwCuZcAwlid/w3QFydLhn5xeIkatfQ5M5IxyXRqJgUKbj4pgs0lEig7BkfgGCMh1eXWGYVV+EevXXVYZEzQMfvpJ8RJP+vibXiYcoCVRWyanWCdB61ka2cYNHQgoiaHwr0ujHzPNXxVo26HlJzxstvL8NCekF2TmcFipVfXAO10QzTdA1vPhyBjHHZbluWYlptuzzW4J34B1jZyY9ZCjtdAAAAAElFTkSuQmCC"
      },
      1352: function(e, n, i) {
        e.exports = i.p + "assets/images/mine-gushi.png"
      },
      4383: function(e, n, i) {
        e.exports = i.p + "assets/images/mine-jinbi.png"
      },
      46: function(e, n, i) {
        e.exports = i.p + "assets/images/mine-kapai.png"
      },
      2492: function(e) {
        e.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADQAAAA0CAMAAADypuvZAAAA5FBMVEUAAAA+RFI9RFRAUXU+p/8+RVE+RFI+RVI+RVE9RFJAQFBAQEk+RFI9RFE+RFI+p/8+RFE8RVE+RVI+RVI+RFI5Qk46QVA8QE0+RVE+RlI+RVI6RFE8of8+RFE+pv8+pv8+RVI+pv8+RFE+RVE+RlI9RFM7RVA5QlFAn/9An/8+pv89pv8+RVI+RVI+RFI+RFE9RFM+RVI9RFE+RVE9RVE+pv89RFI8p/89pf87RlA9pP8+pv8+RVI+pf89pv8+pv8+pf85pv89SE87pP89RFI9pv8/pv89RVI+pf8+RVE+RVI+pv9EfrrQAAAASnRSTlMAr08EgPvwb+C5EAn3v39vWT731V8kGhTPj3MrF+nfzZ6Qh3dkRjQfEAjXtquLg3tTyrOjlmdLOTEvIO/bx617WyMjHMO/p6d3a1lRMZwAAAJvSURBVEjH1ZbreqIwEIZFVCqiiEfEolKUtp7W1narPW+7R7j/+9lMSAiEhPbfPju/kpB3MvPNJFr6H009tv3NaNSaW8NPEgOt3gkT654FHyPWuhpyZmuNQuStHorspF2QimPQXcrG17Tt3qbHNgcSZkiOsZ1+sqbvlDhBdyFkzk9inxYvzAhjxlzKuKLo+03sTsvHhpm6JPQvkFu1zGuA81lLtV1AiMY4u9jCjBhgVDfj9GhAbHTpVET1wO1ZegWCc0k+F9NoeiGgZpBWn80t8EJ1M6MoMgWQDlLN2PwaTa/ppIKgiizA6nlSIjgo+BBSH9G2Fp35aHIV53NrmisErUzzNp/XHASkk+9JuacRs6WaywpKTKRowBjH+h6l7Z6HsMi9eBig4WMc9TLFTC7z3YR23jBV1qSsL573hIAnz6u856X4CreA8VBqmXrMxkyJERo6n4KGaGeNQFQ81hG/xZAuhU4n0YRr2ZdocmAniXIqXR64Ct1DufEVhjeHFXpfKrBXBP3g1LOIJlJ7ptL4uM9ZRwzlzB2U+o7ehj9k9Qp3B29MGMRMsXe43+PUA9GUMd+WyUE9qgMRhXnghKs8AOPhiZ2RGR7DXwLEJA28wr1bBt9vmUYMB/nIotieMdPook1K6nNX+OoeMPLwGs+24PmY+uyIpfiJZKPXfhGyy8SkMERyJxfRMkA6PfO1JqsvZWBDx8ouulgJqbUNCM7JX5SqKkP0WQjW4j3ByydDtBrPsEbaiAg12MRIhzz1nOT2rpy2XXk33you/S8h6LIgLLSaL8x3X4S0dHG2jbqMUNpqwY/qjcLbbNsbA/Hv7C/H4rkd1o1FPAAAAABJRU5ErkJggg=="
      }
    },
    function(e) {
      e.O(0, [2107, 1216, 8592], (function() {
        return function(n) {
          return e(e.s = n)
        }(6499)
      })), e.O()
    }
  ])
}();