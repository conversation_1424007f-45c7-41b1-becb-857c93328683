! function() {
  "use strict";
  (wx.webpackJsonp = wx.webpackJsonp || []).push([
    [2003], {
      1356: function(n, e, o) {
        var c = o(4886),
          a = o(6234),
          t = o(2017),
          i = o(2784),
          l = o(1678),
          r = o.n(l),
          s = o(3517),
          u = o(3675),
          g = o(3057),
          d = o(2725),
          m = o(2524),
          h = o.n(m),
          v = o(2322),
          p = function(n) {
            var e = n.type,
              o = n.onClick,
              c = (0, i.useState)(!1),
              t = (0, a.Z)(c, 2),
              l = t[0],
              r = t[1],
              s = function() {
                setTimeout((function() {
                  r(!0), setTimeout((function() {
                    r(!1)
                  }), 100), "cancel" === e && (null == o || o())
                }))
              };
            return "cancel" === e ? (0, v.jsx)(u.zx, {
              className: h()("privacy-modal-content-btn", "privacy-modal-content-btn-disagree", {
                "privacy-modal-content-btn-touched": l
              }),
              onClick: s,
              children: "不同意"
            }) : (0, v.jsx)(u.zx, {
              className: h()("privacy-modal-content-btn", {
                "privacy-modal-content-btn-touched": l
              }),
              id: "agree-btn",
              "open-type": "agreePrivacyAuthorization",
              onClick: s,
              onAgreePrivacyAuthorization: o,
              children: "同意"
            })
          },
          f = function(n) {
            var e = n.visible,
              o = n.title,
              c = n.content,
              a = n.onCancel,
              t = n.onOk;
            return e ? (0, v.jsx)(u.G7, {
              className: "privacy-modal",
              children: (0, v.jsxs)(u.G7, {
                className: "privacy-modal-ctr",
                children: [(0, v.jsx)(u.G7, {
                  className: "privacy-modal-top"
                }), (0, v.jsxs)(u.G7, {
                  className: "privacy-modal-content",
                  children: [(0, v.jsx)(u.G7, {
                    className: "privacy-modal-content-title",
                    children: o
                  }), (0, v.jsx)(u.G7, {
                    className: "privacy-modal-content-text",
                    children: c
                  }), (0, v.jsxs)(u.G7, {
                    className: "privacy-modal-content-btns",
                    children: [(0, v.jsx)(p, {
                      type: "cancel",
                      onClick: a
                    }), (0, v.jsx)(p, {
                      type: "ok",
                      onClick: t
                    })]
                  })]
                })]
              })
            }) : null
          },
          x = function(n) {
            var e = n.resolvePrivacyAuthorization,
              o = n.onCancel,
              c = (0, i.useState)(!0),
              t = (0, a.Z)(c, 2),
              l = t[0],
              s = t[1];
            return (0, v.jsx)(f, {
              visible: l,
              title: "希望学在线小程序",
              content: (0, v.jsx)(v.Fragment, {
                children: (0, v.jsxs)(u.xv, {
                  children: ["在您使用【希望学在线小程序】服务之前，请仔细阅读", (0, v.jsx)(u.xv, {
                    onClick: function() {
                      r().openPrivacyContract()
                    },
                    className: "logo-color",
                    children: "《希望学在线隐私保护指引》"
                  }), "。如您同意《希望学在线隐私保护指引》，请点击“同意”开始使用【希望学在线小程序】"]
                })
              }),
              onOk: function() {
                var n;
                s(!1), null === (n = e.current) || void 0 === n || n.call(e, {
                  buttonId: "agree-btn",
                  event: "agree"
                })
              },
              onCancel: function() {
                var n;
                null === (n = e.current) || void 0 === n || n.call(e, {
                  buttonId: "agree-btn",
                  event: "disagree"
                }), s(!1), o(!1)
              }
            })
          },
          b = o(586),
          _ = o(2967),
          k = function() {
            var n = (0, i.useRef)(null),
              e = (0, i.useState)(""),
              o = (0, a.Z)(e, 2),
              c = o[0],
              m = o[1],
              h = (0, i.useState)(!1),
              p = (0, a.Z)(h, 2),
              f = p[0],
              k = p[1],
              y = (0, i.useState)(!1),
              j = (0, a.Z)(y, 2),
              P = j[0],
              N = j[1],
              C = (0, i.useState)(!1),
              T = (0, a.Z)(C, 2),
              z = T[0],
              Z = T[1],
              S = (0, g.Z)(["backPage", "backPageType"]).params;
            (0, i.useEffect)((function() {
              r().hideHomeButton(), A(), (0, t.Z)({
                click_id: "show_login_page"
              })
            }), []);
            var w = function() {
                if (S.backPageType && "reLaunch" === S.backPageType && S.backPage) r().reLaunch({
                  url: decodeURIComponent(S.backPage)
                });
                else if (S.backPage) {
                  var n = decodeURIComponent(S.backPage);
                  ["/pages/home/<USER>", "/pages/mini/index", "/pages/course/index", "/pages/userinfo/index"].includes(n) ? r().switchTab({
                    url: n
                  }) : r().navigateBack({
                    delta: 1
                  })
                } else r().navigateBack({
                  delta: 1
                })
              },
              G = function(n) {
                var e = n,
                  o = e.phoneLoginStatus,
                  c = e.code;
                m(void 0 === c ? "" : c), Z(o), r().getStorageSync("Authorization") && (0, t.Z)({
                  click_id: "login_auth_sucess"
                })
              },
              A = function() {
                r().getStorageSync("Authorization") || (0, d.TZ)().then((function(n) {
                  G(n), console.log("初始化成功", n)
                })).catch((function(n) {
                  console.log("初始化失败", n)
                }))
              },
              O = function() {
                r().showToast({
                  title: "请同意用户协议，同意后可授权登录!",
                  duration: 2e3,
                  icon: "none",
                  mask: !1
                })
              },
              B = function(e) {
                var o;
                if ((0, t.Z)({
                    click_id: "click_78_15_01_01"
                  }), null != n && null !== (o = n.current) && void 0 !== o && o.agreeProtocol) return e && 1400001 === e.detail.errno ? (console.log("切换手机号登录方式"), (0, t.Z)({
                  click_id: "change_phone_login_way"
                }), void I()) : void(0, d.Hg)(e).then((function(n) {
                  G(n), w()
                }));
                O()
              },
              I = function() {
                var n = s.a2,
                  e = {};
                S.backPage && (e.backPage = S.backPage), S.backPageType && (e.backPageType = S.backPageType);
                var o = "".concat(n, "?").concat(Object.keys(e).map((function(n) {
                  return "".concat(n, "=").concat(e[n])
                })).join("&"));
                r().navigateTo({
                  url: o
                })
              },
              L = (0, i.useRef)();
            (0, l.useLoad)((function() {
              var n;
              null === (n = r().onNeedPrivacyAuthorization) || void 0 === n || n.call(r(), (function(n) {
                console.log("监听到授权，打开弹窗", n), N(!0), L.current = n
              }))
            }));
            return (0, v.jsxs)(u.G7, {
              className: "login",
              children: [(0, v.jsxs)(u.G7, {
                className: "login__logo-container",
                children: [(0, v.jsx)(u.Ee, {
                  className: "login__logo-img",
                  src: _
                }), (0, v.jsx)(u.G7, {
                  className: "login__logo-title",
                  children: "希望学"
                })]
              }), (0, v.jsxs)(u.G7, {
                className: "login__btn",
                children: [z ? f ? (0, v.jsx)(u.zx, {
                  className: "login__btn-wechat login__btn-common",
                  onGetPhoneNumber: B,
                  "open-type": "getPhoneNumber",
                  children: "一键登录"
                }) : (0, v.jsx)(u.zx, {
                  className: "login__btn-wechat login__btn-common",
                  onClick: B,
                  children: "一键登录"
                }) : (0, v.jsx)(u.zx, {
                  className: "login__btn-wechat login__btn-common",
                  onClick: function() {
                    var e;
                    console.log("用户手动退出后，点击登录，或者已经登录过和微信授权过"), null != n && null !== (e = n.current) && void 0 !== e && e.agreeProtocol ? (0, d.PT)(c).then((function(n) {
                      G(n), w()
                    })).catch((function() {
                      r().getStorageSync("Authorization") || (0, d.TZ)().then((function(n) {
                        G(n)
                      }))
                    })) : O()
                  },
                  children: "一键登录"
                }), (0, v.jsx)(u.zx, {
                  className: "login__btn-phone login__btn-common",
                  onClick: I,
                  children: "手机号登录/注册"
                })]
              }), (0, v.jsx)(b.Z, {
                changed: function(n) {
                  k(n)
                },
                className: "login__protocol",
                ref: n
              }), P ? (0, v.jsx)(x, {
                onCancel: function() {
                  N(!1)
                },
                resolvePrivacyAuthorization: L
              }) : null]
            })
          };
        Page((0, c.createPageConfig)(k, "pages/login/index", {
          root: {
            cn: []
          }
        }, {
          navigationBarTitleText: "登录",
          backgroundColorTop: "#fff"
        } || {}))
      },
      2967: function(n, e, o) {
        n.exports = o.p + "assets/images/xiwangxue-logo.png"
      }
    },
    function(n) {
      n.O(0, [2107, 1216, 8592], (function() {
        return function(e) {
          return n(n.s = e)
        }(1356)
      })), n.O()
    }
  ])
}();