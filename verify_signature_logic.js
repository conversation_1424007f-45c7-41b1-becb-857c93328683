const crypto = require('crypto');

// 模拟CryptoJS
const CryptoJS = {
    SHA1: function(data) {
        return {
            toString: function() {
                return crypto.createHash('sha1').update(data).digest('hex');
            }
        };
    },
    MD5: function(data) {
        return {
            toString: function() {
                return crypto.createHash('md5').update(data).digest('hex');
            }
        };
    }
};

// Base64编码
const Base64 = {
    encode: function(str) {
        return Buffer.from(str).toString('base64');
    }
};

console.log("=== 验证签名生成逻辑 ===");

// 已知数据
const timestamp = 1755837626550;
const encryptedParams = "symbol=H7Vq89sLHpKJBcyPALZ40g1755837626550&password=OyIeEKnApDquWth4uY8RjA1755837626550";
const expectedSignature = "bWluaV8xLjE5OjM5ZmIwYjg2N2U1N2U2MzVlOGExMzQ0MzY0ZjlhMmZjZDQzZWFmOTU=";
const expectedHash = "39fb0b867e57e635e8a1344364f9a2fcd43eaf95";

console.log("时间戳:", timestamp);
console.log("加密后参数:", encryptedParams);
console.log("预期签名:", expectedSignature);
console.log("预期哈希:", expectedHash);

// 解码预期签名验证
const decodedSignature = Buffer.from(expectedSignature, 'base64').toString();
console.log("解码后签名:", decodedSignature);

console.log("\n=== 测试推导的算法 ===");

// 推导的算法: signature = Base64.encode("mini_1.19:" + SHA1("mini_1.19:" + timestamp + 加密后的参数字符串))

// 方法1: 直接拼接
const signString1 = "mini_1.19:" + timestamp + encryptedParams;
const hash1 = CryptoJS.SHA1(signString1).toString();
const signature1 = Base64.encode("mini_1.19:" + hash1);

console.log("方法1 - 直接拼接:");
console.log("  待签名字符串:", signString1);
console.log("  SHA1哈希:", hash1);
console.log("  最终签名:", signature1);
console.log("  匹配:", signature1 === expectedSignature);
console.log("  哈希匹配:", hash1 === expectedHash);

// 方法2: 按照解混淆代码的paramsMerge格式 (key=valuekey=value)
const paramsMerged = "password=OyIeEKnApDquWth4uY8RjA1755837626550symbol=H7Vq89sLHpKJBcyPALZ40g1755837626550";
const signString2 = "mini_1.19:" + timestamp + paramsMerged;
const hash2 = CryptoJS.SHA1(signString2).toString();
const signature2 = Base64.encode("mini_1.19:" + hash2);

console.log("\n方法2 - paramsMerge格式:");
console.log("  合并参数:", paramsMerged);
console.log("  待签名字符串:", signString2);
console.log("  SHA1哈希:", hash2);
console.log("  最终签名:", signature2);
console.log("  匹配:", signature2 === expectedSignature);
console.log("  哈希匹配:", hash2 === expectedHash);

// 方法3: 尝试不同的前缀
const prefixes = [
    "mini_1.19:" + timestamp,
    "mini_1.19" + timestamp,
    timestamp.toString()
];

const paramFormats = [
    encryptedParams,
    paramsMerged,
    "H7Vq89sLHpKJBcyPALZ40g1755837626550OyIeEKnApDquWth4uY8RjA1755837626550", // 只有值
    "OyIeEKnApDquWth4uY8RjA1755837626550H7Vq89sLHpKJBcyPALZ40g1755837626550"  // 交换顺序
];

console.log("\n=== 尝试所有组合 ===");

for (let i = 0; i < prefixes.length; i++) {
    for (let j = 0; j < paramFormats.length; j++) {
        const testString = prefixes[i] + paramFormats[j];
        const testHash = CryptoJS.SHA1(testString).toString();
        const testSignature = Base64.encode("mini_1.19:" + testHash);
        
        if (testHash === expectedHash || testSignature === expectedSignature) {
            console.log(`\n*** 找到匹配！***`);
            console.log(`前缀${i+1}: "${prefixes[i]}"`);
            console.log(`参数${j+1}: "${paramFormats[j]}"`);
            console.log(`完整字符串: "${testString}"`);
            console.log(`SHA1哈希: ${testHash}`);
            console.log(`最终签名: ${testSignature}`);
            console.log(`哈希匹配: ${testHash === expectedHash}`);
            console.log(`签名匹配: ${testSignature === expectedSignature}`);
        }
    }
}

// 方法4: 尝试MD5
console.log("\n=== 尝试MD5算法 ===");
const md5Hash1 = CryptoJS.MD5(signString1).toString();
const md5Signature1 = Base64.encode("mini_1.19:" + md5Hash1);

console.log("MD5哈希:", md5Hash1);
console.log("MD5签名:", md5Signature1);
console.log("MD5哈希匹配:", md5Hash1 === expectedHash);
console.log("MD5签名匹配:", md5Signature1 === expectedSignature);
