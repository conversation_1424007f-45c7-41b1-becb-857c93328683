.privacy-modal {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    -webkit-align-items: center;
    align-items: center;
    background: rgba(0,0,0,.6);
    bottom: 0;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 9
}

.privacy-modal .privacy-modal-ctr {
    width: 560rpx
}

.privacy-modal .privacy-modal-ctr .privacy-modal-top {
    background-color: #fff;
    background-size: 100% 100%;
    border-radius: 24rpx 24rpx 0 0;
    height: 48rpx;
    width: 100%
}

.privacy-modal .privacy-modal-ctr .privacy-modal-content {
    background: #fff;
    border-radius: 0 0 24rpx 24rpx;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin-top: -4rpx;
    padding: 8rpx 40rpx 40rpx
}

.privacy-modal .privacy-modal-ctr .privacy-modal-content .privacy-modal-content-title {
    color: #333;
    font-family: PingFangSC-Medium,PingFang SC;
    font-size: 32rpx;
    font-weight: 500;
    line-height: 44rpx;
    margin-bottom: 24rpx;
    text-align: center
}

.privacy-modal .privacy-modal-ctr .privacy-modal-content .privacy-modal-content-text {
    color: #666;
    font-family: PingFangSC-Regular,PingFang SC;
    font-size: 28rpx;
    font-weight: 400;
    line-height: 40rpx
}

.privacy-modal .privacy-modal-ctr .privacy-modal-content .privacy-modal-content-text .privacy-btn {
    color: #fe8b01
}

.privacy-modal .privacy-modal-ctr .privacy-modal-content .privacy-modal-content-btns {
    -ms-flex-pack: justify;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin-top: 48rpx
}

.privacy-modal .privacy-modal-ctr .privacy-modal-content .privacy-modal-content-btns .privacy-modal-content-btn {
    background: -webkit-linear-gradient(135deg,#ff3627,#ff664f);
    background: linear-gradient(315deg,#ff3627,#ff664f);
    border-radius: 40rpx;
    color: #fff;
    height: 80rpx;
    line-height: 80rpx;
    width: 224rpx
}

.privacy-modal .privacy-modal-ctr .privacy-modal-content .privacy-modal-content-btns .privacy-modal-content-btn-disagree {
    background: hsla(0,0%,60%,.1);
    border-radius: 40rpx;
    color: #333;
    height: 80rpx;
    line-height: 80rpx;
    width: 224rpx
}

.privacy-modal .privacy-modal-ctr .privacy-modal-content .privacy-modal-content-btns .privacy-modal-content-btn-disagree::after {
    border: none
}

.privacy-modal .logo-color {
    color: #ff3627
}

.login {
    background: #fff
}

.login__logo-container {
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    margin-top: 148rpx;
    width: 100%
}

.login__logo-img {
    height: 112rpx;
    width: 112rpx
}

.login__logo-title {
    color: #333;
    font-family: PingFangSC-Medium,PingFang SC;
    font-size: 28rpx;
    font-weight: 500;
    height: 28rpx;
    margin-top: 24rpx
}

.login__btn {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    margin-top: 200rpx
}

.login__btn,.login__btn-common {
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}

.login__btn-common {
    -ms-flex-pack: center;
    background: -webkit-linear-gradient(135deg,#ff3627,#ff664f);
    background: linear-gradient(315deg,#ff3627,#ff664f);
    border-radius: 44rpx;
    color: #fff;
    font-family: PingFangSC-Medium,PingFang SC;
    font-size: 30rpx;
    font-weight: 500;
    height: 88rpx;
    -webkit-justify-content: center;
    justify-content: center;
    width: 606rpx
}

.login__btn-common::after {
    content: none
}

.login__btn-phone {
    background: #fff;
    border: 2rpx solid #d6d6d6;
    color: #333
}

.login__btn-wechat {
    background: -webkit-linear-gradient(135deg,#ff3627,#ff664f);
    background: linear-gradient(315deg,#ff3627,#ff664f);
    color: #fff;
    margin-bottom: 40rpx
}

.login__protocol {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin-top: 48rpx;
    padding: 0 72rpx
}
