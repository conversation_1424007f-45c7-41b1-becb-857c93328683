(wx.webpackJsonp = wx.webpackJsonp || []).push([
  [2107], {
    5045: function(e, t, n) {
      "use strict";
      n.d(t, {
        Ox: function() {
          return q
        }
      });
      var r = n(6666),
        i = n(6234),
        o = n(9249),
        a = n(7371),
        u = n(4826),
        s = n(5754),
        c = n(8079),
        l = n(3295),
        d = n(4886),
        f = {
          PageContext: l.kT,
          R: l.kT
        },
        h = "taro-app";

      function p(e, t) {
        var n, r = t.prototype;
        return !(null === (n = t.displayName) || void 0 === n ? void 0 : n.includes("Connect")) && ((0, l.mf)(t.render) || !!(null == r ? void 0 : r.isReactComponent) || r instanceof e.Component)
      }

      function v(e) {
        return e.writable = !0, e.enumerable = !0, e
      }

      function m(e) {
        d.Current.router = Object.assign({
          params: null == e ? void 0 : e.query
        }, e)
      }
      var g, b, y, k = function(e) {
          return function(t) {
            var n = f.R,
              r = f.PageContext,
              i = n.useContext(r) || h,
              o = n.useRef(),
              a = n.useRef(t);
            a.current !== t && (a.current = t), n.useLayoutEffect((function() {
              var t = o.current = (0, d.getPageInstance)(i),
                n = !1;
              t || (n = !0, o.current = Object.create(null), t = o.current);
              var r = function() {
                return a.current.apply(a, arguments)
              };
              return (0, l.mf)(t[e]) ? t[e] = [t[e], r] : t[e] = [].concat((0, c.Z)(t[e] || []), [r]), n && (0, d.injectPageInstance)(t, i),
                function() {
                  var t = o.current;
                  if (t) {
                    var n = t[e];
                    n === r ? t[e] = void 0 : (0, l.kJ)(n) && (t[e] = n.filter((function(e) {
                      return e !== r
                    }))), o.current = void 0
                  }
                }
            }), [])
          }
        },
        w = k("componentDidHide"),
        T = k("componentDidShow"),
        C = k("onError"),
        E = k("onUnhandledRejection"),
        S = k("onLaunch"),
        P = k("onPageNotFound"),
        x = k("onLoad"),
        I = k("onPageScroll"),
        N = k("onPullDownRefresh"),
        A = k("onPullIntercept"),
        _ = k("onReachBottom"),
        O = k("onResize"),
        L = k("onUnload"),
        R = k("onAddToFavorites"),
        Z = k("onOptionMenuClick"),
        D = k("onSaveExitState"),
        B = k("onShareAppMessage"),
        M = k("onShareTimeline"),
        j = k("onTitleClick"),
        U = k("onReady"),
        F = k("onTabItemTap"),
        H = Object.freeze({
          __proto__: null,
          useAddToFavorites: R,
          useDidHide: w,
          useDidShow: T,
          useError: C,
          useLaunch: S,
          useLoad: x,
          useOptionMenuClick: Z,
          usePageNotFound: P,
          usePageScroll: I,
          usePullDownRefresh: N,
          usePullIntercept: A,
          useReachBottom: _,
          useReady: U,
          useResize: O,
          useRouter: function() {
            var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0],
              t = f.R;
            return e ? d.Current.router : t.useMemo((function() {
              return d.Current.router
            }), [])
          },
          useSaveExitState: D,
          useScope: function() {},
          useShareAppMessage: B,
          useShareTimeline: M,
          useTabItemTap: F,
          useTitleClick: j,
          useUnhandledRejection: E,
          useUnload: L
        }),
        W = (0, d.incrementId)(),
        V = (0, l.gl)();

      function z(e) {
        l.PT.tap("getLifecycle", (function(e, t) {
          return e[t = t.replace(/^on(Show|Hide)$/, "componentDid$1")]
        })), l.PT.tap("modifyMpEvent", (function(e) {
          Object.defineProperty(e, "type", {
            value: e.type.replace(/-/g, "")
          })
        })), l.PT.tap("batchedEventUpdates", (function(t) {
          e.unstable_batchedUpdates(t)
        })), l.PT.tap("mergePageInstance", (function(e, t) {
          e && t && ("constructor" in e || Object.keys(e).forEach((function(n) {
            var r = e[n],
              i = function(e) {
                return (0, l.kJ)(e) ? e : e ? [e] : []
              }(t[n]);
            t[n] = i.concat(r)
          })))
        })), V && (l.PT.tap("createPullDownComponent", (function(e, t, n, r) {
          var i = p(n, e);
          return n.forwardRef((function(t, n) {
            var o = Object.assign({}, t),
              a = i ? {
                ref: n
              } : {
                forwardedRef: n,
                reactReduxForwardedRef: n
              };
            return g(r || "taro-pull-to-refresh", null, g(e, Object.assign(Object.assign({}, o), a)))
          }))
        })), l.PT.tap("getDOMNode", (function(t) {
          return e.findDOMNode(t)
        })))
      }

      function G(e, t) {
        return function(n) {
          var r = function(e) {
              return e && (0, d.injectPageInstance)(e, t)
            },
            i = p(e, n) ? {
              ref: r
            } : {
              forwardedRef: r,
              reactReduxForwardedRef: r
            };
          return f.PageContext === l.kT && (f.PageContext = e.createContext("")),
            function(e) {
              function r() {
                var e;
                return (0, o.Z)(this, r), (e = (0, u.Z)(this, r, arguments)).state = {
                  hasError: !1
                }, e
              }
              return (0, s.Z)(r, e), (0, a.Z)(r, [{
                key: "componentDidCatch",
                value: function(e, t) {}
              }, {
                key: "render",
                value: function() {
                  var e = this.state.hasError ? [] : g(f.PageContext.Provider, {
                    value: t
                  }, g(n, Object.assign(Object.assign({}, this.props), i)));
                  return V ? g("div", {
                    id: t,
                    className: "taro_page"
                  }, e) : g("root", {
                    id: t
                  }, e)
                }
              }], [{
                key: "getDerivedStateFromError",
                value: function(e) {
                  var t, n;
                  return null === (n = null === (t = d.Current.app) || void 0 === t ? void 0 : t.onError) || void 0 === n || n.call(t, e.message + e.stack), {
                    hasError: !0
                  }
                }
              }])
            }(e.Component)
        }
      }

      function q(e, t, n, c) {
        f.R = t, g = t.createElement, b = n, y = t.Fragment;
        var k, w, T = t.createRef(),
          C = p(t, e),
          E = new Promise((function(e) {
            return w = e
          }));

        function S() {
          return T.current
        }

        function P(e) {
          k ? e() : E.then((function() {
            return e()
          }))
        }

        function x() {
          var e, n, r = "app";
          V && (r = (null == c ? void 0 : c.appId) || r);
          var i = d.document.getElementById(r);
          if ((t.version || "").startsWith("18")) {
            var o = b.createRoot(i);
            null === (e = o.render) || void 0 === e || e.call(o, g(I))
          } else null === (n = b.render) || void 0 === n || n.call(b, g(I), i)
        }
        z(b);
        var I = function(n) {
          function r(e) {
            var t;
            return (0, o.Z)(this, r), (t = (0, u.Z)(this, r, [e])).pages = [], t.elements = [], k = t, w(t), t
          }
          return (0, s.Z)(r, n), (0, a.Z)(r, [{
            key: "mount",
            value: function(e, n, r) {
              var i = G(t, n)(e),
                o = n + W();
              this.pages.push((function() {
                return g(i, {
                  key: o,
                  tid: n
                })
              })), this.forceUpdate(r)
            }
          }, {
            key: "unmount",
            value: function(e, t) {
              var n = this.elements,
                r = n.findIndex((function(t) {
                  return t.props.tid === e
                }));
              n.splice(r, 1), this.forceUpdate(t)
            }
          }, {
            key: "render",
            value: function() {
              for (var t = this.pages, n = this.elements; t.length > 0;) {
                var r = t.pop();
                n.push(r())
              }
              var i = null;
              return C && (i = {
                ref: T
              }), g(e, i, V ? g(null != y ? y : "div", null, n.slice()) : n.slice())
            }
          }])
        }(t.Component);
        V || x();
        var N = (0, i.Z)(l.PT.call("getMiniLifecycleImpl").app, 3),
          A = N[0],
          _ = N[1],
          O = N[2],
          L = Object.create({
            render: function(e) {
              k.forceUpdate(e)
            },
            mount: function(e, t, n) {
              k ? k.mount(e, t, n) : E.then((function(r) {
                return r.mount(e, t, n)
              }))
            },
            unmount: function(e, t) {
              k.unmount(e, t)
            }
          }, (0, r.Z)((0, r.Z)((0, r.Z)((0, r.Z)((0, r.Z)((0, r.Z)({
            config: v({
              configurable: !0,
              value: c
            })
          }, A, v({
            value: function(e) {
              var t = this;
              m(e), V && x(), P((function() {
                var n, r = S();
                if (t.$app = r, r) {
                  if (r.taroGlobalData) {
                    var i = r.taroGlobalData,
                      o = Object.keys(i),
                      a = Object.getOwnPropertyDescriptors(i);
                    o.forEach((function(e) {
                      Object.defineProperty(t, e, {
                        configurable: !0,
                        enumerable: !0,
                        get: function() {
                          return i[e]
                        },
                        set: function(t) {
                          i[e] = t
                        }
                      })
                    })), Object.defineProperties(t, a)
                  }
                  null === (n = r.onLaunch) || void 0 === n || n.call(r, e)
                }
                R("onLaunch", e)
              }))
            }
          })), _, v({
            value: function(e) {
              m(e), P((function() {
                var t, n = S();
                null === (t = null == n ? void 0 : n.componentDidShow) || void 0 === t || t.call(n, e), R("onShow", e)
              }))
            }
          })), O, v({
            value: function() {
              P((function() {
                var e, t = S();
                null === (e = null == t ? void 0 : t.componentDidHide) || void 0 === e || e.call(t), R("onHide")
              }))
            }
          })), "onError", v({
            value: function(e) {
              P((function() {
                var t, n = S();
                null === (t = null == n ? void 0 : n.onError) || void 0 === t || t.call(n, e), R("onError", e)
              }))
            }
          })), "onUnhandledRejection", v({
            value: function(e) {
              P((function() {
                var t, n = S();
                null === (t = null == n ? void 0 : n.onUnhandledRejection) || void 0 === t || t.call(n, e), R("onUnhandledRejection", e)
              }))
            }
          })), "onPageNotFound", v({
            value: function(e) {
              P((function() {
                var t, n = S();
                null === (t = null == n ? void 0 : n.onPageNotFound) || void 0 === t || t.call(n, e), R("onPageNotFound", e)
              }))
            }
          })));

        function R(e) {
          for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), r = 1; r < t; r++) n[r - 1] = arguments[r];
          var i = (0, d.getPageInstance)(h);
          if (i) {
            var o = S(),
              a = l.PT.call("getLifecycle", i, e);
            Array.isArray(a) && a.forEach((function(e) {
              return e.apply(o, n)
            }))
          }
        }
        return d.Current.app = L, L
      }(0, d.incrementId)(), l.PT.tap("initNativeApi", (function(e) {
        for (var t in H) e[t] = H[t]
      }))
    },
    3675: function(e, t, n) {
      "use strict";
      n.d(t, {
        zx: function() {
          return o
        },
        V1: function() {
          return f
        },
        Xz: function() {
          return m
        },
        Ee: function() {
          return h
        },
        II: function() {
          return a
        },
        Gf: function() {
          return p
        },
        gw: function() {
          return b
        },
        Y8: function() {
          return u
        },
        pf: function() {
          return c
        },
        tq: function() {
          return l
        },
        t3: function() {
          return d
        },
        xv: function() {
          return i
        },
        gx: function() {
          return s
        },
        nk: function() {
          return v
        },
        G7: function() {
          return r
        },
        kh: function() {
          return g
        }
      });
      var r = "view",
        i = "text",
        o = "button",
        a = "input",
        u = "radio",
        s = "textarea",
        c = "scroll-view",
        l = "swiper",
        d = "swiper-item",
        f = "camera",
        h = "image",
        p = "live-player",
        v = "video",
        m = "canvas",
        g = "web-view",
        b = "live-pusher"
    },
    5743: function(e, t, n) {
      "use strict";
      var r = n(3295),
        i = new Set(["addFileToFavorites", "addVideoToFavorites", "authPrivateMessage", "checkIsAddedToMyMiniProgram", "chooseContact", "cropImage", "disableAlertBeforeUnload", "editImage", "enableAlertBeforeUnload", "getBackgroundFetchData", "getChannelsLiveInfo", "getChannelsLiveNoticeInfo", "getFuzzyLocation", "getGroupEnterInfo", "getLocalIPAddress", "getShareInfo", "getUserProfile", "getWeRunData", "join1v1Chat", "openChannelsActivity", "openChannelsEvent", "openChannelsLive", "openChannelsUserProfile", "openCustomerServiceChat", "openVideoEditor", "saveFileToDisk", "scanItem", "setEnable1v1Chat", "setWindowSize", "sendBizRedPacket", "startFacialRecognitionVerify"]),
        o = "true",
        a = "false",
        u = "",
        s = "0",
        c = {
          Progress: {
            "border-radius": s,
            "font-size": "16",
            duration: "30",
            bindActiveEnd: u
          },
          RichText: {
            space: u,
            "user-select": a
          },
          Text: {
            "user-select": a
          },
          Map: {
            polygons: "[]",
            subkey: u,
            rotate: s,
            skew: s,
            "max-scale": "20",
            "min-scale": "3",
            "enable-3D": a,
            "show-compass": a,
            "show-scale": a,
            "enable-overlooking": a,
            "enable-zoom": o,
            "enable-scroll": o,
            "enable-rotate": a,
            "enable-satellite": a,
            "enable-traffic": a,
            "enable-poi": o,
            "enable-building": o,
            setting: "[]",
            bindLabelTap: u,
            bindRegionChange: u,
            bindPoiTap: u,
            bindAnchorPointTap: u
          },
          Button: {
            lang: "en",
            "session-from": u,
            "send-message-title": u,
            "send-message-path": u,
            "send-message-img": u,
            "app-parameter": u,
            "show-message-card": a,
            "business-id": u,
            bindGetUserInfo: u,
            bindContact: u,
            bindGetPhoneNumber: u,
            bindGetRealTimePhoneNumber: u,
            bindChooseAvatar: u,
            bindError: u,
            bindOpenSetting: u,
            bindLaunchApp: u,
            bindAgreePrivacyAuthorization: u
          },
          Form: {
            "report-submit-timeout": s
          },
          Input: {
            "always-embed": a,
            "adjust-position": o,
            "hold-keyboard": a,
            "safe-password-cert-path": "",
            "safe-password-length": "",
            "safe-password-time-stamp": "",
            "safe-password-nonce": "",
            "safe-password-salt": "",
            "safe-password-custom-hash": "",
            "auto-fill": u,
            bindKeyboardHeightChange: u,
            bindNicknameReview: u
          },
          Picker: {
            "header-text": u,
            level: "region"
          },
          PickerView: {
            "immediate-change": a,
            bindPickStart: u,
            bindPickEnd: u
          },
          Slider: {
            color: "'#e9e9e9'",
            "selected-color": "'#1aad19'"
          },
          Textarea: {
            "show-confirm-bar": o,
            "adjust-position": o,
            "hold-keyboard": a,
            "disable-default-padding": a,
            "confirm-type": "'return'",
            "confirm-hold": a,
            bindKeyboardHeightChange: u
          },
          ScrollView: {
            type: "'list'",
            "event-passive": a,
            "enable-flex": a,
            "scroll-anchoring": a,
            "refresher-enabled": a,
            "refresher-threshold": "45",
            "refresher-default-style": "'black'",
            "refresher-background": "'#FFF'",
            "refresher-triggered": a,
            enhanced: a,
            bounces: o,
            "show-scrollbar": o,
            "paging-enabled": a,
            "fast-deceleration": a,
            reverse: a,
            "cache-extent": s,
            "scroll-into-view-within-extent": a,
            "scroll-into-view-alignment": "'start'",
            bindDragStart: u,
            bindDragging: u,
            bindDragEnd: u,
            bindRefresherPulling: u,
            bindRefresherRefresh: u,
            bindRefresherRestore: u,
            bindRefresherAbort: u,
            bindScrollStart: u,
            bindScrollEnd: u,
            bindRefresherWillRefresh: u
          },
          StickySection: {
            "push-pinned-header": o
          },
          GridView: {
            type: "'aligned'",
            "cross-axis-count": "2",
            "max-cross-axis-extent": s,
            "main-axis-gap": s,
            "cross-axis-gap": s
          },
          ListView: {},
          StickyHeader: {},
          Swiper: {
            "snap-to-edge": a,
            "easing-function": "'default'"
          },
          SwiperItem: {
            "skip-hidden-item-layout": a
          },
          Navigator: {
            target: "'self'",
            "app-id": u,
            path: u,
            "extra-data": u,
            version: "'version'"
          },
          Camera: {
            mode: "'normal'",
            resolution: "'medium'",
            "frame-size": "'medium'",
            bindInitDone: u,
            bindScanCode: u
          },
          Image: {
            webp: a,
            "show-menu-by-longpress": a
          },
          LivePlayer: {
            mode: "'live'",
            "sound-mode": "'speaker'",
            "auto-pause-if-navigate": o,
            "auto-pause-if-open-native": o,
            "picture-in-picture-mode": "[]",
            bindstatechange: u,
            bindfullscreenchange: u,
            bindnetstatus: u,
            bindAudioVolumeNotify: u,
            bindEnterPictureInPicture: u,
            bindLeavePictureInPicture: u
          },
          Video: {
            title: u,
            "play-btn-position": "'bottom'",
            "enable-play-gesture": a,
            "auto-pause-if-navigate": o,
            "auto-pause-if-open-native": o,
            "vslide-gesture": a,
            "vslide-gesture-in-fullscreen": o,
            "ad-unit-id": u,
            "poster-for-crawler": u,
            "show-casting-button": a,
            "picture-in-picture-mode": "[]",
            "enable-auto-rotation": a,
            "show-screen-lock-button": a,
            "show-snapshot-button": a,
            "show-background-playback-button": a,
            "background-poster": u,
            bindProgress: u,
            bindLoadedMetadata: u,
            bindControlsToggle: u,
            bindEnterPictureInPicture: u,
            bindLeavePictureInPicture: u,
            bindSeekComplete: u,
            bindAdLoad: u,
            bindAdError: u,
            bindAdClose: u,
            bindAdPlay: u
          },
          Canvas: {
            type: u
          },
          Ad: {
            "ad-type": "'banner'",
            "ad-theme": "'white'"
          },
          CoverView: {
            "marker-id": u,
            slot: u
          },
          Editor: {
            "read-only": a,
            placeholder: u,
            "show-img-size": a,
            "show-img-toolbar": a,
            "show-img-resize": a,
            focus: a,
            bindReady: u,
            bindFocus: u,
            bindBlur: u,
            bindInput: u,
            bindStatusChange: u,
            name: u
          },
          MatchMedia: {
            "min-width": u,
            "max-width": u,
            width: u,
            "min-height": u,
            "max-height": u,
            height: u,
            orientation: u
          },
          FunctionalPageNavigator: {
            version: "'release'",
            name: u,
            args: u,
            bindSuccess: u,
            bindFail: u,
            bindCancel: u
          },
          LivePusher: {
            url: u,
            mode: "'RTC'",
            autopush: a,
            muted: a,
            "enable-camera": o,
            "auto-focus": o,
            orientation: "'vertical'",
            beauty: s,
            whiteness: s,
            aspect: "'9:16'",
            "min-bitrate": "200",
            "max-bitrate": "1000",
            "audio-quality": "'high'",
            "waiting-image": u,
            "waiting-image-hash": u,
            zoom: a,
            "device-position": "'front'",
            "background-mute": a,
            mirror: a,
            "remote-mirror": a,
            "local-mirror": a,
            "audio-reverb-type": s,
            "enable-mic": o,
            "enable-agc": a,
            "enable-ans": a,
            "audio-volume-type": "'voicecall'",
            "video-width": "360",
            "video-height": "640",
            "beauty-style": "'smooth'",
            filter: "'standard'",
            animation: u,
            bindStateChange: u,
            bindNetStatus: u,
            bindBgmStart: u,
            bindBgmProgress: u,
            bindBgmComplete: u,
            bindAudioVolumeNotify: u
          },
          OfficialAccount: {
            bindLoad: u,
            bindError: u
          },
          OpenData: {
            type: u,
            "open-gid": u,
            lang: "'en'",
            "default-text": u,
            "default-avatar": u,
            bindError: u
          },
          NavigationBar: {
            title: u,
            loading: a,
            "front-color": u,
            "background-color": u,
            "color-animation-duration": s,
            "color-animation-timing-func": "'linear'"
          },
          PageMeta: {
            "background-text-style": u,
            "background-color": u,
            "background-color-top": u,
            "background-color-bottom": u,
            "scroll-top": "''",
            "scroll-duration": "300",
            "page-style": "''",
            "root-font-size": "''",
            bindResize: u,
            bindScroll: u,
            bindScrollDone: u
          },
          VoipRoom: {
            openid: u,
            mode: "'camera'",
            "device-position": "'front'",
            bindError: u
          },
          AdCustom: {
            "unit-id": u,
            "ad-intervals": u,
            bindLoad: u,
            bindError: u
          },
          PageContainer: {
            show: a,
            duration: "300",
            "z-index": "100",
            overlay: o,
            position: "'bottom'",
            round: a,
            "close-on-slide-down": a,
            "overlay-style": u,
            "custom-style": u,
            bindBeforeEnter: u,
            bindEnter: u,
            bindAfterEnter: u,
            bindBeforeLeave: u,
            bindLeave: u,
            bindAfterLeave: u,
            bindClickOverlay: u
          },
          ShareElement: {
            mapkey: u,
            transform: a,
            duration: "300",
            "easing-function": "'ease-out'"
          },
          KeyboardAccessory: {},
          RootPortal: {},
          ChannelLive: {
            feedId: u,
            finderUserName: u
          },
          ChannelVideo: {
            feedId: u,
            finderUserName: u,
            autoplay: a,
            loop: a,
            muted: a,
            objectFit: "'contain'",
            bindError: u
          }
        },
        l = {
          initNativeApi: function(e) {
            (0, r.Ig)(e, wx, {
              needPromiseApis: i,
              modifyApis: function(e) {
                e.delete("lanDebug")
              },
              transformMeta: function(e, t) {
                var n;
                return "showShareMenu" === e && (t.menus = null === (n = t.showShareItems) || void 0 === n ? void 0 : n.map((function(e) {
                  return "wechatFriends" === e ? "shareAppMessage" : "wechatMoment" === e ? "shareTimeline" : e
                }))), {
                  key: e,
                  options: t
                }
              }
            }), e.cloud = wx.cloud, e.getTabBar = function(e) {
              var t;
              if ("function" == typeof(null == e ? void 0 : e.getTabBar)) return null === (t = e.getTabBar()) || void 0 === t ? void 0 : t.$taroInstances
            }, e.getRenderer = function() {
              var t, n, r;
              return null !== (r = null === (n = null === (t = e.getCurrentInstance()) || void 0 === t ? void 0 : t.page) || void 0 === n ? void 0 : n.renderer) && void 0 !== r ? r : "webview"
            }
          },
          getMiniLifecycle: function(e) {
            var t = e.page[5];
            return -1 === t.indexOf("onSaveExitState") && t.push("onSaveExitState"), e
          }
        };
      (0, r.xi)(l), (0, r.ku)(c)
    },
    8424: function(e, t, n) {
      "use strict";
      n.d(t, {
        ZP: function() {
          return z
        }
      });
      var r = n(9249),
        i = n(7371),
        o = n(6522),
        a = n(3295),
        u = n(4886),
        s = n(7962),
        c = n.n(s),
        l = n(1801),
        d = {
          color: !0,
          date: !0,
          datetime: !0,
          "datetime-local": !0,
          email: !0,
          month: !0,
          number: !0,
          password: !0,
          range: !0,
          search: !0,
          tel: !0,
          text: !0,
          time: !0,
          url: !0,
          week: !0
        };

      function f(e) {
        switch (e) {
          case "cancel":
          case "click":
          case "close":
          case "contextmenu":
          case "copy":
          case "cut":
          case "dragend":
          case "dragstart":
          case "drop":
          case "input":
          case "paste":
          case "pause":
          case "play":
          case "pointercancel":
          case "pointerdown":
          case "pointerup":
          case "reset":
          case "resize":
          case "submit":
          case "touchcancel":
          case "touchend":
          case "touchstart":
          case "change":
          case "blur":
          case "focus":
          case "select":
          case "selectstart":
            return 1;
          case "drag":
          case "dragenter":
          case "dragexit":
          case "dragleave":
          case "dragover":
          case "pointermove":
          case "pointerout":
          case "pointerover":
          case "scroll":
          case "toggle":
          case "touchmove":
          case "pointerenter":
          case "pointerleave":
            return 4;
          default:
            return 16
        }
      }
      var h = Math.random().toString(36).slice(2),
        p = "__reactProps$" + h,
        v = "__reactFiber$" + h,
        m = "__reactContainer$" + h;

      function g(e, t) {
        t[v] = e
      }

      function b(e, t) {
        t[m] = e
      }

      function y(e) {
        var t = e[v] || e[m];
        return !t || 5 !== t.tag && 6 !== t.tag && 13 !== t.tag && 3 !== t.tag ? null : t
      }

      function k(e, t) {
        e[p] = t
      }

      function w(e) {
        return "function" == typeof e || "symbol" === (0, o.Z)(e) ? "" : e
      }

      function T(e) {
        return "" + e
      }

      function C(e, t, n) {
        ! function(e, t, n) {
          var r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : "string";
          null != n ? "number" === r ? (0 === n && "" === e.value || t != n) && (e.value = T(n)) : t !== T(n) && (e.value = T(n)) : "submit" !== r && "reset" !== r || e.removeAttribute("value")
        }(e, t, w(n.value), n.type)
      }
      var E = C,
        S = function(e, t, n) {
          var r = e;
          null == n.checked ? (C(e, t, n), function(e, t) {
            var n = t.name;
            "radio" === t.type && null != n && console.warn("radio updateNamedCousins 未实现", e, t)
          }(e, n)) : console.warn("updateCheck 未实现", r)
        };

      function P(e) {
        return e._valueTracker
      }

      function x(e) {
        P(e) || (e._valueTracker = function(e) {
          var t = function(e) {
              var t = e.type,
                n = e.nodeName;
              return n && "input" === n.toLowerCase() && ("checkbox" === t || "radio" === t)
            }(e) ? "checked" : "value",
            n = Object.getOwnPropertyDescriptor(e.constructor.prototype, t),
            r = "" + e[t];
          if (!e.hasOwnProperty(t) && void 0 !== n && "function" == typeof n.get && "function" == typeof n.set) {
            var i = n.get,
              o = n.set;
            return Object.defineProperty(e, t, {
              configurable: !0,
              enumerable: n.enumerable,
              get: function() {
                return i.call(this)
              },
              set: function(e) {
                r = "" + e, o.call(this, e)
              }
            }), {
              getValue: function() {
                return r
              },
              setValue: function(e) {
                r = "" + e
              },
              stopTracking: function() {
                (function(e) {
                  e._valueTracker = null
                })(e), delete e[t]
              }
            }
          }
        }(e))
      }
      var I = /aspect|acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;

      function N(e, t, n) {
        for (var r = 0; r < n.length; r += 2) {
          var i = n[r];
          O(e, i, n[r + 1], t[i])
        }
      }

      function A(e, t, n) {
        var r, i = null;
        for (r in t) r in n || (i = i || []).push(r, null);
        var o = e instanceof u.FormElement;
        for (r in n)(t[r] !== n[r] || o && "value" === r) && (i = i || []).push(r, n[r]);
        return i
      }

      function _(e, t, n) {
        "-" !== t[0] ? e[t] = (0, a.hj)(n) && !1 === I.test(t) ? n + "px" : null == n ? "" : n : e.setProperty(t, n.toString())
      }

      function O(e, t, n, r) {
        var i, o;
        if ("key" === (t = "className" === t ? "class" : t) || "children" === t || "ref" === t);
        else if ("style" === t) {
          var u = e.style;
          if ((0, a.HD)(n)) u.cssText = n;
          else {
            if ((0, a.HD)(r) && (u.cssText = "", r = null), (0, a.Kn)(r))
              for (var s in r) n && s in n || _(u, s, "");
            if ((0, a.Kn)(n))
              for (var c in n) r && n[c] === r[c] || _(u, c, n[c])
          }
        } else if (function(e) {
            return "o" === e[0] && "n" === e[1]
          }(t)) ! function(e, t, n, r) {
          var i = t.endsWith("Capture"),
            o = t.toLowerCase().slice(2);
          i && (o = o.slice(0, -7));
          var u = (0, a.kC)((0, a.CA)(e.tagName.toLowerCase()));
          "click" === o && u in a.rD && (o = "tap"), (0, a.mf)(n) ? r ? (e.removeEventListener(o, r, !1), e.addEventListener(o, n, {
            isCapture: i,
            sideEffect: !1
          })) : e.addEventListener(o, n, i) : e.removeEventListener(o, r)
        }(e, t, n, r);
        else if ("dangerouslySetInnerHTML" === t) {
          var l = null !== (i = null == n ? void 0 : n.__html) && void 0 !== i ? i : "",
            d = null !== (o = null == r ? void 0 : r.__html) && void 0 !== o ? o : "";
          (l || d) && d !== l && (e.innerHTML = l)
        } else(0, a.mf)(n) || (null == n ? e.removeAttribute(t) : e.setAttribute(t, n))
      }
      var L = {
          getPublicInstance: function(e) {
            return e
          },
          getRootHostContext: function() {
            return {}
          },
          getChildHostContext: function(e) {
            return e
          },
          prepareForCommit: function() {
            return null
          },
          resetAfterCommit: a.ZT,
          createInstance: function(e, t, n, r, i) {
            var o = u.document.createElement(e);
            return g(i, o), k(o, t), o
          },
          appendInitialChild: function(e, t) {
            e.appendChild(t)
          },
          finalizeInitialChildren: function(e, t, n) {
            return function(e, t, n) {
              var r = A(e, t, n);
              r && N(e, t, r)
            }(e, {}, n), "input" !== t && "textarea" !== t || x(e), !1
          },
          prepareUpdate: function(e, t, n, r) {
            return A(e, n, r)
          },
          shouldSetTextContent: function() {
            return !1
          },
          createTextInstance: function(e, t, n, r) {
            var i = u.document.createTextNode(e);
            return g(r, i), i
          },
          scheduleTimeout: setTimeout,
          cancelTimeout: clearTimeout,
          noTimeout: -1,
          isPrimaryRenderer: !0,
          warnsIfNotActing: !0,
          supportsMutation: !0,
          supportsPersistence: !1,
          supportsHydration: !1,
          getInstanceFromNode: function() {
            return null
          },
          beforeActiveInstanceBlur: a.ZT,
          afterActiveInstanceBlur: a.ZT,
          preparePortalMount: a.ZT,
          prepareScopeUpdate: a.ZT,
          getInstanceFromScope: function() {
            return null
          },
          getCurrentEventPriority: function() {
            return l.DefaultEventPriority
          },
          detachDeletedInstance: a.ZT,
          supportsMicrotasks: !0,
          scheduleMicrotask: (0, a.o8)(Promise) ? setTimeout : function(e) {
            return Promise.resolve(null).then(e).catch((function(e) {
              setTimeout((function() {
                throw e
              }))
            }))
          },
          appendChild: function(e, t) {
            e.appendChild(t)
          },
          appendChildToContainer: function(e, t) {
            e.appendChild(t)
          },
          commitTextUpdate: function(e, t, n) {
            e.nodeValue = n
          },
          commitMount: a.ZT,
          commitUpdate: function(e, t, n, r, i) {
            N(e, r, t), k(e, i)
          },
          insertBefore: function(e, t, n) {
            e.insertBefore(t, n)
          },
          insertInContainerBefore: function(e, t, n) {
            e.insertBefore(t, n)
          },
          removeChild: function(e, t) {
            e.removeChild(t)
          },
          removeChildFromContainer: function(e, t) {
            e.removeChild(t)
          },
          resetTextContent: a.ZT,
          hideInstance: function(e) {
            e.style.setProperty("display", "none")
          },
          hideTextInstance: function(e) {
            e.nodeValue = ""
          },
          unhideInstance: function(e, t) {
            var n = t.style,
              r = (null == n ? void 0 : n.hasOwnProperty("display")) ? n.display : null;
            r = null == r || (0, a.jn)(r) || "" === r ? "" : ("" + r).trim(), e.style.display = r
          },
          unhideTextInstance: function(e, t) {
            e.nodeValue = t
          },
          clearContainer: function(e) {
            e.childNodes.length > 0 && (e.textContent = "")
          }
        },
        R = c()(L),
        Z = null;

      function D(e, t) {
        var n, r, i = y(t),
          o = e.type;
        if (i && function(e) {
            var t = e && e.nodeName && e.nodeName.toLowerCase();
            if ("input" === t) {
              var n = e.type;
              return !n || !!d[n]
            }
            return "textarea" === t
          }(t) && ("input" === o || "change" === o)) return function(e, t) {
          var n = function(e) {
            if (5 === e.tag || 6 === e.tag) return e.stateNode
          }(e);
          return !!n && (function(e, t) {
            if (!e) return !1;
            var n = P(e);
            return !n || t !== n.getValue() && (n.setValue(t), !0)
          }(n, t) ? e : void 0)
        }(i, T(null === (r = null === (n = e.mpEvent) || void 0 === n ? void 0 : n.detail) || void 0 === r ? void 0 : r.value))
      }

      function B(e) {
        Z ? Z.push(e) : Z = [e]
      }

      function M() {
        null !== Z && (R.flushSync(), function() {
          if (Z) {
            var e = Z;
            Z = null;
            for (var t = 0; t < e.length; t++) j(e[t])
          }
        }())
      }

      function j(e) {
        var t = y(e.target);
        if (t) {
          var n = t.stateNode,
            r = t.type;
          if (n) {
            var i = function(e) {
              return e[p] || null
            }(n);
            ! function(e, t, n, r) {
              switch (t) {
                case "input":
                  S(e, n, r);
                  break;
                case "textarea":
                  E(e, n, r)
              }
            }(n, r, e.value, i)
          }
        }
      }
      var U = new WeakMap,
        F = (0, i.Z)((function e(t, n, i) {
          (0, r.Z)(this, e), this.renderer = t, this.initInternalRoot(t, n, i)
        }), [{
          key: "initInternalRoot",
          value: function(e, t, n) {
            var r = t;
            if (n) {
              var i = !1,
                o = "",
                a = function(e) {
                  return console.error(e)
                },
                u = null;
              !0 === n.unstable_strictMode && (i = !0), void 0 !== n.identifierPrefix && (o = n.identifierPrefix), void 0 !== n.onRecoverableError && (a = n.onRecoverableError), void 0 !== n.unstable_transitionCallbacks && (u = n.unstable_transitionCallbacks), this.internalRoot = e.createContainer(r, 1, null, i, !1, o, a, u)
            } else this.internalRoot = e.createContainer(r, 0, null, !1, !1, "", (function() {}), null)
          }
        }, {
          key: "render",
          value: function(e, t) {
            var n = this.renderer,
              r = this.internalRoot;
            return n.updateContainer(e, r, null, t), n.getPublicRootInstance(r)
          }
        }, {
          key: "unmount",
          value: function(e) {
            this.renderer.updateContainer(null, this.internalRoot, null, e)
          }
        }]),
        H = !1,
        W = function(e, t) {
          if (H) return e(t);
          H = !0;
          try {
            return R.batchedUpdates(e, t)
          } finally {
            H = !1, M()
          }
        },
        V = (0, a.mf)(Symbol) && Symbol.for ? Symbol.for("react.portal") : 60106,
        z = {
          render: function(e, t, n) {
            var r = U.get(t);
            if (null != r) return r.render(e, n);
            var i = new F(R, t);
            return U.set(t, i), i.render(e, n)
          },
          createRoot: function(e) {
            var t, n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
              r = U.get(e);
            if (null != r) return r;
            var i = new F(R, e, n);
            return U.set(e, i), b(null === (t = null == i ? void 0 : i.internalRoot) || void 0 === t ? void 0 : t.current, e), u.hooks.tap("dispatchTaroEvent", (function(e, t) {
              var n = f(e.type);
              R.runWithPriority(n, (function() {
                t.dispatchEvent(e)
              }))
            })), u.hooks.tap("modifyTaroEvent", (function(e, t) {
              var n, r;
              D(e, t) && B({
                target: t,
                value: null === (r = null === (n = e.mpEvent) || void 0 === n ? void 0 : n.detail) || void 0 === r ? void 0 : r.value
              })
            })), i
          },
          unstable_batchedUpdates: W,
          unmountComponentAtNode: function(e) {
            (0, a.zx)(e && [1, 8, 9, 11].includes(e.nodeType), "unmountComponentAtNode(...): Target container is not a DOM element.");
            var t = U.get(e);
            return !!t && (W((function() {
              t.unmount((function() {
                U.delete(e)
              }))
            }), null), !0)
          },
          findDOMNode: function(e) {
            if (null == e) return null;
            var t = e.nodeType;
            return 1 === t || 3 === t ? e : R.findHostInstance(e)
          },
          createPortal: function(e, t, n) {
            return {
              $$typeof: V,
              key: null == n ? null : String(n),
              children: e,
              containerInfo: t,
              implementation: null
            }
          }
        }
    },
    4230: function(e, t, n) {
      "use strict";
      var r = n(4886);
      Component((0, r.createRecursiveComponentConfig)())
    },
    8770: function(e, t, n) {
      "use strict";
      var r = n(4886);
      Component((0, r.createRecursiveComponentConfig)("custom-wrapper"))
    },
    1924: function(e, t, n) {
      "use strict";
      var r = n(6666),
        i = n(6234),
        o = n(3295);

      function a(e) {
        return function(t, n) {
          var r = t.toLowerCase();
          if (r in e) {
            var i = e[r];
            (0, o.HD)(i) ? t = i: (t = i[0], n = i[1][n] || n)
          }
          return [t, n]
        }
      }
      var u = new Set(["i", "abbr", "select", "acronym", "small", "bdi", "kbd", "strong", "big", "sub", "sup", "br", "mark", "meter", "template", "cite", "object", "time", "code", "output", "u", "data", "picture", "tt", "datalist", "var", "dfn", "del", "q", "em", "s", "embed", "samp", "b"]),
        s = new Set(["body", "svg", "address", "fieldset", "li", "span", "article", "figcaption", "main", "aside", "figure", "nav", "blockquote", "footer", "ol", "details", "p", "dialog", "h1", "h2", "h3", "h4", "h5", "h6", "pre", "dd", "header", "section", "div", "hgroup", "table", "dl", "hr", "ul", "dt", "view", "view-block"]),
        c = new Map([
          ["slot", "slot"],
          ["form", "form"],
          ["iframe", "web-view"],
          ["img", "image"],
          ["audio", "audio"],
          ["video", "video"],
          ["canvas", "canvas"],
          ["a", {
            mapName: function(e) {
              return e.as && (0, o.HD)(e.as) ? e.as.toLowerCase() : !e.href || /^javascript/.test(e.href) ? "view" : "navigator"
            },
            mapNameCondition: ["href"],
            mapAttr: a({
              href: "url",
              target: ["openType", {
                _blank: "navigate",
                _self: "redirect"
              }]
            })
          }],
          ["input", {
            mapName: function(e) {
              return "checkbox" === e.type ? "checkbox" : "radio" === e.type ? "radio" : "input"
            },
            mapNameCondition: ["type"],
            mapAttr: function(e, t, n) {
              var r = e.toLowerCase();
              return "autofocus" === r ? e = "focus" : "readonly" === r ? (!0 === n.disabled && (t = !0), e = "disabled") : "type" === r ? "password" === t ? (e = "password", t = !0) : "tel" === t && (t = "number") : "maxlength" === r && (e = "maxlength"), [e, t]
            }
          }],
          ["label", {
            mapName: "label",
            mapAttr: a({
              htmlfor: "for"
            })
          }],
          ["textarea", {
            mapName: "textarea",
            mapAttr: a({
              autofocus: "focus",
              readonly: "disabled",
              maxlength: "maxlength"
            })
          }],
          ["progress", {
            mapName: "progress",
            mapAttr: function(e, t, n) {
              if ("value" === e) {
                var r = n.max || 1;
                e = "percent", t = Math.round(t / r * 100)
              }
              return [e, t]
            }
          }],
          ["button", {
            mapName: "button",
            mapAttr: function(e, t) {
              return "type" !== e || "submit" !== t && "reset" !== t || (e = "formType"), [e, t]
            }
          }]
        ]);

      function l(e) {
        return !!(u.has(e) || s.has(e) || c.has(e))
      }

      function d(e, t) {
        if (u.has(e)) return "text";
        if (c.has(e)) {
          var n = c.get(e);
          if ((0, o.HD)(n)) return n;
          var r = n.mapName;
          return (0, o.mf)(r) ? r(t) : r
        }
        return "view"
      }

      function f(e) {
        var t = c.get(e);
        if (!(0, o.HD)(t)) return null == t ? void 0 : t.mapAttr
      }

      function h(e, t, n) {
        var r = c.get(e);
        if (r && !(0, o.HD)(r)) {
          var i = r.mapName,
            a = r.mapNameCondition;
          if (a) return a.indexOf(t) > -1 && !(0, o.HD)(i) ? i(n) : void 0
        }
      }

      function p(e, t, n, r) {
        var i = h(e, t, n.props);
        if (i) {
          var o = r[i]._num;
          n.enqueueUpdate({
            path: "".concat(n._path, ".", "nn"),
            value: o
          })
        }
      }

      function v(e) {
        var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "",
          n = t.split(" "),
          r = "h5-".concat(e);
        return -1 === n.indexOf(r) && n.unshift(r), n.join(" ")
      }

      function m(e) {
        var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "",
          n = t,
          r = e.width,
          i = e.height;
        return r && (n = "width: ".concat(r, ";").concat(n)), i && (n = "height: ".concat(i, ";").concat(n)), n
      }

      function g(e, t, n) {
        Object.defineProperty(e, t, {
          enumerable: !0,
          configurable: !0,
          get: function() {
            return e[n]
          },
          set: function(t) {
            e[n] = t
          }
        })
      }
      o.PT.tap("modifyHydrateData", (function(e) {
        var t = e.nn;
        if (l(t)) {
          e.nn = d(t, e);
          var n = f(t);
          if (n)
            for (var o in e) {
              var a = e[o],
                u = n(o, a, e),
                s = (0, i.Z)(u, 2),
                c = s[0],
                h = s[1];
              o !== c ? (delete e[o], e[c] = h) : a !== h && (e[o] = h)
            }
          "br" === t && (e.cn = [(0, r.Z)((0, r.Z)({}, "nn", "#text"), "v", "\n")]), e.cl = v(t, e.cl), e.st = m(e, e.st)
        }
      })), o.PT.tap("modifySetAttrPayload", (function(e, t, n, r) {
        var a = e.nodeName,
          u = e._path,
          s = e.props;
        if (l(a)) {
          p(a, t, e, r);
          var c = r[d(a, s)],
            h = f(a);
          if (h) {
            var g = h(t, n.value, s),
              b = (0, i.Z)(g, 2),
              y = b[0],
              k = b[1];
            n.path = "".concat(u, ".").concat(c[y] || y), n.value = k
          } else c[t] && c[t] !== t && (n.path = "".concat(u, ".").concat((0, o.CA)(c[t])));
          "cl" === t ? n.value = v(a, n.value) : "st" !== t && "width" !== t && "height" !== t || (n.path = "".concat(u, ".", "st"), n.value = m(s, e.style.cssText))
        }
      })), o.PT.tap("modifyRmAttrPayload", (function(e, t, n, r) {
        var a = e.nodeName,
          u = e._path,
          s = e.props;
        if (l(a)) {
          p(a, t, e, r);
          var c = r[d(a, s)],
            h = f(a);
          if (h) {
            var g = h(t, n[t], s),
              b = (0, i.Z)(g, 1)[0];
            n.path = "".concat(u, ".").concat(c[b] || b)
          } else c[t] && c[t] !== t && (n.path = "".concat(u, ".").concat((0, o.CA)(c[t])));
          "cl" === t ? n.value = v(a, n.value) : "st" !== t && "width" !== t && "height" !== t || (n.path = "".concat(u, ".", "st"), n.value = m(s, e.style.cssText))
        }
      })), o.PT.tap("onAddEvent", (function(e, t, n, r) {
        l(r.nodeName) && ("click" === e ? g(r.__handlers, e, "tap") : "input" === r.nodeName && ("change" === e ? "checkbox" === r.props.type || "radio" === r.props.type ? g(r.__handlers, e, "tap") : g(r.__handlers, e, "input") : "keypress" === e && g(r.__handlers, e, "confirm")))
      })), o.PT.tap("modifyTaroEvent", (function(e, t) {
        var n = t.nodeName,
          r = t.props;
        if ("input" === n && "tap" === e.type && ("checkbox" === r.type ? r.checked = !r.checked : "radio" !== r.type || r.checked || (r.checked = !0), e.mpEvent)) {
          var i = e.mpEvent,
            o = i.currentTarget,
            a = i.target;
          o.checked = r.checked, a.id === o.id && (a.checked = r.checked)
        }
      }))
    },
    8685: function(e, t, n) {
      "use strict";
      n.d(t, {
        Ee: function() {
          return a
        },
        II: function() {
          return i
        },
        pf: function() {
          return o
        },
        G7: function() {
          return r
        }
      });
      var r = "view",
        i = "input",
        o = "scroll-view",
        a = "image"
    },
    7482: function(e, t, n) {
      "use strict";
      n.d(t, {
        jz: function() {
          return w
        },
        ZP: function() {
          return T
        },
        mm: function() {
          return y
        }
      });
      var r = n(9249),
        i = n(7371),
        o = n(3295),
        a = n(4886),
        u = n(7962),
        s = n.n(u),
        c = n(4616),
        l = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord/i;

      function d(e, t, n) {
        var r;
        for (r in t) r in n || h(e, r, null, t[r]);
        var i = e instanceof a.FormElement;
        for (r in n)(t[r] !== n[r] || i && "value" === r) && h(e, r, n[r], t[r])
      }

      function f(e, t, n) {
        "-" === t[0] && e.setProperty(t, n.toString()), e[t] = (0, o.hj)(n) && !1 === l.test(t) ? n + "px" : null == n ? "" : n
      }

      function h(e, t, n, r) {
        var i, a;
        if ("key" === (t = "className" === t ? "class" : t) || "children" === t || "ref" === t);
        else if ("style" === t) {
          var u = e.style;
          if ((0, o.HD)(n)) u.cssText = n;
          else {
            if ((0, o.HD)(r) && (u.cssText = "", r = null), (0, o.Kn)(r))
              for (var s in r) n && s in n || f(u, s, "");
            if ((0, o.Kn)(n))
              for (var c in n) r && n[c] === r[c] || f(u, c, n[c])
          }
        } else if (function(e) {
            return "o" === e[0] && "n" === e[1]
          }(t)) ! function(e, t, n, r) {
          var i = t.endsWith("Capture"),
            a = t.toLowerCase().slice(2);
          i && (a = a.slice(0, -7));
          var u = (0, o.kC)((0, o.CA)(e.tagName.toLowerCase()));
          "click" === a && u in o.rD && (a = "tap"), (0, o.mf)(n) ? r ? (e.removeEventListener(a, r, !1), e.addEventListener(a, n, {
            isCapture: i,
            sideEffect: !1
          })) : e.addEventListener(a, n, i) : e.removeEventListener(a, r)
        }(e, t, n, r);
        else if ("dangerouslySetInnerHTML" === t) {
          var l = null !== (i = null == n ? void 0 : n.__html) && void 0 !== i ? i : "",
            d = null !== (a = null == r ? void 0 : r.__html) && void 0 !== a ? a : "";
          (l || d) && d !== l && (e.innerHTML = l)
        } else(0, o.mf)(n) || (null == n ? e.removeAttribute(t) : e.setAttribute(t, n))
      }
      var p = c.unstable_now,
        v = {
          createInstance: function(e) {
            return a.document.createElement(e)
          },
          createTextInstance: function(e) {
            return a.document.createTextNode(e)
          },
          getPublicInstance: function(e) {
            return e
          },
          getRootHostContext: function() {
            return {}
          },
          getChildHostContext: function() {
            return {}
          },
          getCurrentEventPriority: function() {
            return 16
          },
          detachDeletedInstance: function() {},
          appendChild: function(e, t) {
            e.appendChild(t)
          },
          appendInitialChild: function(e, t) {
            e.appendChild(t)
          },
          appendChildToContainer: function(e, t) {
            e.appendChild(t)
          },
          removeChild: function(e, t) {
            e.removeChild(t)
          },
          removeChildFromContainer: function(e, t) {
            e.removeChild(t)
          },
          insertBefore: function(e, t, n) {
            e.insertBefore(t, n)
          },
          insertInContainerBefore: function(e, t, n) {
            e.insertBefore(t, n)
          },
          commitTextUpdate: function(e, t, n) {
            e.nodeValue = n
          },
          finalizeInitialChildren: function(e, t, n) {
            return d(e, {}, n), !1
          },
          prepareUpdate: function() {
            return o.Z6
          },
          commitUpdate: function(e, t, n, r, i) {
            d(e, r, i)
          },
          hideInstance: function(e) {
            e.style.setProperty("display", "none")
          },
          unhideInstance: function(e, t) {
            var n = t.style,
              r = (null == n ? void 0 : n.hasOwnProperty("display")) ? n.display : null;
            r = null == r || (0, o.jn)(r) || "" === r ? "" : ("" + r).trim(), e.style.display = r
          },
          clearContainer: function(e) {
            e.childNodes.length > 0 && (e.textContent = "")
          },
          queueMicrotask: (0, o.o8)(Promise) ? setTimeout : function(e) {
            return Promise.resolve(null).then(e).catch((function(e) {
              setTimeout((function() {
                throw e
              }))
            }))
          },
          shouldSetTextContent: function() {
            return !1
          },
          prepareForCommit: function() {
            return null
          },
          resetAfterCommit: o.ZT,
          commitMount: o.ZT,
          now: p,
          cancelTimeout: clearTimeout,
          scheduleTimeout: setTimeout,
          preparePortalMount: o.ZT,
          noTimeout: -1,
          supportsMutation: !0,
          supportsPersistence: !1,
          isPrimaryRenderer: !0,
          supportsHydration: !1
        },
        m = s()(v),
        g = new WeakMap,
        b = (0, i.Z)((function e(t, n) {
          var i = arguments.length > 2 && void 0 !== arguments[2] && arguments[2];
          (0, r.Z)(this, e), this.renderer = t, this.internalRoot = t.createContainer(n, i ? 1 : 0, !1, null)
        }), [{
          key: "render",
          value: function(e, t) {
            var n = this.renderer,
              r = this.internalRoot;
            return n.updateContainer(e, r, null, t), n.getPublicRootInstance(r)
          }
        }, {
          key: "unmount",
          value: function(e) {
            this.renderer.updateContainer(null, this.internalRoot, null, e)
          }
        }]),
        y = m.batchedUpdates,
        k = (0, o.mf)(Symbol) && Symbol.for ? Symbol.for("react.portal") : 60106;

      function w(e, t, n) {
        return {
          $$typeof: k,
          key: null == n ? null : String(n),
          children: e,
          containerInfo: t,
          implementation: null
        }
      }
      var T = {
        render: function(e, t, n) {
          var r = g.get(t);
          if (null != r) return r.render(e, n);
          var i = new b(m, t);
          return g.set(t, i), i.render(e, n)
        },
        createRoot: function(e) {
          var t = g.get(e);
          if (null != t) return t;
          var n = new b(m, e, !0);
          return g.set(e, n), n
        },
        unstable_batchedUpdates: y,
        unmountComponentAtNode: function(e) {
          (0, o.zx)(e && [1, 8, 9, 11].includes(e.nodeType), "unmountComponentAtNode(...): Target container is not a DOM element.");
          var t = g.get(e);
          return !!t && (y((function() {
            t.unmount((function() {
              g.delete(e)
            }))
          }), null), !0)
        },
        findDOMNode: function(e) {
          if (null == e) return null;
          var t = e.nodeType;
          return 1 === t || 3 === t ? e : m.findHostInstance(e)
        },
        createPortal: w
      }
    },
    4886: function(e, t, n) {
      "use strict";
      n.r(t), n.d(t, {
        Events: function() {
          return v.zW
        },
        hooks: function() {
          return v.PT
        },
        A: function() {
          return pe
        },
        APP: function() {
          return N
        },
        BEHAVIORS: function() {
          return he
        },
        BODY: function() {
          return I
        },
        CATCHMOVE: function() {
          return re
        },
        CATCH_VIEW: function() {
          return ie
        },
        CHANGE: function() {
          return G
        },
        CLASS: function() {
          return Z
        },
        COMMENT: function() {
          return oe
        },
        CONFIRM: function() {
          return Y
        },
        CONTAINER: function() {
          return A
        },
        CONTEXT_ACTIONS: function() {
          return r
        },
        CURRENT_TARGET: function() {
          return K
        },
        CUSTOM_WRAPPER: function() {
          return q
        },
        Current: function() {
          return Fn
        },
        DATASET: function() {
          return H
        },
        DATE: function() {
          return te
        },
        DOCUMENT_ELEMENT_NAME: function() {
          return _
        },
        DOCUMENT_FRAGMENT: function() {
          return O
        },
        EVENT_CALLBACK_RESULT: function() {
          return fe
        },
        EXTERNAL_CLASSES: function() {
          return de
        },
        FOCUS: function() {
          return B
        },
        FormElement: function() {
          return Gt
        },
        HEAD: function() {
          return x
        },
        HOOKS_APP_ID: function() {
          return T
        },
        HTML: function() {
          return P
        },
        History: function() {
          return _n
        },
        ID: function() {
          return L
        },
        INPUT: function() {
          return z
        },
        KEY_CODE: function() {
          return X
        },
        Location: function() {
          return zn
        },
        MutationObserver: function() {
          return ke
        },
        OBJECT: function() {
          return W
        },
        ON_HIDE: function() {
          return ce
        },
        ON_LOAD: function() {
          return ae
        },
        ON_READY: function() {
          return ue
        },
        ON_SHOW: function() {
          return se
        },
        OPTIONS: function() {
          return le
        },
        PAGE_INIT: function() {
          return E
        },
        PROPERTY_THRESHOLD: function() {
          return k
        },
        PROPS: function() {
          return F
        },
        PURE_VIEW: function() {
          return U
        },
        ROOT_STR: function() {
          return S
        },
        SET_DATA: function() {
          return C
        },
        SET_TIMEOUT: function() {
          return ne
        },
        STATIC_VIEW: function() {
          return j
        },
        STYLE: function() {
          return D
        },
        SVGElement: function() {
          return nr
        },
        Style: function() {
          return ut
        },
        TARGET: function() {
          return $
        },
        TARO_RUNTIME: function() {
          return w
        },
        TIME_STAMP: function() {
          return Q
        },
        TOUCHMOVE: function() {
          return ee
        },
        TYPE: function() {
          return J
        },
        TaroElement: function() {
          return dt
        },
        TaroEvent: function() {
          return Ft
        },
        TaroNode: function() {
          return je
        },
        TaroRootElement: function() {
          return Jt
        },
        TaroText: function() {
          return Yt
        },
        UID: function() {
          return R
        },
        URL: function() {
          return mn
        },
        URLSearchParams: function() {
          return vn
        },
        VALUE: function() {
          return V
        },
        VIEW: function() {
          return M
        },
        addLeadingSlash: function() {
          return cr
        },
        cancelAnimationFrame: function() {
          return Qn
        },
        createComponentConfig: function() {
          return gr
        },
        createEvent: function() {
          return Ht
        },
        createPageConfig: function() {
          return mr
        },
        createRecursiveComponentConfig: function() {
          return br
        },
        document: function() {
          return bn
        },
        env: function() {
          return Ze
        },
        eventCenter: function() {
          return In
        },
        eventHandler: function() {
          return zt
        },
        eventSource: function() {
          return Le
        },
        getComputedStyle: function() {
          return wn
        },
        getCurrentInstance: function() {
          return Hn
        },
        getOnHideEventKey: function() {
          return vr
        },
        getOnReadyEventKey: function() {
          return hr
        },
        getOnShowEventKey: function() {
          return pr
        },
        getPageInstance: function() {
          return ur
        },
        getPath: function() {
          return fr
        },
        history: function() {
          return tr
        },
        hydrate: function() {
          return De
        },
        incrementId: function() {
          return we
        },
        injectPageInstance: function() {
          return ar
        },
        location: function() {
          return er
        },
        navigator: function() {
          return $n
        },
        nextTick: function() {
          return yr
        },
        now: function() {
          return Gn
        },
        options: function() {
          return ft
        },
        parseUrl: function() {
          return gn
        },
        removePageInstance: function() {
          return sr
        },
        requestAnimationFrame: function() {
          return Yn
        },
        safeExecute: function() {
          return lr
        },
        stringify: function() {
          return dr
        },
        window: function() {
          return Kn
        }
      });
      var r, i = n(6234),
        o = n(1208),
        a = n(3563),
        u = n(6840),
        s = n(6666),
        c = n(4826),
        l = n(5754),
        d = n(5991),
        f = n(8079),
        h = n(9249),
        p = n(7371),
        v = n(3295),
        m = n(4886).window,
        g = n(4886).document,
        b = n(4886).requestAnimationFrame,
        y = n(4886).cancelAnimationFrame,
        k = 2046,
        w = "Taro runtime",
        T = "taro-app",
        C = "小程序 setData",
        E = "页面初始化",
        S = "root",
        P = "html",
        x = "head",
        I = "body",
        N = "app",
        A = "container",
        _ = "#document",
        O = "document-fragment",
        L = "id",
        R = "uid",
        Z = "class",
        D = "style",
        B = "focus",
        M = "view",
        j = "static-view",
        U = "pure-view",
        F = "props",
        H = "dataset",
        W = "object",
        V = "value",
        z = "input",
        G = "change",
        q = "custom-wrapper",
        $ = "target",
        K = "currentTarget",
        J = "type",
        Y = "confirm",
        Q = "timeStamp",
        X = "keyCode",
        ee = "touchmove",
        te = "Date",
        ne = "setTimeout",
        re = "catchMove",
        ie = "catch-view",
        oe = "comment",
        ae = "onLoad",
        ue = "onReady",
        se = "onShow",
        ce = "onHide",
        le = "options",
        de = "externalClasses",
        fe = "e_result",
        he = "behaviors",
        pe = "a";
      ! function(e) {
        e.INIT = "0", e.RESTORE = "1", e.RECOVER = "2", e.DESTORY = "3"
      }(r || (r = {}));
      var ve = [],
        me = function(e, t) {
          return !!e && e.sid === (null == t ? void 0 : t.sid)
        },
        ge = function(e, t) {
          var n = t.characterData,
            r = t.characterDataOldValue,
            i = t.attributes,
            o = t.attributeOldValue,
            a = t.childList;
          switch (e.type) {
            case "characterData":
              return !!n && (r || (e.oldValue = null), !0);
            case "attributes":
              return !!i && (o || (e.oldValue = null), !0);
            case "childList":
              return !!a
          }
        },
        be = !1;

      function ye(e, t) {
        e.records.push(t), be || (be = !0, Promise.resolve().then((function() {
          be = !1, ve.forEach((function(e) {
            return e.callback(e.takeRecords())
          }))
        })))
      }
      var ke = (0, p.Z)((function e(t) {
          (0, h.Z)(this, e), this.core = {
            observe: v.ZT,
            disconnect: v.ZT,
            takeRecords: v.ZT
          }
        }), [{
          key: "observe",
          value: function() {
            var e;
            (e = this.core).observe.apply(e, arguments)
          }
        }, {
          key: "disconnect",
          value: function() {
            this.core.disconnect()
          }
        }, {
          key: "takeRecords",
          value: function() {
            return this.core.takeRecords()
          }
        }], [{
          key: "record",
          value: function(e) {
            ! function(e) {
              ve.forEach((function(t) {
                for (var n = t.options, r = e.target; r; r = r.parentNode) {
                  if (me(t.target, r) && ge(e, n)) {
                    ye(t, e);
                    break
                  }
                  if (!n.subtree) break
                }
              }))
            }(e)
          }
        }]),
        we = function() {
          for (var e = [], t = 65; t <= 90; t++) e.push(t);
          for (var n = 97; n <= 122; n++) e.push(n);
          var r = e.length - 1,
            i = [0, 0];
          return function() {
            var t = i.map((function(t) {
                return e[t]
              })),
              n = String.fromCharCode.apply(String, (0, f.Z)(t)),
              o = i.length - 1;
            for (i[o]++; i[o] > r;) {
              if (i[o] = 0, (o -= 1) < 0) {
                i.push(0);
                break
              }
              i[o]++
            }
            return n
          }
        };

      function Te(e) {
        return 1 === e.nodeType
      }

      function Ce(e) {
        return e.nodeName === oe
      }

      function Ee(e) {
        var t = Object.keys(e.props).find((function(e) {
          return !(/^(class|style|id)$/.test(e) || e.startsWith("data-"))
        }));
        return Boolean(t)
      }

      function Se(e) {
        switch (e) {
          case D:
            return "st";
          case L:
            return R;
          case Z:
            return "cl";
          default:
            return e
        }
      }
      var Pe, xe = new Map;

      function Ie(e, t, n) {
        (0, v.mf)(n) && (n = {
          value: n
        }), Object.defineProperty(e.prototype, t, Object.assign({
          configurable: !0,
          enumerable: !0
        }, n))
      }

      function Ne() {
        return Pe || (Pe = (0, v.W)(v.rD)), Pe
      }
      var Ae, _e, Oe = (0, p.Z)((function e(t, n) {
          var r = this;
          (0, h.Z)(this, e), this.tokenList = [], this.el = n, t.trim().split(/\s+/).forEach((function(e) {
            return r.tokenList.push(e)
          }))
        }), [{
          key: "value",
          get: function() {
            return this.toString()
          }
        }, {
          key: "length",
          get: function() {
            return this.tokenList.length
          }
        }, {
          key: "add",
          value: function() {
            var e = 0,
              t = !1,
              n = arguments,
              r = n.length,
              i = this.tokenList;
            do {
              var o = n[e];
              this.checkTokenIsValid(o) && !~i.indexOf(o) && (i.push(o), t = !0)
            } while (++e < r);
            t && this._update()
          }
        }, {
          key: "remove",
          value: function() {
            var e = 0,
              t = !1,
              n = arguments,
              r = n.length,
              i = this.tokenList;
            do {
              var o = n[e] + "";
              if (this.checkTokenIsValid(o)) {
                var a = i.indexOf(o);
                ~i.indexOf(o) && (i.splice(a, 1), t = !0)
              }
            } while (++e < r);
            t && this._update()
          }
        }, {
          key: "contains",
          value: function(e) {
            return !!this.checkTokenIsValid(e) && !!~this.tokenList.indexOf(e)
          }
        }, {
          key: "toggle",
          value: function(e, t) {
            var n = this.contains(e),
              r = n ? !0 !== t && "remove" : !1 !== t && "add";
            return r && this[r](e), !0 === t || !1 === t ? t : !n
          }
        }, {
          key: "replace",
          value: function(e, t) {
            if (this.checkTokenIsValid(e) && this.checkTokenIsValid(t)) {
              var n = this.tokenList.indexOf(e);
              ~n && (this.tokenList.splice(n, 1, t), this._update())
            }
          }
        }, {
          key: "toString",
          value: function() {
            return this.tokenList.filter((function(e) {
              return "" !== e
            })).join(" ")
          }
        }, {
          key: "checkTokenIsValid",
          value: function(e) {
            return "" !== e && !/\s/.test(e)
          }
        }, {
          key: "_update",
          value: function() {
            this.el.className = this.value
          }
        }]),
        Le = new(function(e) {
          function t() {
            return (0, h.Z)(this, t), (0, c.Z)(this, t, arguments)
          }
          return (0, l.Z)(t, e), (0, p.Z)(t, [{
            key: "removeNode",
            value: function(e) {
              var t = e.sid,
                n = e.uid;
              this.delete(t), n !== t && n && this.delete(n)
            }
          }, {
            key: "removeNodeTree",
            value: function(e) {
              var t = this;
              this.removeNode(e), e.childNodes.forEach((function(e) {
                return t.removeNodeTree(e)
              }))
            }
          }])
        }((0, d.Z)(Map))),
        Re = (0, v.gl)(),
        Ze = {
          window: Re ? m : v.kT,
          document: Re ? g : v.kT
        };

      function De(e) {
        var t;
        _e || (_e = Ne()), Ae || (Ae = v.PT.call("getSpecialNodes"));
        var n = e.nodeName;
        if (function(e) {
            return 3 === e.nodeType
          }(e)) return (0, s.Z)((0, s.Z)({}, "v", e.nodeValue), "nn", (null === (t = _e[n]) || void 0 === t ? void 0 : t._num) || "8");
        var r = (0, s.Z)((0, s.Z)({}, "nn", n), "sid", e.sid);
        e.uid !== e.sid && (r.uid = e.uid), !e.isAnyEventBinded() && Ae.indexOf(n) > -1 && (r.nn = "static-".concat(n), n !== M || Ee(e) || (r.nn = U));
        var i = e.props;
        for (var o in i) {
          var a = (0, v.CA)(o);
          o.startsWith("data-") || o === Z || o === D || o === L || a === re || (r[a] = i[o]), n === M && a === re && !1 !== i[o] && (r.nn = ie)
        }
        var u = e.childNodes;
        (u = u.filter((function(e) {
          return !Ce(e)
        }))).length > 0 ? r.cn = u.map(De) : r.cn = [], "" !== e.className && (r.cl = e.className);
        var c = e.cssText;
        "" !== c && "swiper-item" !== n && (r.st = c), v.PT.call("modifyHydrateData", r);
        var l = r.nn,
          d = _e[l];
        if (d)
          for (var f in r.nn = d._num, r) f in d && (r[d[f]] = r[f], delete r[f]);
        return r
      }
      var Be = (0, p.Z)((function e() {
          (0, h.Z)(this, e), this.__handlers = {}
        }), [{
          key: "addEventListener",
          value: function(e, t, n) {
            if (e = e.toLowerCase(), v.PT.call("onAddEvent", e, t, n, this), "regionchange" === e) return this.addEventListener("begin", t, n), void this.addEventListener("end", t, n);
            Boolean(n);
            var r = !1;
            if ((0, v.Kn)(n) && (Boolean(n.capture), r = Boolean(n.once)), r) {
              var i = function() {
                t.apply(this, arguments), this.removeEventListener(e, i)
              };
              this.addEventListener(e, i, Object.assign(Object.assign({}, n), {
                once: !1
              }))
            } else {
              var o = t;
              (t = function() {
                return o.apply(this, arguments)
              }).oldHandler = o;
              var a = this.__handlers[e];
              (0, v.kJ)(a) ? a.push(t): this.__handlers[e] = [t]
            }
          }
        }, {
          key: "removeEventListener",
          value: function(e, t) {
            if ("regionchange" === (e = e.toLowerCase())) return this.removeEventListener("begin", t), void this.removeEventListener("end", t);
            if (t) {
              var n = this.__handlers[e];
              if ((0, v.kJ)(n)) {
                var r = n.findIndex((function(e) {
                  if (e === t || e.oldHandler === t) return !0
                }));
                n.splice(r, 1)
              }
            }
          }
        }, {
          key: "isAnyEventBinded",
          value: function() {
            var e = this.__handlers,
              t = Object.keys(e).find((function(t) {
                return e[t].length
              }));
            return Boolean(t)
          }
        }]),
        Me = we(),
        je = function(e) {
          function t() {
            var e;
            return (0, h.Z)(this, t), (e = (0, c.Z)(this, t)).parentNode = null, e.childNodes = [], e.hydrate = function(e) {
              return function() {
                return De(e)
              }
            }, e.uid = "_" + Me(), e.sid = e.uid, Le.set(e.sid, e), e
          }
          return (0, l.Z)(t, e), (0, p.Z)(t, [{
            key: "updateChildNodes",
            value: function(e) {
              var t = this;
              this.enqueueUpdate({
                path: "".concat(this._path, ".").concat("cn"),
                value: e ? function() {
                  return []
                } : function() {
                  return t.childNodes.filter((function(e) {
                    return !Ce(e)
                  })).map(De)
                }
              })
            }
          }, {
            key: "updateSingleChild",
            value: function(e) {
              var t = this;
              this.childNodes.forEach((function(n, r) {
                Ce(n) || e && r < e || t.enqueueUpdate({
                  path: n._path,
                  value: t.hydrate(n)
                })
              }))
            }
          }, {
            key: "_root",
            get: function() {
              var e;
              return (null === (e = this.parentNode) || void 0 === e ? void 0 : e._root) || null
            }
          }, {
            key: "findIndex",
            value: function(e) {
              var t = this.childNodes.indexOf(e);
              return (0, v.zx)(-1 !== t, "The node to be replaced is not a child of this node."), t
            }
          }, {
            key: "_path",
            get: function() {
              var e = this.parentNode;
              if (e) {
                var t = e.childNodes.filter((function(e) {
                    return !Ce(e)
                  })).indexOf(this),
                  n = v.PT.call("getPathIndex", t);
                return "".concat(e._path, ".").concat("cn", ".").concat(n)
              }
              return ""
            }
          }, {
            key: "nextSibling",
            get: function() {
              var e = this.parentNode;
              return (null == e ? void 0 : e.childNodes[e.findIndex(this) + 1]) || null
            }
          }, {
            key: "previousSibling",
            get: function() {
              var e = this.parentNode;
              return (null == e ? void 0 : e.childNodes[e.findIndex(this) - 1]) || null
            }
          }, {
            key: "parentElement",
            get: function() {
              var e = this.parentNode;
              return 1 === (null == e ? void 0 : e.nodeType) ? e : null
            }
          }, {
            key: "firstChild",
            get: function() {
              return this.childNodes[0] || null
            }
          }, {
            key: "lastChild",
            get: function() {
              var e = this.childNodes;
              return e[e.length - 1] || null
            }
          }, {
            key: "textContent",
            set: function(e) {
              for (var t = this.childNodes.slice(), n = []; this.firstChild;) this.removeChild(this.firstChild, {
                doUpdate: !1
              });
              if ("" === e) this.updateChildNodes(!0);
              else {
                var r = Ze.document.createTextNode(e);
                n.push(r), this.appendChild(r), this.updateChildNodes()
              }
              ke.record({
                type: "childList",
                target: this,
                removedNodes: t,
                addedNodes: n
              })
            }
          }, {
            key: "insertBefore",
            value: function(e, t, n) {
              var r = this;
              if (e.nodeName === O) return e.childNodes.reduceRight((function(e, t) {
                return r.insertBefore(t, e), t
              }), t), e;
              e.remove({
                cleanRef: !1
              });
              var i = 0;
              e.parentNode = this, t ? (i = this.findIndex(t), this.childNodes.splice(i, 0, e)) : this.childNodes.push(e);
              var o = this.childNodes.length;
              return this._root && (t ? n ? this.enqueueUpdate({
                path: e._path,
                value: this.hydrate(e)
              }) : 2 * o / 3 > i ? this.updateChildNodes() : this.updateSingleChild(i) : 1 === o ? this.updateChildNodes() : this.enqueueUpdate({
                path: e._path,
                value: this.hydrate(e)
              })), ke.record({
                type: "childList",
                target: this,
                addedNodes: [e],
                removedNodes: n ? [t] : [],
                nextSibling: n ? t.nextSibling : t || null,
                previousSibling: e.previousSibling
              }), e
            }
          }, {
            key: "appendChild",
            value: function(e) {
              return this.insertBefore(e)
            }
          }, {
            key: "replaceChild",
            value: function(e, t) {
              if (t.parentNode === this) return this.insertBefore(e, t, !0), t.remove({
                doUpdate: !1
              }), t
            }
          }, {
            key: "removeChild",
            value: function(e) {
              var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                n = t.cleanRef,
                r = t.doUpdate;
              !1 !== n && !1 !== r && ke.record({
                type: "childList",
                target: this,
                removedNodes: [e],
                nextSibling: e.nextSibling,
                previousSibling: e.previousSibling
              });
              var i = this.findIndex(e);
              return this.childNodes.splice(i, 1), e.parentNode = null, !1 !== n && Le.removeNodeTree(e), this._root && !1 !== r && this.updateChildNodes(), e
            }
          }, {
            key: "remove",
            value: function(e) {
              var t;
              null === (t = this.parentNode) || void 0 === t || t.removeChild(this, e)
            }
          }, {
            key: "hasChildNodes",
            value: function() {
              return this.childNodes.length > 0
            }
          }, {
            key: "enqueueUpdate",
            value: function(e) {
              var t;
              null === (t = this._root) || void 0 === t || t.enqueueUpdate(e)
            }
          }, {
            key: "ownerDocument",
            get: function() {
              return Ze.document
            }
          }], [{
            key: "extend",
            value: function(e, n) {
              Ie(t, e, n)
            }
          }])
        }(Be),
        Ue = ["all", "appearance", "blockOverflow", "blockSize", "bottom", "clear", "contain", "content", "continue", "cursor", "direction", "display", "filter", "float", "gap", "height", "inset", "isolation", "left", "letterSpacing", "lightingColor", "markerSide", "mixBlendMode", "opacity", "order", "position", "quotes", "resize", "right", "rowGap", "tabSize", "tableLayout", "top", "userSelect", "verticalAlign", "visibility", "voiceFamily", "volume", "whiteSpace", "widows", "width", "zIndex", "pointerEvents", "aspectRatio"];

      function Fe(e, t, n) {
        !n && Ue.push(e), t.forEach((function(t) {
          Ue.push(e + t), "webkit" === e && Ue.push("Webkit" + t)
        }))
      }
      var He = "Color",
        We = "Style",
        Ve = "Width",
        ze = "Image",
        Ge = "Size",
        qe = [He, We, Ve],
        $e = ["FitLength", "FitWidth", ze],
        Ke = [].concat($e, ["Radius"]),
        Je = [].concat(qe, $e),
        Ye = ["EndRadius", "StartRadius"],
        Qe = ["Bottom", "Left", "Right", "Top"],
        Xe = ["End", "Start"],
        et = ["Content", "Items", "Self"],
        tt = ["BlockSize", "Height", "InlineSize", Ve],
        nt = ["After", "Before"];

      function rt(e) {
        ke.record({
          type: "attributes",
          target: e._element,
          attributeName: "style",
          oldValue: e.cssText
        })
      }

      function it(e) {
        var t = e._element;
        t._root && t.enqueueUpdate({
          path: "".concat(t._path, ".", "st"),
          value: e.cssText
        })
      }

      function ot(e, t) {
        this[t] !== e && (!this._pending && rt(this), (0, v.Ft)(e) || (0, v.o8)(e) ? (this._usedStyleProp.delete(t), delete this._value[t]) : (this._usedStyleProp.add(t), this._value[t] = e), !this._pending && it(this))
      }

      function at(e, t) {
        for (var n, r = {}, i = function() {
            var n = t[o];
            if (e[n]) return {
              v: void 0
            };
            r[n] = {
              get: function() {
                var e = this._value[n];
                return (0, v.Ft)(e) || (0, v.o8)(e) ? "" : e
              },
              set: function(e) {
                ot.call(this, e, n)
              }
            }
          }, o = 0; o < t.length; o++)
          if (n = i()) return n.v;
        Object.defineProperties(e.prototype, r)
      }
      Fe("borderBlock", qe), Fe("borderBlockEnd", qe), Fe("borderBlockStart", qe), Fe("outline", [].concat(qe, ["Offset"])), Fe("border", [].concat(qe, ["Boundary", "Break", "Collapse", "Radius", "Spacing"])), Fe("borderFit", ["Length", Ve]), Fe("borderInline", qe), Fe("borderInlineEnd", qe), Fe("borderInlineStart", qe), Fe("borderLeft", Je), Fe("borderRight", Je), Fe("borderTop", Je), Fe("borderBottom", Je), Fe("textDecoration", [He, We, "Line"]), Fe("textEmphasis", [He, We, "Position"]), Fe("scrollMargin", Qe), Fe("scrollPadding", Qe), Fe("padding", Qe), Fe("margin", [].concat(Qe, ["Trim"])), Fe("scrollMarginBlock", Xe), Fe("scrollMarginInline", Xe), Fe("scrollPaddingBlock", Xe), Fe("scrollPaddingInline", Xe), Fe("gridColumn", Xe), Fe("gridRow", Xe), Fe("insetBlock", Xe), Fe("insetInline", Xe), Fe("marginBlock", Xe), Fe("marginInline", Xe), Fe("paddingBlock", Xe), Fe("paddingInline", Xe), Fe("pause", nt), Fe("cue", nt), Fe("mask", ["Clip", "Composite", ze, "Mode", "Origin", "Position", "Repeat", Ge, "Type"]), Fe("borderImage", ["Outset", "Repeat", "Slice", "Source", "Transform", Ve]), Fe("maskBorder", ["Mode", "Outset", "Repeat", "Slice", "Source", Ve]), Fe("font", ["Family", "FeatureSettings", "Kerning", "LanguageOverride", "MaxSize", "MinSize", "OpticalSizing", "Palette", Ge, "SizeAdjust", "Stretch", We, "Weight", "VariationSettings"]), Fe("transform", ["Box", "Origin", We]), Fe("background", [He, ze, "Attachment", "BlendMode", "Clip", "Origin", "Position", "Repeat", Ge]), Fe("listStyle", [ze, "Position", "Type"]), Fe("scrollSnap", ["Align", "Stop", "Type"]), Fe("grid", ["Area", "AutoColumns", "AutoFlow", "AutoRows"]), Fe("gridTemplate", ["Areas", "Columns", "Rows"]), Fe("overflow", ["Block", "Inline", "Wrap", "X", "Y"]), Fe("transition", ["Delay", "Duration", "Property", "TimingFunction"]), Fe("color", ["Adjust", "InterpolationFilters", "Scheme"]), Fe("textAlign", ["All", "Last"]), Fe("page", ["BreakAfter", "BreakBefore", "BreakInside"]), Fe("animation", ["Delay", "Direction", "Duration", "FillMode", "IterationCount", "Name", "PlayState", "TimingFunction"]), Fe("flex", ["Basis", "Direction", "Flow", "Grow", "Shrink", "Wrap"]), Fe("offset", [].concat(nt, Xe, ["Anchor", "Distance", "Path", "Position", "Rotate"])), Fe("perspective", ["Origin"]), Fe("clip", ["Path", "Rule"]), Fe("flow", ["From", "Into"]), Fe("align", ["Content", "Items", "Self"], !0), Fe("alignment", ["Adjust", "Baseline"], !0), Fe("borderStart", Ye, !0), Fe("borderEnd", Ye, !0), Fe("borderCorner", ["Fit", ze, "ImageTransform"], !0), Fe("borderTopLeft", Ke, !0), Fe("borderTopRight", Ke, !0), Fe("borderBottomLeft", Ke, !0), Fe("borderBottomRight", Ke, !0), Fe("column", ["s", "Count", "Fill", "Gap", "Rule", "RuleColor", "RuleStyle", "RuleWidth", "Span", Ve], !0), Fe("break", [].concat(nt, ["Inside"]), !0), Fe("wrap", [].concat(nt, ["Flow", "Inside", "Through"]), !0), Fe("justify", et, !0), Fe("place", et, !0), Fe("max", [].concat(tt, ["Lines"]), !0), Fe("min", tt, !0), Fe("line", ["Break", "Clamp", "Grid", "Height", "Padding", "Snap"], !0), Fe("inline", ["BoxAlign", Ge, "Sizing"], !0), Fe("text", ["CombineUpright", "GroupAlign", "Height", "Indent", "Justify", "Orientation", "Overflow", "Shadow", "SpaceCollapse", "SpaceTrim", "Spacing", "Transform", "UnderlinePosition", "Wrap"], !0), Fe("shape", ["ImageThreshold", "Inside", "Margin", "Outside"], !0), Fe("word", ["Break", "Spacing", "Wrap"], !0), Fe("object", ["Fit", "Position"], !0), Fe("box", ["DecorationBreak", "Shadow", "Sizing", "Snap"], !0), Fe("webkit", ["LineClamp", "BoxOrient", "TextFillColor", "TextStroke", "TextStrokeColor", "TextStrokeWidth"], !0);
      var ut = (0, p.Z)((function e(t) {
        (0, h.Z)(this, e), this._element = t, this._usedStyleProp = new Set, this._value = {}
      }), [{
        key: "setCssVariables",
        value: function(e) {
          var t = this;
          this.hasOwnProperty(e) || Object.defineProperty(this, e, {
            enumerable: !0,
            configurable: !0,
            get: function() {
              return t._value[e] || ""
            },
            set: function(n) {
              ot.call(t, n, e)
            }
          })
        }
      }, {
        key: "cssText",
        get: function() {
          var e = this;
          if (!this._usedStyleProp.size) return "";
          var t = [];
          return this._usedStyleProp.forEach((function(n) {
            var r = e[n];
            if (!(0, v.Ft)(r) && !(0, v.o8)(r)) {
              var i = function(e) {
                return /^--/.test(e)
              }(n) ? n : (0, v.eu)(n);
              0 !== i.indexOf("webkit") && 0 !== i.indexOf("Webkit") || (i = "-".concat(i)), t.push("".concat(i, ": ").concat(r, ";"))
            }
          })), t.join(" ")
        },
        set: function(e) {
          var t = this;
          if (this._pending = !0, rt(this), this._usedStyleProp.forEach((function(e) {
              t.removeProperty(e)
            })), "" === e || (0, v.o8)(e) || (0, v.Ft)(e)) return this._pending = !1, void it(this);
          for (var n = e.split(";"), r = 0; r < n.length; r++) {
            var i = n[r].trim();
            if ("" !== i) {
              var o = i.split(":"),
                a = (0, u.Z)(o),
                s = a[0],
                c = a.slice(1).join(":");
              (0, v.o8)(c) || this.setProperty(s.trim(), c.trim())
            }
          }
          this._pending = !1, it(this)
        }
      }, {
        key: "setProperty",
        value: function(e, t) {
          "-" === e[0] ? this.setCssVariables(e) : e = (0, v.CA)(e), (0, v.Ft)(t) || (0, v.o8)(t) ? this.removeProperty(e) : this[e] = t
        }
      }, {
        key: "removeProperty",
        value: function(e) {
          if (e = (0, v.CA)(e), !this._usedStyleProp.has(e)) return "";
          var t = this[e];
          return this[e] = void 0, t
        }
      }, {
        key: "getPropertyValue",
        value: function(e) {
          return this[e = (0, v.CA)(e)] || ""
        }
      }]);

      function st() {
        return !0
      }

      function ct(e, t) {
        for (var n = [], r = null != t ? t : st, i = e; i;) 1 === i.nodeType && r(i) && n.push(i), i = lt(i, e);
        return n
      }

      function lt(e, t) {
        var n = e.firstChild,
          r = 1 === e.nodeType || 9 === e.nodeType;
        if (n && r) return n;
        var i = e;
        do {
          if (i === t) return null;
          var o = i.nextSibling;
          if (o) return o;
          i = i.parentElement
        } while (i);
        return null
      }
      at(ut, Ue), v.PT.tap("injectNewStyleProperties", (function(e) {
        if ((0, v.kJ)(e)) at(ut, e);
        else {
          if ("string" != typeof e) return;
          at(ut, [e])
        }
      }));
      var dt = function(e) {
          function t() {
            var e;
            return (0, h.Z)(this, t), (e = (0, c.Z)(this, t)).props = {}, e.dataset = v.kT, e.nodeType = 1, e.style = new ut(e), v.PT.call("patchElement", e), e
          }
          return (0, l.Z)(t, e), (0, p.Z)(t, [{
            key: "_stopPropagation",
            value: function(e) {
              for (var t = this; t = t.parentNode;) {
                var n = t.__handlers[e.type];
                if ((0, v.kJ)(n))
                  for (var r = n.length; r--;) n[r]._stop = !0
              }
            }
          }, {
            key: "id",
            get: function() {
              return this.getAttribute(L)
            },
            set: function(e) {
              this.setAttribute(L, e)
            }
          }, {
            key: "className",
            get: function() {
              return this.getAttribute(Z) || ""
            },
            set: function(e) {
              this.setAttribute(Z, e)
            }
          }, {
            key: "cssText",
            get: function() {
              return this.getAttribute(D) || ""
            }
          }, {
            key: "classList",
            get: function() {
              return new Oe(this.className, this)
            }
          }, {
            key: "children",
            get: function() {
              return this.childNodes.filter(Te)
            }
          }, {
            key: "attributes",
            get: function() {
              var e = this.props,
                t = Object.keys(e),
                n = this.style.cssText;
              return t.map((function(t) {
                return {
                  name: t,
                  value: e[t]
                }
              })).concat(n ? {
                name: D,
                value: n
              } : [])
            }
          }, {
            key: "textContent",
            get: function() {
              for (var e = "", t = this.childNodes, n = 0; n < t.length; n++) e += t[n].textContent;
              return e
            },
            set: function(e) {
              (0, a.Z)(t, "textContent", e, this, 1, 1)
            }
          }, {
            key: "hasAttribute",
            value: function(e) {
              return !(0, v.o8)(this.props[e])
            }
          }, {
            key: "hasAttributes",
            value: function() {
              return this.attributes.length > 0
            }
          }, {
            key: "focus",
            get: function() {
              return function() {
                this.setAttribute(B, !0)
              }
            },
            set: function(e) {
              this.setAttribute(B, e)
            }
          }, {
            key: "blur",
            value: function() {
              this.setAttribute(B, !1)
            }
          }, {
            key: "setAttribute",
            value: function(e, t) {
              var n = this.nodeName === M && !Ee(this) && !this.isAnyEventBinded();
              switch (e !== D && ke.record({
                  target: this,
                  type: "attributes",
                  attributeName: e,
                  oldValue: this.getAttribute(e)
                }), e) {
                case D:
                  this.style.cssText = t;
                  break;
                case L:
                  this.uid !== this.sid && Le.delete(this.uid), t = String(t), this.props[e] = this.uid = t, Le.set(t, this);
                  break;
                default:
                  this.props[e] = t, e.startsWith("data-") && (this.dataset === v.kT && (this.dataset = Object.create(null)), this.dataset[(0, v.CA)(e.replace(/^data-/, ""))] = t)
              }
              if (this._root) {
                var r = Ne(),
                  i = r[this.nodeName],
                  o = r[M]._num,
                  a = r[j]._num,
                  u = r[ie]._num,
                  s = this._path;
                e = Se(e);
                var c = (0, v.CA)(e),
                  l = {
                    path: "".concat(s, ".").concat(c),
                    value: (0, v.mf)(t) ? function() {
                      return t
                    } : t
                  };
                if (v.PT.call("modifySetAttrPayload", this, e, l, r), i) {
                  var d = i[c] || e;
                  l.path = "".concat(s, ".").concat((0, v.CA)(d))
                }
                this.enqueueUpdate(l), this.nodeName === M && (c === re ? this.enqueueUpdate({
                  path: "".concat(s, ".", "nn"),
                  value: t ? u : this.isAnyEventBinded() ? o : a
                }) : n && Ee(this) && this.enqueueUpdate({
                  path: "".concat(s, ".", "nn"),
                  value: a
                }))
              }
            }
          }, {
            key: "removeAttribute",
            value: function(e) {
              var t = this.nodeName === M && Ee(this) && !this.isAnyEventBinded();
              if (ke.record({
                  target: this,
                  type: "attributes",
                  attributeName: e,
                  oldValue: this.getAttribute(e)
                }), e === D) this.style.cssText = "";
              else {
                if (v.PT.call("onRemoveAttribute", this, e)) return;
                if (!this.props.hasOwnProperty(e)) return;
                delete this.props[e]
              }
              if (this._root) {
                var n = Ne(),
                  r = n[this.nodeName],
                  i = n[M]._num,
                  o = n[j]._num,
                  a = n[U]._num,
                  u = this._path;
                e = Se(e);
                var s = (0, v.CA)(e),
                  c = {
                    path: "".concat(u, ".").concat(s),
                    value: ""
                  };
                if (v.PT.call("modifyRmAttrPayload", this, e, c, n), r) {
                  var l = r[s] || e;
                  c.path = "".concat(u, ".").concat((0, v.CA)(l))
                }
                this.enqueueUpdate(c), this.nodeName === M && (s === re ? this.enqueueUpdate({
                  path: "".concat(u, ".", "nn"),
                  value: this.isAnyEventBinded() ? i : Ee(this) ? o : a
                }) : t && !Ee(this) && this.enqueueUpdate({
                  path: "".concat(u, ".", "nn"),
                  value: a
                }))
              }
            }
          }, {
            key: "getAttribute",
            value: function(e) {
              var t = e === D ? this.style.cssText : this.props[e];
              return null != t ? t : ""
            }
          }, {
            key: "getElementsByTagName",
            value: function(e) {
              var t = this;
              return ct(this, (function(n) {
                return n.nodeName === e || "*" === e && t !== n
              }))
            }
          }, {
            key: "getElementsByClassName",
            value: function(e) {
              var t = e.trim().split(/\s+/);
              return ct(this, (function(e) {
                var n = e.classList;
                return t.every((function(e) {
                  return n.contains(e)
                }))
              }))
            }
          }, {
            key: "dispatchEvent",
            value: function(e) {
              var t = e.cancelable,
                n = this.__handlers[e.type];
              if (!(0, v.kJ)(n)) return !1;
              for (var r = n.length; r--;) {
                var i = n[r],
                  o = void 0;
                if (i._stop ? i._stop = !1 : (v.PT.call("modifyDispatchEvent", e, this), o = i.call(this, e)), (!1 === o || e._end) && t && (e.defaultPrevented = !0), !(0, v.o8)(o) && e.mpEvent && (e.mpEvent[fe] = o), e._end && e._stop) break
              }
              return e._stop ? this._stopPropagation(e) : e._stop = !0, null != n
            }
          }, {
            key: "addEventListener",
            value: function(e, n, r) {
              var i = this.nodeName,
                a = v.PT.call("getSpecialNodes"),
                u = !0;
              if ((0, v.Kn)(r) && !1 === r.sideEffect && (u = !1, delete r.sideEffect), !1 !== u && !this.isAnyEventBinded() && a.indexOf(i) > -1) {
                var s = Ne()[i]._num;
                this.enqueueUpdate({
                  path: "".concat(this._path, ".", "nn"),
                  value: s
                })
              }(0, o.Z)(t, "addEventListener", this, 3)([e, n, r])
            }
          }, {
            key: "removeEventListener",
            value: function(e, n) {
              var r = !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2];
              (0, o.Z)(t, "removeEventListener", this, 3)([e, n]);
              var i = this.nodeName,
                a = v.PT.call("getSpecialNodes");
              if (!1 !== r && !this.isAnyEventBinded() && a.indexOf(i) > -1) {
                var u = Ne(),
                  s = Ee(this) ? "static-".concat(i) : "pure-".concat(i),
                  c = u[s]._num;
                this.enqueueUpdate({
                  path: "".concat(this._path, ".", "nn"),
                  value: c
                })
              }
            }
          }], [{
            key: "extend",
            value: function(e, n) {
              Ie(t, e, n)
            }
          }])
        }(je),
        ft = {
          prerender: !0,
          debug: !1
        };

      function ht(e, t, n) {
        for (var r = e.index, i = e.index = r + n, o = r; o < i; o++) "\n" === t.charAt(o) ? (e.line++, e.column = 0) : e.column++
      }

      function pt(e, t, n) {
        return ht(e, t, n - e.index)
      }

      function vt(e) {
        return {
          index: e.index,
          line: e.line,
          column: e.column
        }
      }
      var mt = /\s/;

      function gt(e) {
        return mt.test(e)
      }
      var bt = /=/;

      function yt(e) {
        return bt.test(e)
      }

      function kt(e) {
        var t = e.toLowerCase();
        return !!ft.html.skipElements.has(t)
      }
      var wt = /[A-Za-z0-9]/;

      function Tt(e, t, n) {
        if (!gt(n.charAt(e))) return !1;
        for (var r = n.length, i = e - 1; i > t; i--) {
          var o = n.charAt(i);
          if (!gt(o)) {
            if (yt(o)) return !1;
            break
          }
        }
        for (var a = e + 1; a < r; a++) {
          var u = n.charAt(a);
          if (!gt(u)) return !yt(u)
        }
      }
      var Ct = (0, p.Z)((function e(t) {
        (0, h.Z)(this, e), this.tokens = [], this.position = {
          index: 0,
          column: 0,
          line: 0
        }, this.html = t
      }), [{
        key: "scan",
        value: function() {
          for (var e = this.html, t = this.position, n = e.length; t.index < n;) {
            var r = t.index;
            if (this.scanText(), t.index === r)
              if (e.startsWith("!--", r + 1)) this.scanComment();
              else {
                var i = this.scanTag();
                kt(i) && this.scanSkipTag(i)
              }
          }
          return this.tokens
        }
      }, {
        key: "scanText",
        value: function() {
          var e = this.html,
            t = this.position,
            n = function(e, t) {
              for (;;) {
                var n = e.indexOf("<", t);
                if (-1 === n) return n;
                var r = e.charAt(n + 1);
                if ("/" === r || "!" === r || wt.test(r)) return n;
                t = n + 1
              }
            }(e, t.index);
          if (n !== t.index) {
            -1 === n && (n = e.length);
            var r = vt(t),
              i = e.slice(t.index, n);
            pt(t, e, n);
            var o = vt(t);
            this.tokens.push({
              type: "text",
              content: i,
              position: {
                start: r,
                end: o
              }
            })
          }
        }
      }, {
        key: "scanComment",
        value: function() {
          var e = this.html,
            t = this.position,
            n = vt(t);
          ht(t, e, 4);
          var r = e.indexOf("--\x3e", t.index),
            i = r + 3; - 1 === r && (r = i = e.length);
          var o = e.slice(t.index, r);
          pt(t, e, i), this.tokens.push({
            type: "comment",
            content: o,
            position: {
              start: n,
              end: vt(t)
            }
          })
        }
      }, {
        key: "scanTag",
        value: function() {
          this.scanTagStart();
          var e = this.scanTagName();
          return this.scanAttrs(), this.scanTagEnd(), e
        }
      }, {
        key: "scanTagStart",
        value: function() {
          var e = this.html,
            t = this.position,
            n = "/" === e.charAt(t.index + 1),
            r = vt(t);
          ht(t, e, n ? 2 : 1), this.tokens.push({
            type: "tag-start",
            close: n,
            position: {
              start: r
            }
          })
        }
      }, {
        key: "scanTagEnd",
        value: function() {
          var e = this.html,
            t = this.position,
            n = "/" === e.charAt(t.index);
          ht(t, e, n ? 2 : 1);
          var r = vt(t);
          this.tokens.push({
            type: "tag-end",
            close: n,
            position: {
              end: r
            }
          })
        }
      }, {
        key: "scanTagName",
        value: function() {
          for (var e = this.html, t = this.position, n = e.length, r = t.index; r < n;) {
            var i = e.charAt(r);
            if (!gt(i) && "/" !== i && ">" !== i) break;
            r++
          }
          for (var o = r + 1; o < n;) {
            var a = e.charAt(o);
            if (gt(a) || "/" === a || ">" === a) break;
            o++
          }
          pt(t, e, o);
          var u = e.slice(r, o);
          return this.tokens.push({
            type: "tag",
            content: u
          }), u
        }
      }, {
        key: "scanAttrs",
        value: function() {
          for (var e = this.html, t = this.position, n = this.tokens, r = t.index, i = null, o = r, a = [], u = e.length; r < u;) {
            var s = e.charAt(r);
            if (i) s === i && (i = null), r++;
            else {
              if ("/" === s || ">" === s) {
                r !== o && a.push(e.slice(o, r));
                break
              }
              Tt(r, o, e) ? (r !== o && a.push(e.slice(o, r)), o = r + 1, r++) : "'" === s || '"' === s ? (i = s, r++) : r++
            }
          }
          pt(t, e, r);
          for (var c = a.length, l = "attribute", d = 0; d < c; d++) {
            var f = a[d];
            if (f.includes("=")) {
              var h = a[d + 1];
              if (h && h.startsWith("=")) {
                if (h.length > 1) {
                  var p = f + h;
                  n.push({
                    type: l,
                    content: p
                  }), d += 1;
                  continue
                }
                var v = a[d + 2];
                if (d += 1, v) {
                  var m = f + "=" + v;
                  n.push({
                    type: l,
                    content: m
                  }), d += 1;
                  continue
                }
              }
            }
            if (f.endsWith("=")) {
              var g = a[d + 1];
              if (g && !g.includes("=")) {
                var b = f + g;
                n.push({
                  type: l,
                  content: b
                }), d += 1;
                continue
              }
              var y = f.slice(0, -1);
              n.push({
                type: l,
                content: y
              })
            } else n.push({
              type: l,
              content: f
            })
          }
        }
      }, {
        key: "scanSkipTag",
        value: function(e) {
          for (var t = this.html, n = this.position, r = e.toLowerCase(), i = t.length; n.index < i;) {
            var o = t.indexOf("</", n.index);
            if (-1 === o) {
              this.scanText();
              break
            }
            if (pt(n, t, o), r === this.scanTag().toLowerCase()) break
          }
        }
      }]);

      function Et(e) {
        var t = e.charAt(0),
          n = e.length - 1;
        return '"' !== t && "'" !== t || t !== e.charAt(n) ? e : e.slice(1, n)
      }
      var St = (0, p.Z)((function e() {
        (0, h.Z)(this, e), this.styles = []
      }), [{
        key: "extractStyle",
        value: function(e) {
          var t = this,
            n = e;
          return (n = n.replace(/<style\s?[^>]*>((.|\n|\s)+?)<\/style>/g, (function(e, n) {
            var r = n.trim();
            return t.stringToSelector(r), ""
          }))).trim()
        }
      }, {
        key: "stringToSelector",
        value: function(e) {
          for (var t = this, n = e.indexOf("{"), r = function() {
              var r = e.indexOf("}"),
                i = e.slice(0, n).trim(),
                o = e.slice(n + 1, r);
              o = (o = (o = o.replace(/:(.*);/g, (function(e, t) {
                var n = t.trim().replace(/ +/g, "+++");
                return ":".concat(n, ";")
              }))).replace(/ /g, "")).replace(/\+\+\+/g, " "), /;$/.test(o) || (o += ";"), i.split(",").forEach((function(e) {
                var n = t.parseSelector(e);
                t.styles.push({
                  content: o,
                  selectorList: n
                })
              })), e = e.slice(r + 1), n = e.indexOf("{")
            }; n > -1;) r()
        }
      }, {
        key: "parseSelector",
        value: function(e) {
          return e.trim().replace(/ *([>~+]) */g, " $1").replace(/ +/g, " ").replace(/\[\s*([^[\]=\s]+)\s*=\s*([^[\]=\s]+)\s*\]/g, "[$1=$2]").split(" ").map((function(e) {
            var t = e.charAt(0),
              n = {
                isChild: ">" === t,
                isGeneralSibling: "~" === t,
                isAdjacentSibling: "+" === t,
                tag: null,
                id: null,
                class: [],
                attrs: []
              };
            return "" !== (e = (e = (e = e.replace(/^[>~+]/, "")).replace(/\[(.+?)\]/g, (function(e, t) {
              var r = t.split("="),
                o = (0, i.Z)(r, 2),
                a = o[0],
                u = o[1],
                s = -1 === t.indexOf("="),
                c = {
                  all: s,
                  key: a,
                  value: s ? null : u
                };
              return n.attrs.push(c), ""
            }))).replace(/([.#][A-Za-z0-9-_]+)/g, (function(e, t) {
              return "#" === t[0] ? n.id = t.substr(1) : "." === t[0] && n.class.push(t.substr(1)), ""
            }))) && (n.tag = e), n
          }))
        }
      }, {
        key: "matchStyle",
        value: function(e, t, n) {
          var r = this;
          return function(e) {
            return e.sort((function(e, t) {
              var n = xt(e.selectorList),
                r = xt(t.selectorList);
              if (n !== r) return n - r;
              var i = It(e.selectorList),
                o = It(t.selectorList);
              return i !== o ? i - o : Nt(e.selectorList) - Nt(t.selectorList)
            }))
          }(this.styles).reduce((function(i, o, a) {
            var u = o.content,
              s = o.selectorList,
              c = n[a],
              l = s[c],
              d = s[c + 1];
            ((null == d ? void 0 : d.isGeneralSibling) || (null == d ? void 0 : d.isAdjacentSibling)) && (l = d, c += 1, n[a] += 1);
            var f = r.matchCurrent(e, t, l);
            if (f && l.isGeneralSibling)
              for (var h = Pt(t); h;) {
                if (h.h5tagName && r.matchCurrent(h.h5tagName, h, s[c - 1])) {
                  f = !0;
                  break
                }
                h = Pt(h), f = !1
              }
            if (f && l.isAdjacentSibling) {
              var p = Pt(t);
              p && p.h5tagName && r.matchCurrent(p.h5tagName, p, s[c - 1]) || (f = !1)
            }
            if (f) {
              if (c === s.length - 1) return i + u;
              c < s.length - 1 && (n[a] += 1)
            } else l.isChild && c > 0 && (n[a] -= 1, r.matchCurrent(e, t, s[n[a]]) && (n[a] += 1));
            return i
          }), "")
        }
      }, {
        key: "matchCurrent",
        value: function(e, t, n) {
          if (n.tag && n.tag !== e) return !1;
          if (n.id && n.id !== t.id) return !1;
          if (n.class.length)
            for (var r = t.className.split(" "), i = 0; i < n.class.length; i++) {
              var o = n.class[i];
              if (-1 === r.indexOf(o)) return !1
            }
          if (n.attrs.length)
            for (var a = 0; a < n.attrs.length; a++) {
              var u = n.attrs[a],
                s = u.all,
                c = u.key,
                l = u.value;
              if (s && !t.hasAttribute(c)) return !1;
              if (t.getAttribute(c) !== Et(l || "")) return !1
            }
          return !0
        }
      }]);

      function Pt(e) {
        if (!e.parentElement) return null;
        var t = e.previousSibling;
        return t ? 1 === t.nodeType ? t : Pt(t) : null
      }

      function xt(e) {
        return e.reduce((function(e, t) {
          return e + (t.id ? 1 : 0)
        }), 0)
      }

      function It(e) {
        return e.reduce((function(e, t) {
          return e + t.class.length + t.attrs.length
        }), 0)
      }

      function Nt(e) {
        return e.reduce((function(e, t) {
          return e + (t.tag ? 1 : 0)
        }), 0)
      }

      function At(e, t) {
        for (var n = Object.create(null), r = e.split(","), i = 0; i < r.length; i++) n[r[i]] = !0;
        return t ? function(e) {
          return !!n[e.toLowerCase()]
        } : function(e) {
          return !!n[e]
        }
      }
      var _t = {
          img: "image",
          iframe: "web-view"
        },
        Ot = At(Object.keys(v.rD).map((function(e) {
          return e.toLowerCase()
        })).join(","), !0),
        Lt = At("a,i,abbr,iframe,select,acronym,slot,small,span,bdi,kbd,strong,big,map,sub,sup,br,mark,mark,meter,template,canvas,textarea,cite,object,time,code,output,u,data,picture,tt,datalist,var,dfn,del,q,em,s,embed,samp,b", !0),
        Rt = At("address,fieldset,li,article,figcaption,main,aside,figure,nav,blockquote,footer,ol,details,form,p,dialog,h1,h2,h3,h4,h5,h6,pre,dd,header,section,div,hgroup,table,dl,hr,ul,dt", !0),
        Zt = {
          li: ["ul", "ol", "menu"],
          dt: ["dl"],
          dd: ["dl"],
          tbody: ["table"],
          thead: ["table"],
          tfoot: ["table"],
          tr: ["table"],
          td: ["table"]
        };

      function Dt(e, t) {
        var n = Zt[e];
        if (n)
          for (var r = t.length - 1; r >= 0;) {
            var i = t[r].tagName;
            if (i === e) break;
            if (n && n.includes(i)) return !0;
            r--
          }
        return !1
      }

      function Bt(e) {
        var t = e.indexOf("=");
        return -1 === t ? [e] : [e.slice(0, t).trim(), e.slice(t + "=".length).trim()]
      }

      function Mt(e, t, n, r) {
        return e.filter((function(e) {
          return "comment" !== e.type && ("text" !== e.type || "" !== e.content)
        })).map((function(e) {
          if ("text" === e.type) {
            var o = t.createTextNode(e.content);
            return (0, v.mf)(ft.html.transformText) && (o = ft.html.transformText(o, e)), null == r || r.appendChild(o), o
          }
          var a = t.createElement(function(e) {
            return ft.html.renderHTMLTag ? e : _t[e] ? _t[e] : Ot(e) ? e : Rt(e) ? "view" : Lt(e) ? "text" : "view"
          }(e.tagName));
          a.h5tagName = e.tagName, null == r || r.appendChild(a), ft.html.renderHTMLTag || (a.className = "h5-".concat(e.tagName));
          for (var u = 0; u < e.attributes.length; u++) {
            var s = Bt(e.attributes[u]),
              c = (0, i.Z)(s, 2),
              l = c[0],
              d = c[1];
            if ("class" === l) a.className += " " + Et(d);
            else {
              if ("o" === l[0] && "n" === l[1]) continue;
              a.setAttribute(l, null == d || Et(d))
            }
          }
          var f = n.styleTagParser,
            h = n.descendantList.slice(),
            p = f.matchStyle(e.tagName, a, h);
          return a.setAttribute("style", p + a.style.cssText), Mt(e.children, t, {
            styleTagParser: f,
            descendantList: h
          }, a), (0, v.mf)(ft.html.transformElement) ? ft.html.transformElement(a, e) : a
        }))
      }

      function jt(e, t) {
        var n = new St;
        e = n.extractStyle(e);
        var r = {
          tagName: "",
          children: [],
          type: "element",
          attributes: []
        };
        return function e(t) {
          for (var n = t.tokens, r = t.stack, i = t.cursor, o = n.length, a = r[r.length - 1].children; i < o;) {
            var u = n[i];
            if ("tag-start" === u.type) {
              var s = n[++i];
              i++;
              var c = s.content.toLowerCase();
              if (u.close) {
                for (var l = r.length, d = !1; --l > -1;)
                  if (r[l].tagName === c) {
                    d = !0;
                    break
                  } for (; i < o && "tag-end" === n[i].type;) i++;
                if (d) {
                  r.splice(l);
                  break
                }
              } else {
                var f = ft.html.closingElements.has(c);
                if (f && (f = !Dt(c, r)), f)
                  for (var h = r.length - 1; h > 0;) {
                    if (c === r[h].tagName) {
                      r.splice(h), a = r[h - 1].children;
                      break
                    }
                    h -= 1
                  }
                for (var p = [], v = void 0; i < o && "tag-end" !== (v = n[i]).type;) p.push(v.content), i++;
                i++;
                var m = [],
                  g = {
                    type: "element",
                    tagName: s.content,
                    attributes: p,
                    children: m
                  };
                if (a.push(g), !v.close && !ft.html.voidElements.has(c)) {
                  r.push({
                    tagName: c,
                    children: m
                  });
                  var b = {
                    tokens: n,
                    cursor: i,
                    stack: r
                  };
                  e(b), i = b.cursor
                }
              }
            } else a.push(u), i++
          }
          t.cursor = i
        }({
          tokens: new Ct(e).scan(),
          options: ft,
          cursor: 0,
          stack: [r]
        }), Mt(r.children, t, {
          styleTagParser: n,
          descendantList: Array(n.styles.length).fill(0)
        })
      }

      function Ut(e, t) {
        for (; e.firstChild;) e.removeChild(e.firstChild);
        for (var n = jt(t, e.ownerDocument), r = 0; r < n.length; r++) e.appendChild(n[r])
      }
      ft.html = {
        skipElements: new Set(["style", "script"]),
        voidElements: new Set(["!doctype", "area", "base", "br", "col", "command", "embed", "hr", "img", "input", "keygen", "link", "meta", "param", "source", "track", "wbr"]),
        closingElements: new Set(["html", "head", "body", "p", "dt", "dd", "li", "option", "thead", "th", "tbody", "tr", "td", "tfoot", "colgroup"]),
        renderHTMLTag: !1
      }, (0, v.gl)() || (je.extend("innerHTML", {
        set: function(e) {
          Ut.call(this, this, e)
        },
        get: function() {
          return ""
        }
      }), dt.extend("getBoundingClientRect", (function() {
        var e = this;
        return ft.miniGlobal ? new Promise((function(t) {
          ft.miniGlobal.createSelectorQuery().select("#".concat(e.uid)).boundingClientRect((function(e) {
            t(e)
          })).exec()
        })) : Promise.resolve(null)
      })));
      var Ft = (0, p.Z)((function e(t, n, r) {
        (0, h.Z)(this, e), this._stop = !1, this._end = !1, this.defaultPrevented = !1, this.button = 0, this.timeStamp = Date.now(), this.type = t.toLowerCase(), this.mpEvent = r, this.bubbles = Boolean(n && n.bubbles), this.cancelable = Boolean(n && n.cancelable)
      }), [{
        key: "stopPropagation",
        value: function() {
          this._stop = !0
        }
      }, {
        key: "stopImmediatePropagation",
        value: function() {
          this._end = this._stop = !0
        }
      }, {
        key: "preventDefault",
        value: function() {
          this.defaultPrevented = !0
        }
      }, {
        key: "target",
        get: function() {
          var e, t, n, r = this.cacheTarget;
          if (r) return r;
          var i = Object.create((null === (e = this.mpEvent) || void 0 === e ? void 0 : e.target) || null),
            o = Ze.document.getElementById((null === (t = i.dataset) || void 0 === t ? void 0 : t.sid) || i.id || null);
          for (var a in i.dataset = null !== o ? o.dataset : v.kT, null === (n = this.mpEvent) || void 0 === n ? void 0 : n.detail) i[a] = this.mpEvent.detail[a];
          return this.cacheTarget = i, i
        }
      }, {
        key: "currentTarget",
        get: function() {
          var e, t, n, r, i, o, a, u, s = this.cacheCurrentTarget;
          if (s) return s;
          var c = Ze.document,
            l = Object.create((null === (e = this.mpEvent) || void 0 === e ? void 0 : e.currentTarget) || null),
            d = c.getElementById((null === (t = l.dataset) || void 0 === t ? void 0 : t.sid) || l.id || null),
            f = c.getElementById((null === (i = null === (r = null === (n = this.mpEvent) || void 0 === n ? void 0 : n.target) || void 0 === r ? void 0 : r.dataset) || void 0 === i ? void 0 : i.sid) || (null === (a = null === (o = this.mpEvent) || void 0 === o ? void 0 : o.target) || void 0 === a ? void 0 : a.id) || null);
          if (null === d || d && d === f) return this.cacheCurrentTarget = this.target, this.target;
          for (var h in l.dataset = d.dataset, null === (u = this.mpEvent) || void 0 === u ? void 0 : u.detail) l[h] = this.mpEvent.detail[h];
          return this.cacheCurrentTarget = l, l
        }
      }]);

      function Ht(e, t) {
        if ("string" == typeof e) return new Ft(e, {
          bubbles: !0,
          cancelable: !0
        });
        var n = new Ft(e.type, {
          bubbles: !0,
          cancelable: !0
        }, e);
        for (var r in e) r !== K && r !== $ && r !== J && r !== Q && (n[r] = e[r]);
        return n.type === Y && (null == t ? void 0 : t.nodeName) === z && (n[X] = 13), n
      }
      var Wt = {};

      function Vt(e) {
        var t = e[fe];
        return (0, v.o8)(t) || delete e[fe], t
      }

      function zt(e) {
        var t, n;
        void 0 === e.type && Object.defineProperty(e, "type", {
          value: e._type
        }), void 0 === e.detail && Object.defineProperty(e, "detail", {
          value: e._detail || Object.assign({}, e)
        }), e.currentTarget = e.currentTarget || e.target || Object.assign({}, e), v.PT.call("modifyMpEventImpl", e);
        var r = e.currentTarget,
          i = (null === (t = r.dataset) || void 0 === t ? void 0 : t.sid) || r.id || (null === (n = e.detail) || void 0 === n ? void 0 : n.id) || "",
          o = Ze.document.getElementById(i);
        if (o) {
          var a = function() {
            var t = Ht(e, o);
            v.PT.call("modifyTaroEvent", t, o), v.PT.call("dispatchTaroEvent", t, o), v.PT.call("dispatchTaroEventFinish", t, o)
          };
          if (!v.PT.isExist("batchedEventUpdates")) return a(), Vt(e);
          var u = e.type;
          if (!v.PT.call("isBubbleEvents", u) || ! function(e, t) {
              for (var n, r = !1;
                (null == e ? void 0 : e.parentElement) && e.parentElement._path !== S;) {
                if (null === (n = e.parentElement.__handlers[t]) || void 0 === n ? void 0 : n.length) {
                  r = !0;
                  break
                }
                e = e.parentElement
              }
              return r
            }(o, u) || u === ee && o.props.catchMove) return v.PT.call("batchedEventUpdates", (function() {
            Wt[u] && (Wt[u].forEach((function(e) {
              return e()
            })), delete Wt[u]), a()
          })), Vt(e);
          (Wt[u] || (Wt[u] = [])).push(a)
        }
      }
      var Gt = function(e) {
          function t() {
            return (0, h.Z)(this, t), (0, c.Z)(this, t, arguments)
          }
          return (0, l.Z)(t, e), (0, p.Z)(t, [{
            key: "type",
            get: function() {
              var e;
              return null !== (e = this.props[J]) && void 0 !== e ? e : ""
            },
            set: function(e) {
              this.setAttribute(J, e)
            }
          }, {
            key: "value",
            get: function() {
              var e = this.props[V];
              return null == e ? "" : e
            },
            set: function(e) {
              this.setAttribute(V, e)
            }
          }, {
            key: "dispatchEvent",
            value: function(e) {
              if (e.mpEvent) {
                var n = e.mpEvent.detail.value;
                e.type === G ? this.props.value = n : e.type === z && (this.value = n)
              }
              return (0, o.Z)(t, "dispatchEvent", this, 3)([e])
            }
          }])
        }(dt),
        qt = new((0, p.Z)((function e() {
          (0, h.Z)(this, e), this.recorder = new Map
        }), [{
          key: "start",
          value: function(e) {
            ft.debug && this.recorder.set(e, Date.now())
          }
        }, {
          key: "stop",
          value: function(e) {
            if (ft.debug) {
              var t = Date.now(),
                n = this.recorder.get(e);
              this.recorder.delete(e);
              var r = t - n;
              console.log("".concat(e, " 时长： ").concat(r, "ms"))
            }
          }
        }]));

      function $t(e, t) {
        var n, r = t.slice(1),
          i = e,
          o = "";
        if (r.some((function(e, r) {
            var a = e.replace(/^\[(.+)\]$/, "$1").replace(/\bcn\b/g, "childNodes");
            if (i = i[a], (0, v.kJ)(i) && (i = i.filter((function(e) {
                return !Ce(e)
              }))), (0, v.o8)(i)) return !0;
            if (i.nodeName === q) {
              var u = xe.get(i.sid);
              u && (n = u, o = t.slice(r + 2).join("."))
            }
          })), n) return {
          customWrapper: n,
          splitedPath: o
        }
      }
      var Kt, Jt = function(e) {
          function t() {
            var e;
            return (0, h.Z)(this, t), (e = (0, c.Z)(this, t)).updatePayloads = [], e.updateCallbacks = [], e.pendingUpdate = !1, e.ctx = null, e.nodeName = S, e.tagName = S.toUpperCase(), e
          }
          return (0, l.Z)(t, e), (0, p.Z)(t, [{
            key: "_path",
            get: function() {
              return S
            }
          }, {
            key: "_root",
            get: function() {
              return this
            }
          }, {
            key: "enqueueUpdate",
            value: function(e) {
              this.updatePayloads.push(e), !this.pendingUpdate && this.ctx && this.performUpdate()
            }
          }, {
            key: "performUpdate",
            value: function() {
              var e = this,
                t = arguments.length > 0 && void 0 !== arguments[0] && arguments[0],
                n = arguments.length > 1 ? arguments[1] : void 0;
              this.pendingUpdate = !0;
              var r = v.PT.call("proxyToRaw", this.ctx);
              setTimeout((function() {
                var i = "".concat(C, " 开始时间戳 ").concat(Date.now());
                qt.start(i);
                for (var o = Object.create(null), a = new Set(t ? ["root.cn.[0]", "root.cn[0]"] : []); e.updatePayloads.length > 0;) {
                  var u = e.updatePayloads.shift(),
                    c = u.path,
                    l = u.value;
                  c.endsWith("cn") && a.add(c), o[c] = l
                }
                var d = function(e) {
                  a.forEach((function(t) {
                    e.includes(t) && e !== t && delete o[e]
                  }));
                  var t = o[e];
                  (0, v.mf)(t) && (o[e] = t())
                };
                for (var f in o) d(f);
                if ((0, v.mf)(n)) return n(o);
                e.pendingUpdate = !1;
                var h = {},
                  p = new Map;
                if (t) h = o;
                else
                  for (var m in o) {
                    var g = m.split("."),
                      b = $t(e, g);
                    if (b) {
                      var y = b.customWrapper,
                        k = b.splitedPath;
                      p.set(y, Object.assign(Object.assign({}, p.get(y) || {}), (0, s.Z)({}, "i.".concat(k), o[m])))
                    } else h[m] = o[m]
                  }
                var w = p.size,
                  T = Object.keys(h).length > 0,
                  S = w + (T ? 1 : 0),
                  P = 0,
                  x = function() {
                    ++P === S && (qt.stop(i), e.flushUpdateCallback(), t && qt.stop(E))
                  };
                w && p.forEach((function(e, t) {
                  t.setData(e, x)
                })), T && r.setData(h, x)
              }), 0)
            }
          }, {
            key: "enqueueUpdateCallback",
            value: function(e, t) {
              this.updateCallbacks.push((function() {
                t ? e.call(t) : e()
              }))
            }
          }, {
            key: "flushUpdateCallback",
            value: function() {
              var e = this.updateCallbacks;
              if (e.length) {
                var t = e.slice(0);
                this.updateCallbacks.length = 0;
                for (var n = 0; n < t.length; n++) t[n]()
              }
            }
          }])
        }(dt),
        Yt = function(e) {
          function t(e) {
            var n;
            return (0, h.Z)(this, t), (n = (0, c.Z)(this, t)).nodeType = 3, n.nodeName = "#text", n._value = e, n
          }
          return (0, l.Z)(t, e), (0, p.Z)(t, [{
            key: "textContent",
            get: function() {
              return this._value
            },
            set: function(e) {
              ke.record({
                target: this,
                type: "characterData",
                oldValue: this._value
              }), this._value = e, this.enqueueUpdate({
                path: "".concat(this._path, ".", "v"),
                value: e
              })
            }
          }, {
            key: "nodeValue",
            get: function() {
              return this._value
            },
            set: function(e) {
              this.textContent = e
            }
          }, {
            key: "data",
            get: function() {
              return this._value
            },
            set: function(e) {
              this.textContent = e
            }
          }])
        }(je);

      function Qt(e, t, n, r) {
        if ("a" === n && !r) throw new TypeError("Private accessor was defined without a getter");
        if ("function" == typeof t ? e !== t || !r : !t.has(e)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
        return "m" === n ? r : "a" === n ? r.call(e) : r ? r.value : t.get(e)
      }

      function Xt(e, t, n, r, i) {
        if ("m" === r) throw new TypeError("Private method is not writable");
        if ("a" === r && !i) throw new TypeError("Private accessor was defined without a setter");
        if ("function" == typeof t ? e !== t || !i : !t.has(e)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
        return "a" === r ? i.call(e, n) : i ? i.value = n : t.set(e, n), n
      }
      var en = /[!'()~]|%20|%00/g,
        tn = /\+/g,
        nn = {
          "!": "%21",
          "'": "%27",
          "(": "%28",
          ")": "%29",
          "~": "%7E",
          "%20": "+",
          "%00": "\0"
        };

      function rn(e) {
        return nn[e]
      }

      function on(e, t, n) {
        var r = (0, v.kJ)(n) ? n.join(",") : n;
        t in e ? e[t].push(r) : e[t] = [r]
      }

      function an(e, t) {
        on(this, t, e)
      }

      function un(e) {
        return decodeURIComponent(e.replace(tn, " "))
      }

      function sn(e) {
        return encodeURIComponent(e).replace(en, rn)
      }
      var cn, ln, dn, fn, hn, pn, vn = (0, p.Z)((function e(t) {
        (0, h.Z)(this, e), Kt.set(this, Object.create(null)), null != t || (t = "");
        var n = Qt(this, Kt, "f");
        if ("string" == typeof t) {
          "?" === t.charAt(0) && (t = t.slice(1));
          for (var r = t.split("&"), i = 0, o = r.length; i < o; i++) {
            var a = r[i],
              u = a.indexOf("=");
            u > -1 ? on(n, un(a.slice(0, u)), un(a.slice(u + 1))) : a.length && on(n, un(a), "")
          }
        } else if ((0, v.kJ)(t))
          for (var s = 0, c = t.length; s < c; s++) {
            var l = t[s];
            on(n, l[0], l[1])
          } else if (t.forEach) t.forEach(an, n);
          else
            for (var d in t) on(n, d, t[d])
      }), [{
        key: "append",
        value: function(e, t) {
          on(Qt(this, Kt, "f"), e, t)
        }
      }, {
        key: "delete",
        value: function(e) {
          delete Qt(this, Kt, "f")[e]
        }
      }, {
        key: "get",
        value: function(e) {
          var t = Qt(this, Kt, "f");
          return e in t ? t[e][0] : null
        }
      }, {
        key: "getAll",
        value: function(e) {
          var t = Qt(this, Kt, "f");
          return e in t ? t[e].slice(0) : []
        }
      }, {
        key: "has",
        value: function(e) {
          return e in Qt(this, Kt, "f")
        }
      }, {
        key: "keys",
        value: function() {
          return Object.keys(Qt(this, Kt, "f"))
        }
      }, {
        key: "set",
        value: function(e, t) {
          Qt(this, Kt, "f")[e] = ["" + t]
        }
      }, {
        key: "forEach",
        value: function(e, t) {
          var n = Qt(this, Kt, "f");
          Object.getOwnPropertyNames(n).forEach((function(r) {
            n[r].forEach((function(n) {
              e.call(t, n, r, this)
            }), this)
          }), this)
        }
      }, {
        key: "toJSON",
        value: function() {
          return {}
        }
      }, {
        key: "toString",
        value: function() {
          var e = Qt(this, Kt, "f"),
            t = [];
          for (var n in e)
            for (var r = sn(n), i = 0, o = e[n]; i < o.length; i++) t.push(r + "=" + sn(o[i]));
          return t.join("&")
        }
      }]);
      Kt = new WeakMap;
      var mn = (0, p.Z)((function e(t, n) {
        (0, h.Z)(this, e), cn.set(this, ""), ln.set(this, ""), dn.set(this, ""), fn.set(this, ""), hn.set(this, ""), pn.set(this, void 0), (0, v.HD)(t) || (t = String(t));
        var r = function(e, t) {
            var n = /^(https?:)\/\//i,
              r = "",
              i = null;
            if (!(0, v.o8)(t)) {
              if (t = String(t).trim(), !n.test(t)) throw new TypeError("Failed to construct 'URL': Invalid base URL");
              i = gn(t)
            }
            if (e = String(e).trim(), n.test(e)) r = e;
            else {
              if (!i) throw new TypeError("Failed to construct 'URL': Invalid URL");
              r = e ? e.startsWith("//") ? i.protocol + e : i.origin + (e.startsWith("/") ? e : "/".concat(e)) : i.href
            }
            return gn(r)
          }(t, n),
          i = r.hash,
          o = r.hostname,
          a = r.pathname,
          u = r.port,
          s = r.protocol,
          c = r.search;
        Xt(this, cn, i, "f"), Xt(this, ln, o, "f"), Xt(this, dn, a || "/", "f"), Xt(this, fn, u, "f"), Xt(this, hn, s, "f"), Xt(this, pn, new vn(c), "f")
      }), [{
        key: "protocol",
        get: function() {
          return Qt(this, hn, "f")
        },
        set: function(e) {
          (0, v.HD)(e) && Xt(this, hn, e.trim(), "f")
        }
      }, {
        key: "host",
        get: function() {
          return this.hostname + (this.port ? ":" + this.port : "")
        },
        set: function(e) {
          if (e && (0, v.HD)(e)) {
            e = e.trim();
            var t = gn("//".concat(e)),
              n = t.hostname,
              r = t.port;
            this.hostname = n, this.port = r
          }
        }
      }, {
        key: "hostname",
        get: function() {
          return Qt(this, ln, "f")
        },
        set: function(e) {
          e && (0, v.HD)(e) && Xt(this, ln, e.trim(), "f")
        }
      }, {
        key: "port",
        get: function() {
          return Qt(this, fn, "f")
        },
        set: function(e) {
          (0, v.HD)(e) && Xt(this, fn, e.trim(), "f")
        }
      }, {
        key: "pathname",
        get: function() {
          return Qt(this, dn, "f")
        },
        set: function(e) {
          if ((0, v.HD)(e)) {
            for (var t = /^(\/|\.\/|\.\.\/)/, n = e = e.trim(); t.test(n);) n = n.replace(t, "");
            Xt(this, dn, n ? "/" + n : "/", "f")
          }
        }
      }, {
        key: "search",
        get: function() {
          var e = Qt(this, pn, "f").toString();
          return 0 === e.length || e.startsWith("?") ? e : "?".concat(e)
        },
        set: function(e) {
          (0, v.HD)(e) && (e = e.trim(), Xt(this, pn, new vn(e), "f"))
        }
      }, {
        key: "hash",
        get: function() {
          return Qt(this, cn, "f")
        },
        set: function(e) {
          (0, v.HD)(e) && (e = e.trim(), Xt(this, cn, e ? e.startsWith("#") ? e : "#".concat(e) : "", "f"))
        }
      }, {
        key: "href",
        get: function() {
          return "".concat(this.protocol, "//").concat(this.host).concat(this.pathname).concat(this.search).concat(this.hash)
        },
        set: function(e) {
          if (e && (0, v.HD)(e)) {
            var t = gn(e = e.trim()),
              n = t.protocol,
              r = t.hostname,
              i = t.port,
              o = t.hash,
              a = t.search,
              u = t.pathname;
            this.protocol = n, this.hostname = r, this.pathname = u, this.port = i, this.hash = o, this.search = a
          }
        }
      }, {
        key: "origin",
        get: function() {
          return "".concat(this.protocol, "//").concat(this.host)
        },
        set: function(e) {
          if (e && (0, v.HD)(e)) {
            var t = gn(e = e.trim()),
              n = t.protocol,
              r = t.hostname,
              i = t.port;
            this.protocol = n, this.hostname = r, this.port = i
          }
        }
      }, {
        key: "searchParams",
        get: function() {
          return Qt(this, pn, "f")
        }
      }, {
        key: "toString",
        value: function() {
          return this.href
        }
      }, {
        key: "toJSON",
        value: function() {
          return this.toString()
        }
      }, {
        key: "_toRaw",
        value: function() {
          return {
            protocol: this.protocol,
            port: this.port,
            host: this.host,
            hostname: this.hostname,
            pathname: this.pathname,
            hash: this.hash,
            search: this.search,
            origin: this.origin,
            href: this.href
          }
        }
      }], [{
        key: "createObjectURL",
        value: function() {
          throw new Error("Oops, not support URL.createObjectURL() in miniprogram.")
        }
      }, {
        key: "revokeObjectURL",
        value: function() {
          throw new Error("Oops, not support URL.revokeObjectURL() in miniprogram.")
        }
      }]);

      function gn() {
        var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "",
          t = {
            href: "",
            origin: "",
            protocol: "",
            hostname: "",
            host: "",
            port: "",
            pathname: "",
            search: "",
            hash: ""
          };
        if (!e || !(0, v.HD)(e)) return t;
        var n = /^(([^:/?#]+):)?\/\/(([^/?#]+):(.+)@)?([^/?#:]*)(:(\d+))?([^?#]*)(\?([^#]*))?(#(.*))?/,
          r = (e = e.trim()).match(n);
        return r ? (t.protocol = r[1] || "https:", t.hostname = r[6] || "taro.com", t.port = r[8] || "", t.pathname = r[9] || "/", t.search = r[10] || "", t.hash = r[12] || "", t.href = e, t.origin = t.protocol + "//" + t.hostname, t.host = t.hostname + (t.port ? ":".concat(t.port) : ""), t) : t
      }
      cn = new WeakMap, ln = new WeakMap, dn = new WeakMap, fn = new WeakMap, hn = new WeakMap, pn = new WeakMap;
      var bn, yn = function(e) {
          function t() {
            return (0, h.Z)(this, t), (0, c.Z)(this, t, arguments)
          }
          return (0, l.Z)(t, e), (0, p.Z)(t, [{
            key: "href",
            get: function() {
              var e;
              return null !== (e = this.props.href) && void 0 !== e ? e : ""
            },
            set: function(e) {
              this.setAttribute("href", e)
            }
          }, {
            key: "protocol",
            get: function() {
              var e;
              return null !== (e = this.props.protocol) && void 0 !== e ? e : ""
            }
          }, {
            key: "host",
            get: function() {
              var e;
              return null !== (e = this.props.host) && void 0 !== e ? e : ""
            }
          }, {
            key: "search",
            get: function() {
              var e;
              return null !== (e = this.props.search) && void 0 !== e ? e : ""
            }
          }, {
            key: "hash",
            get: function() {
              var e;
              return null !== (e = this.props.hash) && void 0 !== e ? e : ""
            }
          }, {
            key: "hostname",
            get: function() {
              var e;
              return null !== (e = this.props.hostname) && void 0 !== e ? e : ""
            }
          }, {
            key: "port",
            get: function() {
              var e;
              return null !== (e = this.props.port) && void 0 !== e ? e : ""
            }
          }, {
            key: "pathname",
            get: function() {
              var e;
              return null !== (e = this.props.pathname) && void 0 !== e ? e : ""
            }
          }, {
            key: "setAttribute",
            value: function(e, n) {
              if ("href" === e) {
                var r = gn(n);
                for (var i in r)(0, o.Z)(t, "setAttribute", this, 3)([i, r[i]])
              } else(0, o.Z)(t, "setAttribute", this, 3)([e, n])
            }
          }])
        }(dt),
        kn = function(e) {
          function t() {
            var e;
            return (0, h.Z)(this, t), (e = (0, c.Z)(this, t)).createEvent = Ht, e.nodeType = 9, e.nodeName = _, e
          }
          return (0, l.Z)(t, e), (0, p.Z)(t, [{
            key: "createElement",
            value: function(e) {
              var t, n = e.toLowerCase();
              switch (!0) {
                case n === S:
                  return t = new Jt;
                case v._c.has(n):
                  t = new Gt;
                  break;
                case n === pe:
                  t = new yn;
                  break;
                default:
                  t = new dt
              }
              return t.nodeName = n, t.tagName = e.toUpperCase(), t
            }
          }, {
            key: "createElementNS",
            value: function(e, t) {
              return this.createElement(t)
            }
          }, {
            key: "createTextNode",
            value: function(e) {
              return new Yt(e)
            }
          }, {
            key: "getElementById",
            value: function(e) {
              var t = Le.get(e);
              return (0, v.o8)(t) ? null : t
            }
          }, {
            key: "querySelector",
            value: function(e) {
              return /^#/.test(e) ? this.getElementById(e.slice(1)) : null
            }
          }, {
            key: "querySelectorAll",
            value: function() {
              return []
            }
          }, {
            key: "createComment",
            value: function() {
              var e = new Yt("");
              return e.nodeName = oe, e
            }
          }, {
            key: "defaultView",
            get: function() {
              return Ze.window
            }
          }])
        }(dt);

      function wn(e) {
        return e.style
      }
      bn = (0, v.gl)() ? Ze.document : Ze.document = function() {
        var e = new kn,
          t = e.createElement.bind(e),
          n = t(P),
          r = t(x),
          i = t(I),
          o = t(N);
        o.id = N;
        var a = t(A);
        return e.appendChild(n), n.appendChild(r), n.appendChild(i), i.appendChild(a), a.appendChild(o), e.documentElement = n, e.head = r, e.body = i, e
      }();
      var Tn, Cn, En, Sn, Pn, xn, In = v.PT.call("getEventCenter", v.zW),
        Nn = (0, p.Z)((function e(t) {
          (0, h.Z)(this, e), this.cache = new Map, this.name = t
        }), [{
          key: "has",
          value: function(e) {
            return this.cache.has(e)
          }
        }, {
          key: "set",
          value: function(e, t) {
            e && t && this.cache.set(e, t)
          }
        }, {
          key: "get",
          value: function(e) {
            if (this.has(e)) return this.cache.get(e)
          }
        }, {
          key: "delete",
          value: function(e) {
            this.cache.delete(e)
          }
        }]),
        An = new Nn("history"),
        _n = function(e) {
          function t(e, n) {
            var i;
            return (0, h.Z)(this, t), i = (0, c.Z)(this, t), Tn.add(i), Cn.set(i, void 0), En.set(i, []), Sn.set(i, 0), Pn.set(i, void 0), Xt(i, Pn, n.window, "f"), Xt(i, Cn, e, "f"), Qt(i, Cn, "f").on("__record_history__", (function(e) {
              var t;
              Xt(i, Sn, (t = Qt(i, Sn, "f"), ++t), "f"), Xt(i, En, Qt(i, En, "f").slice(0, Qt(i, Sn, "f")), "f"), Qt(i, En, "f").push({
                state: null,
                title: "",
                url: e
              })
            }), null), Qt(i, Cn, "f").on("__reset_history__", (function(e) {
              Qt(i, Tn, "m", xn).call(i, e)
            }), null), i.on(r.INIT, (function() {
              Qt(i, Tn, "m", xn).call(i)
            }), null), i.on(r.RESTORE, (function(e) {
              An.set(e, {
                location: Qt(i, Cn, "f"),
                stack: Qt(i, En, "f").slice(),
                cur: Qt(i, Sn, "f")
              })
            }), null), i.on(r.RECOVER, (function(e) {
              if (An.has(e)) {
                var t = An.get(e);
                Xt(i, Cn, t.location, "f"), Xt(i, En, t.stack, "f"), Xt(i, Sn, t.cur, "f")
              }
            }), null), i.on(r.DESTORY, (function(e) {
              An.delete(e)
            }), null), Qt(i, Tn, "m", xn).call(i), i
          }
          return (0, l.Z)(t, e), (0, p.Z)(t, [{
            key: "length",
            get: function() {
              return Qt(this, En, "f").length
            }
          }, {
            key: "state",
            get: function() {
              return Qt(this, En, "f")[Qt(this, Sn, "f")].state
            }
          }, {
            key: "go",
            value: function(e) {
              if ((0, v.hj)(e) && !isNaN(e)) {
                var t = Qt(this, Sn, "f") + e;
                t = Math.min(Math.max(t, 0), this.length - 1), Xt(this, Sn, t, "f"), Qt(this, Cn, "f").trigger("__set_href_without_history__", Qt(this, En, "f")[Qt(this, Sn, "f")].url), Qt(this, Pn, "f").trigger("popstate", Qt(this, En, "f")[Qt(this, Sn, "f")])
              }
            }
          }, {
            key: "back",
            value: function() {
              this.go(-1)
            }
          }, {
            key: "forward",
            value: function() {
              this.go(1)
            }
          }, {
            key: "pushState",
            value: function(e, t, n) {
              n && (0, v.HD)(n) && (Xt(this, En, Qt(this, En, "f").slice(0, Qt(this, Sn, "f") + 1), "f"), Qt(this, En, "f").push({
                state: e,
                title: t,
                url: n
              }), Xt(this, Sn, this.length - 1, "f"), Qt(this, Cn, "f").trigger("__set_href_without_history__", n))
            }
          }, {
            key: "replaceState",
            value: function(e, t, n) {
              n && (0, v.HD)(n) && (Qt(this, En, "f")[Qt(this, Sn, "f")] = {
                state: e,
                title: t,
                url: n
              }, Qt(this, Cn, "f").trigger("__set_href_without_history__", n))
            }
          }, {
            key: "cache",
            get: function() {
              return An
            }
          }])
        }(v.zW);
      Cn = new WeakMap, En = new WeakMap, Sn = new WeakMap, Pn = new WeakMap, Tn = new WeakSet, xn = function() {
        var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "";
        Xt(this, En, [{
          state: null,
          title: "",
          url: e || Qt(this, Cn, "f").href
        }], "f"), Xt(this, Sn, 0, "f")
      };
      var On, Ln, Rn, Zn, Dn, Bn, Mn, jn, Un, Fn = {
          app: null,
          router: null,
          page: null
        },
        Hn = function() {
          return Fn
        },
        Wn = "https://taro.com",
        Vn = new Nn("location"),
        zn = function(e) {
          function t(e) {
            var n;
            return (0, h.Z)(this, t), n = (0, c.Z)(this, t), On.add(n), Ln.set(n, new mn(Wn)), Rn.set(n, !1), Zn.set(n, void 0), Xt(n, Zn, e.window, "f"), Qt(n, On, "m", Dn).call(n), n.on("__set_href_without_history__", (function(e) {
              Xt(n, Rn, !0, "f");
              var t = Qt(n, Ln, "f").hash;
              Qt(n, Ln, "f").href = function() {
                var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "",
                  t = Wn;
                return /^[/?#]/.test(e) ? t + e : e
              }(e), t !== Qt(n, Ln, "f").hash && Qt(n, Zn, "f").trigger("hashchange"), Xt(n, Rn, !1, "f")
            }), null), n.on(r.INIT, (function() {
              Qt(n, On, "m", Dn).call(n)
            }), null), n.on(r.RESTORE, (function(e) {
              Vn.set(e, {
                lastHref: n.href
              })
            }), null), n.on(r.RECOVER, (function(e) {
              if (Vn.has(e)) {
                var t = Vn.get(e);
                Xt(n, Rn, !0, "f"), Qt(n, Ln, "f").href = t.lastHref, Xt(n, Rn, !1, "f")
              }
            }), null), n.on(r.DESTORY, (function(e) {
              Vn.delete(e)
            }), null), n
          }
          return (0, l.Z)(t, e), (0, p.Z)(t, [{
            key: "protocol",
            get: function() {
              return Qt(this, Ln, "f").protocol
            },
            set: function(e) {
              if (e && (0, v.HD)(e) && /^(http|https):$/i.test(e.trim())) {
                e = e.trim();
                var t = Qt(this, On, "m", Bn).call(this);
                Qt(this, Ln, "f").protocol = e, Qt(this, On, "m", Un).call(this, t) && Qt(this, On, "m", jn).call(this)
              }
            }
          }, {
            key: "host",
            get: function() {
              return Qt(this, Ln, "f").host
            },
            set: function(e) {
              if (e && (0, v.HD)(e)) {
                e = e.trim();
                var t = Qt(this, On, "m", Bn).call(this);
                Qt(this, Ln, "f").host = e, Qt(this, On, "m", Un).call(this, t) && Qt(this, On, "m", jn).call(this)
              }
            }
          }, {
            key: "hostname",
            get: function() {
              return Qt(this, Ln, "f").hostname
            },
            set: function(e) {
              if (e && (0, v.HD)(e)) {
                e = e.trim();
                var t = Qt(this, On, "m", Bn).call(this);
                Qt(this, Ln, "f").hostname = e, Qt(this, On, "m", Un).call(this, t) && Qt(this, On, "m", jn).call(this)
              }
            }
          }, {
            key: "port",
            get: function() {
              return Qt(this, Ln, "f").port
            },
            set: function(e) {
              var t = Number(e = e.trim());
              if ((0, v.hj)(t) && !(t <= 0)) {
                var n = Qt(this, On, "m", Bn).call(this);
                Qt(this, Ln, "f").port = e, Qt(this, On, "m", Un).call(this, n) && Qt(this, On, "m", jn).call(this)
              }
            }
          }, {
            key: "pathname",
            get: function() {
              return Qt(this, Ln, "f").pathname
            },
            set: function(e) {
              if (e && (0, v.HD)(e)) {
                e = e.trim();
                var t = Qt(this, On, "m", Bn).call(this);
                Qt(this, Ln, "f").pathname = e, Qt(this, On, "m", Un).call(this, t) && Qt(this, On, "m", jn).call(this)
              }
            }
          }, {
            key: "search",
            get: function() {
              return Qt(this, Ln, "f").search
            },
            set: function(e) {
              if (e && (0, v.HD)(e)) {
                e = (e = e.trim()).startsWith("?") ? e : "?".concat(e);
                var t = Qt(this, On, "m", Bn).call(this);
                Qt(this, Ln, "f").search = e, Qt(this, On, "m", Un).call(this, t) && Qt(this, On, "m", jn).call(this)
              }
            }
          }, {
            key: "hash",
            get: function() {
              return Qt(this, Ln, "f").hash
            },
            set: function(e) {
              if (e && (0, v.HD)(e)) {
                e = (e = e.trim()).startsWith("#") ? e : "#".concat(e);
                var t = Qt(this, On, "m", Bn).call(this);
                Qt(this, Ln, "f").hash = e, Qt(this, On, "m", Un).call(this, t) && Qt(this, On, "m", jn).call(this)
              }
            }
          }, {
            key: "href",
            get: function() {
              return Qt(this, Ln, "f").href
            },
            set: function(e) {
              if (e && (0, v.HD)(e) && /^(http:|https:)?\/\/.+/.test(e = e.trim())) {
                var t = Qt(this, On, "m", Bn).call(this);
                Qt(this, Ln, "f").href = e, Qt(this, On, "m", Un).call(this, t) && Qt(this, On, "m", jn).call(this)
              }
            }
          }, {
            key: "origin",
            get: function() {
              return Qt(this, Ln, "f").origin
            },
            set: function(e) {
              if (e && (0, v.HD)(e) && /^(http:|https:)?\/\/.+/.test(e = e.trim())) {
                var t = Qt(this, On, "m", Bn).call(this);
                Qt(this, Ln, "f").origin = e, Qt(this, On, "m", Un).call(this, t) && Qt(this, On, "m", jn).call(this)
              }
            }
          }, {
            key: "assign",
            value: function() {
              (0, v.ZK)(!0, "小程序环境中调用location.assign()无效.")
            }
          }, {
            key: "reload",
            value: function() {
              (0, v.ZK)(!0, "小程序环境中调用location.reload()无效.")
            }
          }, {
            key: "replace",
            value: function(e) {
              this.trigger("__set_href_without_history__", e)
            }
          }, {
            key: "toString",
            value: function() {
              return this.href
            }
          }, {
            key: "cache",
            get: function() {
              return Vn
            }
          }])
        }(v.zW);
      Ln = new WeakMap, Rn = new WeakMap, Zn = new WeakMap, On = new WeakSet, Dn = function() {
        var e = Hn().router;
        if (e) {
          var t = e.path,
            n = e.params,
            r = Object.keys(n).map((function(e) {
              return "".concat(e, "=").concat(n[e])
            })),
            i = r.length > 0 ? "?" + r.join("&") : "",
            o = "".concat(Wn).concat(t.startsWith("/") ? t : "/" + t).concat(i);
          Xt(this, Ln, new mn(o), "f"), this.trigger("__reset_history__", this.href)
        }
      }, Bn = function() {
        return Qt(this, Ln, "f")._toRaw()
      }, Mn = function(e) {
        Qt(this, Ln, "f").href = e
      }, jn = function() {
        this.trigger("__record_history__", this.href)
      }, Un = function(e) {
        if (Qt(this, Rn, "f")) return !1;
        var t = Qt(this, Ln, "f")._toRaw(),
          n = t.protocol,
          r = t.hostname,
          i = t.port,
          o = t.pathname,
          a = t.search,
          u = t.hash;
        return n !== e.protocol || r !== e.hostname || i !== e.port ? (Qt(this, On, "m", Mn).call(this, e.href), !1) : o !== e.pathname || a !== e.search || (u !== e.hash ? (Qt(this, Zn, "f").trigger("hashchange"), !0) : (Qt(this, On, "m", Mn).call(this, e.href), !1))
      };
      var Gn, qn = "(Macintosh; Intel Mac OS X 10_14_5) AppleWebKit/534.36 (KHTML, like Gecko) NodeJS/v4.1.0 Chrome/76.0.3809.132 Safari/534.36",
        $n = (0, v.gl)() ? Ze.window.navigator : {
          appCodeName: "Mozilla",
          appName: "Netscape",
          appVersion: "5.0 " + qn,
          cookieEnabled: !0,
          mimeTypes: [],
          onLine: !0,
          platform: "MacIntel",
          plugins: [],
          product: "Taro",
          productSub: "20030107",
          userAgent: "Mozilla/5.0 " + qn,
          vendor: "Joyent",
          vendorSub: ""
        };
      ! function() {
        var e;
        "undefined" != typeof performance && null !== performance && performance.now ? Gn = function() {
          return performance.now()
        } : Date.now ? (e = Date.now(), Gn = function() {
          return Date.now() - e
        }) : (e = (new Date).getTime(), Gn = function() {
          return (new Date).getTime() - e
        })
      }();
      var Kn, Jn = 0,
        Yn = null != b ? b : function(e) {
          var t = Gn(),
            n = Math.max(Jn + 16, t);
          return setTimeout((function() {
            e(Jn = n)
          }), n - t)
        },
        Qn = null != y ? y : function(e) {
          clearTimeout(e)
        };
      if ((0, v.gl)()) Kn = Ze.window;
      else {
        var Xn = function(e) {
          function t() {
            var e;
            return (0, h.Z)(this, t), (e = (0, c.Z)(this, t)).navigator = $n, e.requestAnimationFrame = Yn, e.cancelAnimationFrame = Qn, e.getComputedStyle = wn, [].concat((0, f.Z)(Object.getOwnPropertyNames(n.g || {})), (0, f.Z)(Object.getOwnPropertySymbols(n.g || {}))).forEach((function(t) {
              if ("atob" !== t && "document" !== t && !Object.prototype.hasOwnProperty.call(e, t)) try {
                e[t] = n.g[t]
              } catch (e) {}
            })), e.Date || (e.Date = Date), e.location = new zn({
              window: e
            }), e.history = new _n(e.location, {
              window: e
            }), e.initEvent(), e
          }
          return (0, l.Z)(t, e), (0, p.Z)(t, [{
            key: "initEvent",
            value: function() {
              var e = this.location,
                t = this.history;
              this.on(r.INIT, (function(t) {
                e.trigger(r.INIT, t)
              }), null), this.on(r.RECOVER, (function(n) {
                e.trigger(r.RECOVER, n), t.trigger(r.RECOVER, n)
              }), null), this.on(r.RESTORE, (function(n) {
                e.trigger(r.RESTORE, n), t.trigger(r.RESTORE, n)
              }), null), this.on(r.DESTORY, (function(n) {
                e.trigger(r.DESTORY, n), t.trigger(r.DESTORY, n)
              }), null)
            }
          }, {
            key: "document",
            get: function() {
              return Ze.document
            }
          }, {
            key: "addEventListener",
            value: function(e, t) {
              (0, v.HD)(e) && this.on(e, t, null)
            }
          }, {
            key: "removeEventListener",
            value: function(e, t) {
              (0, v.HD)(e) && this.off(e, t, null)
            }
          }, {
            key: "setTimeout",
            value: function(e) {
              function t() {
                return e.apply(this, arguments)
              }
              return t.toString = function() {
                return e.toString()
              }, t
            }((function() {
              return setTimeout.apply(void 0, arguments)
            }))
          }, {
            key: "clearTimeout",
            value: function(e) {
              function t() {
                return e.apply(this, arguments)
              }
              return t.toString = function() {
                return e.toString()
              }, t
            }((function() {
              return clearTimeout.apply(void 0, arguments)
            }))
          }])
        }(v.zW);
        Kn = Ze.window = new Xn
      }
      var er = Kn.location,
        tr = Kn.history,
        nr = function(e) {
          function t() {
            return (0, h.Z)(this, t), (0, c.Z)(this, t, arguments)
          }
          return (0, l.Z)(t, e), (0, p.Z)(t)
        }(dt),
        rr = new Map,
        ir = we(),
        or = (0, v.gl)();

      function ar(e, t) {
        v.PT.call("mergePageInstance", rr.get(t), e), rr.set(t, e)
      }

      function ur(e) {
        return rr.get(e)
      }

      function sr(e) {
        rr.delete(e)
      }

      function cr(e) {
        return null == e ? "" : "/" === e.charAt(0) ? e : "/" + e
      }

      function lr(e, t) {
        for (var n = arguments.length, r = new Array(n > 2 ? n - 2 : 0), i = 2; i < n; i++) r[i - 2] = arguments[i];
        var o = rr.get(e);
        if (null != o) {
          var a = v.PT.call("getLifecycle", o, t);
          if ((0, v.kJ)(a)) {
            var u = a.map((function(e) {
              return e.apply(o, r)
            }));
            return u[0]
          }
          if ((0, v.mf)(a)) return a.apply(o, r)
        }
      }

      function dr(e) {
        if (null == e) return "";
        var t = Object.keys(e).map((function(t) {
          return t + "=" + e[t]
        })).join("&");
        return "" === t ? t : "?" + t
      }

      function fr(e, t) {
        var n = e.indexOf("?");
        return or ? "".concat(n > -1 ? e.substring(0, n) : e).concat(dr((null == t ? void 0 : t.stamp) ? {
          stamp: t.stamp
        } : {})) : "".concat(n > -1 ? e.substring(0, n) : e).concat(dr(t))
      }

      function hr(e) {
        return e + "." + ue
      }

      function pr(e) {
        return e + "." + se
      }

      function vr(e) {
        return e + "." + ce
      }

      function mr(e, t, n, o) {
        var a, u, c = null != t ? t : "taro_page_".concat(ir()),
          l = (0, i.Z)(v.PT.call("getMiniLifecycleImpl").page, 7),
          d = l[0],
          h = l[1],
          p = l[2],
          m = l[3],
          g = l[4],
          b = l[5],
          y = l[6],
          k = null,
          w = !1,
          T = [];

        function C(e) {
          var t = or ? e.$taroPath : e.route || e.__route__ || e.$taroPath;
          Fn.router = {
            params: e.$taroParams,
            path: cr(t),
            $taroPath: e.$taroPath,
            onReady: hr(c),
            onShow: pr(c),
            onHide: vr(c)
          }, (0, v.o8)(e.exitState) || (Fn.router.exitState = e.exitState)
        }
        var S = (0, s.Z)((0, s.Z)((0, s.Z)((0, s.Z)((0, s.Z)({}, d, (function() {
          var t = this,
            n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
            i = arguments.length > 1 ? arguments[1] : void 0;
          u = new Promise((function(e) {
            a = e
          })), qt.start(E), Fn.page = this, this.config = o || {};
          var s = Object.assign({}, n, {
              $taroTimestamp: Date.now()
            }),
            l = this.$taroPath = fr(c, s);
          or && (S.path = l), null == this.$taroParams && (this.$taroParams = s), C(this), or || Kn.trigger(r.INIT, l);
          var d = function() {
            Fn.app.mount(e, l, (function() {
              k = Ze.document.getElementById(l), (0, v.zx)(null !== k, "没有找到页面实例。"), lr(l, ae, t.$taroParams), a(), or ? (0, v.mf)(i) && i() : (k.ctx = t, k.performUpdate(!0, i))
            }))
          };
          w ? T.push(d) : d()
        })), h, (function() {
          var e = this.$taroPath;
          or || Kn.trigger(r.DESTORY, e), lr(e, h), w = !0, Fn.app.unmount(e, (function() {
            w = !1, rr.delete(e), k && (k.ctx = null, k = null), T.length && (T.forEach((function(e) {
              return e()
            })), T = [])
          }))
        })), p, (function() {
          var e = this;
          u.then((function() {
            lr(e.$taroPath, ue), Yn((function() {
              return In.trigger(hr(c))
            })), e.onReady.called = !0
          }))
        })), m, (function() {
          var e = this,
            t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
          u.then((function() {
            Fn.page = e, C(e), or || Kn.trigger(r.RECOVER, e.$taroPath), lr(e.$taroPath, se, t), Yn((function() {
              return In.trigger(pr(c))
            }))
          }))
        })), g, (function() {
          or || Kn.trigger(r.RESTORE, this.$taroPath), Fn.page === this && (Fn.page = null, Fn.router = null), lr(this.$taroPath, ce), In.trigger(vr(c))
        }));
        return b.forEach((function(e) {
          var t = !1;
          e = e.replace(/^defer:/, (function() {
            return t = !0, ""
          })), S[e] = function() {
            var n = arguments,
              r = this,
              i = function() {
                return lr.apply(void 0, [r.$taroPath, e].concat((0, f.Z)(n)))
              };
            if (!t) return i();
            u.then(i)
          }
        })), y.forEach((function(t) {
          var n;
          (e[t] || (null === (n = e.prototype) || void 0 === n ? void 0 : n[t]) || e[t.replace(/^on/, "enable")] || (null == o ? void 0 : o[t.replace(/^on/, "enable")])) && (S[t] = function() {
            for (var e, n = arguments.length, r = new Array(n), i = 0; i < n; i++) r[i] = arguments[i];
            var o = null === (e = r[0]) || void 0 === e ? void 0 : e.target;
            if (null == o ? void 0 : o.id) {
              var a = o.id,
                u = Ze.document.getElementById(a);
              u && (o.dataset = u.dataset)
            }
            return lr.apply(void 0, [this.$taroPath, t].concat(r))
          })
        })), S.eh = zt, (0, v.o8)(n) || (S.data = n), v.PT.call("modifyPageObject", S), S
      }

      function gr(e, t, n) {
        var r = null != t ? t : "taro_component_".concat(ir()),
          o = null,
          a = (0, i.Z)(v.PT.call("getMiniLifecycleImpl").component, 2),
          u = a[0],
          c = a[1],
          l = (0, s.Z)((0, s.Z)((0, s.Z)({}, u, (function() {
            var t, n = this;
            qt.start(E), this.pageIdCache = (null === (t = this.getPageId) || void 0 === t ? void 0 : t.call(this)) || ir();
            var i = fr(r, {
              id: this.pageIdCache
            });
            Fn.app.mount(e, i, (function() {
              o = Ze.document.getElementById(i), (0, v.zx)(null !== o, "没有找到组件实例。"), n.$taroInstances = rr.get(i), lr(i, ae), or || (o.ctx = n, o.performUpdate(!0))
            }))
          })), c, (function() {
            var e = fr(r, {
              id: this.pageIdCache
            });
            Fn.app.unmount(e, (function() {
              rr.delete(e), o && (o.ctx = null)
            }))
          })), "methods", {
            eh: zt
          });
        return (0, v.o8)(n) || (l.data = n), [le, de, he].forEach((function(t) {
          var n;
          l[t] = null !== (n = e[t]) && void 0 !== n ? n : v.kT
        })), l
      }

      function br(e) {
        var t = e === q,
          n = (0, i.Z)(v.PT.call("getMiniLifecycleImpl").component, 2),
          r = n[0],
          o = n[1],
          a = t ? (0, s.Z)((0, s.Z)({}, r, (function() {
            var e, t, n = (null === (e = this.data.i) || void 0 === e ? void 0 : e.sid) || (null === (t = this.props.i) || void 0 === t ? void 0 : t.sid);
            if ((0, v.HD)(n)) {
              xe.set(n, this);
              var r = Ze.document.getElementById(n);
              r && (r.ctx = this)
            }
          })), o, (function() {
            var e, t, n = (null === (e = this.data.i) || void 0 === e ? void 0 : e.sid) || (null === (t = this.props.i) || void 0 === t ? void 0 : t.sid);
            if ((0, v.HD)(n)) {
              xe.delete(n);
              var r = Ze.document.getElementById(n);
              r && (r.ctx = null)
            }
          })) : v.kT;
        return Object.assign({
          properties: {
            i: {
              type: Object,
              value: (0, s.Z)({}, "nn", (0, v.W)(v.rD)[M]._num)
            },
            l: {
              type: String,
              value: ""
            }
          },
          options: {
            addGlobalClass: !0,
            virtualHost: !t
          },
          methods: {
            eh: zt
          }
        }, a)
      }
      var yr = function(e, t) {
        var n = Date.now(),
          r = Fn.router,
          i = function() {
            setTimeout((function() {
              t ? e.call(t) : e()
            }), 1)
          };
        if (null === r) return i();
        var o = r.$taroPath;
        ! function r() {
          var a, u, s, c = Ze.document.getElementById(o);
          (null == c ? void 0 : c.pendingUpdate) ? (0, v.gl)() ? null !== (s = null === (u = null === (a = c.firstChild) || void 0 === a ? void 0 : a.componentOnReady) || void 0 === u ? void 0 : u.call(a).then((function() {
            i()
          }))) && void 0 !== s || i(): c.enqueueUpdateCallback(e, t): Date.now() - n > 100 ? i() : setTimeout((function() {
            return r()
          }), 20)
        }()
      }
    },
    3295: function(e, t, n) {
      "use strict";
      n.d(t, {
        Z6: function() {
          return B
        },
        kT: function() {
          return D
        },
        zW: function() {
          return T
        },
        kC: function() {
          return F
        },
        _c: function() {
          return w
        },
        zx: function() {
          return H
        },
        W: function() {
          return q
        },
        PT: function() {
          return Z
        },
        rD: function() {
          return k
        },
        kJ: function() {
          return _
        },
        jn: function() {
          return x
        },
        mf: function() {
          return I
        },
        Ft: function() {
          return S
        },
        hj: function() {
          return N
        },
        Kn: function() {
          return P
        },
        HD: function() {
          return C
        },
        o8: function() {
          return E
        },
        gl: function() {
          return O
        },
        ku: function() {
          return G
        },
        xi: function() {
          return $
        },
        ZT: function() {
          return M
        },
        Ig: function() {
          return ee
        },
        CA: function() {
          return U
        },
        eu: function() {
          return j
        },
        ZK: function() {
          return W
        }
      });
      var r = n(8079),
        i = n(4826),
        o = n(5754),
        a = n(6522),
        u = n(9249),
        s = n(7371),
        c = "[]",
        l = "",
        d = "!0",
        f = "!1",
        h = {
          bindTouchStart: l,
          bindTouchMove: l,
          bindTouchEnd: l,
          bindTouchCancel: l,
          bindLongTap: l
        },
        p = {
          animation: l,
          bindAnimationStart: l,
          bindAnimationIteration: l,
          bindAnimationEnd: l,
          bindTransitionEnd: l
        };

      function v(e) {
        return "'".concat(e, "'")
      }
      var m, g = Object.assign(Object.assign({
          "hover-class": v("none"),
          "hover-stop-propagation": f,
          "hover-start-time": "50",
          "hover-stay-time": "400"
        }, h), p),
        b = {
          type: l,
          size: "23",
          color: l
        },
        y = Object.assign({
          longitude: l,
          latitude: l,
          scale: "16",
          markers: c,
          covers: l,
          polyline: c,
          circles: c,
          controls: c,
          "include-points": c,
          "show-location": l,
          "layer-style": "1",
          bindMarkerTap: l,
          bindControlTap: l,
          bindCalloutTap: l,
          bindUpdated: l
        }, h),
        k = {
          View: g,
          Icon: b,
          Progress: {
            percent: l,
            "stroke-width": "6",
            color: v("#09BB07"),
            activeColor: v("#09BB07"),
            backgroundColor: v("#EBEBEB"),
            active: f,
            "active-mode": v("backwards"),
            "show-info": f
          },
          RichText: {
            nodes: c
          },
          Text: {
            selectable: f,
            space: l,
            decode: f
          },
          Button: Object.assign({
            size: v("default"),
            type: l,
            plain: f,
            disabled: l,
            loading: f,
            "form-type": l,
            "open-type": l,
            "hover-class": v("button-hover"),
            "hover-stop-propagation": f,
            "hover-start-time": "20",
            "hover-stay-time": "70",
            name: l
          }, h),
          Checkbox: {
            value: l,
            disabled: l,
            checked: f,
            color: v("#09BB07"),
            name: l
          },
          CheckboxGroup: {
            bindChange: l,
            name: l
          },
          Form: {
            "report-submit": f,
            bindSubmit: l,
            bindReset: l,
            name: l
          },
          Input: {
            value: l,
            type: v(l),
            password: f,
            placeholder: l,
            "placeholder-style": l,
            "placeholder-class": v("input-placeholder"),
            disabled: l,
            maxlength: "140",
            "cursor-spacing": "0",
            focus: f,
            "confirm-type": v("done"),
            "confirm-hold": f,
            cursor: "-1",
            "selection-start": "-1",
            "selection-end": "-1",
            bindInput: l,
            bindFocus: l,
            bindBlur: l,
            bindConfirm: l,
            name: l
          },
          Label: {
            for: l,
            name: l
          },
          Picker: {
            mode: v("selector"),
            disabled: l,
            range: l,
            "range-key": l,
            value: l,
            start: l,
            end: l,
            fields: v("day"),
            "custom-item": l,
            name: l,
            bindCancel: l,
            bindChange: l,
            bindColumnChange: l
          },
          PickerView: {
            value: l,
            "indicator-style": l,
            "indicator-class": l,
            "mask-style": l,
            "mask-class": l,
            bindChange: l,
            name: l
          },
          PickerViewColumn: {
            name: l
          },
          Radio: {
            value: l,
            checked: f,
            disabled: l,
            color: v("#09BB07"),
            name: l
          },
          RadioGroup: {
            bindChange: l,
            name: l
          },
          Slider: {
            min: "0",
            max: "100",
            step: "1",
            disabled: l,
            value: "0",
            activeColor: v("#1aad19"),
            backgroundColor: v("#e9e9e9"),
            "block-size": "28",
            "block-color": v("#ffffff"),
            "show-value": f,
            bindChange: l,
            bindChanging: l,
            name: l
          },
          Switch: {
            checked: f,
            disabled: l,
            type: v("switch"),
            color: v("#04BE02"),
            bindChange: l,
            name: l
          },
          CoverImage: {
            src: l,
            bindLoad: "eh",
            bindError: "eh"
          },
          Textarea: {
            value: l,
            placeholder: l,
            "placeholder-style": l,
            "placeholder-class": v("textarea-placeholder"),
            disabled: l,
            maxlength: "140",
            "auto-focus": f,
            focus: f,
            "auto-height": f,
            fixed: f,
            "cursor-spacing": "0",
            cursor: "-1",
            "selection-start": "-1",
            "selection-end": "-1",
            bindFocus: l,
            bindBlur: l,
            bindLineChange: l,
            bindInput: l,
            bindConfirm: l,
            name: l
          },
          CoverView: Object.assign({
            "scroll-top": f
          }, h),
          MovableArea: {
            "scale-area": f
          },
          MovableView: Object.assign(Object.assign({
            direction: "none",
            inertia: f,
            "out-of-bounds": f,
            x: l,
            y: l,
            damping: "20",
            friction: "2",
            disabled: l,
            scale: f,
            "scale-min": "0.5",
            "scale-max": "10",
            "scale-value": "1",
            bindChange: l,
            bindScale: l,
            bindHTouchMove: l,
            bindVTouchMove: l,
            width: v("10px"),
            height: v("10px")
          }, h), p),
          ScrollView: Object.assign(Object.assign({
            "scroll-x": f,
            "scroll-y": f,
            "upper-threshold": "50",
            "lower-threshold": "50",
            "scroll-top": l,
            "scroll-left": l,
            "scroll-into-view": l,
            "scroll-with-animation": f,
            "enable-back-to-top": f,
            bindScrollToUpper: l,
            bindScrollToLower: l,
            bindScroll: l
          }, h), p),
          Swiper: Object.assign({
            "indicator-dots": f,
            "indicator-color": v("rgba(0, 0, 0, .3)"),
            "indicator-active-color": v("#000000"),
            autoplay: f,
            current: "0",
            interval: "5000",
            duration: "500",
            circular: f,
            vertical: f,
            "previous-margin": v("0px"),
            "next-margin": v("0px"),
            "display-multiple-items": "1",
            bindChange: l,
            bindTransition: l,
            bindAnimationFinish: l
          }, h),
          SwiperItem: {
            "item-id": l
          },
          Navigator: {
            url: l,
            "open-type": v("navigate"),
            delta: "1",
            "hover-class": v("navigator-hover"),
            "hover-stop-propagation": f,
            "hover-start-time": "50",
            "hover-stay-time": "600",
            bindSuccess: l,
            bindFail: l,
            bindComplete: l
          },
          Audio: {
            id: l,
            src: l,
            loop: f,
            controls: f,
            poster: l,
            name: l,
            author: l,
            bindError: l,
            bindPlay: l,
            bindPause: l,
            bindTimeUpdate: l,
            bindEnded: l
          },
          Camera: {
            "device-position": v("back"),
            flash: v("auto"),
            bindStop: l,
            bindError: l
          },
          Image: Object.assign({
            src: l,
            mode: v("scaleToFill"),
            "lazy-load": f,
            bindError: l,
            bindLoad: l
          }, h),
          LivePlayer: Object.assign({
            src: l,
            autoplay: f,
            muted: f,
            orientation: v("vertical"),
            "object-fit": v("contain"),
            "background-mute": f,
            "min-cache": "1",
            "max-cache": "3",
            bindStateChange: l,
            bindFullScreenChange: l,
            bindNetStatus: l
          }, p),
          Video: Object.assign({
            src: l,
            duration: l,
            controls: d,
            "danmu-list": l,
            "danmu-btn": l,
            "enable-danmu": l,
            autoplay: f,
            loop: f,
            muted: f,
            "initial-time": "0",
            "page-gesture": f,
            direction: l,
            "show-progress": d,
            "show-fullscreen-btn": d,
            "show-play-btn": d,
            "show-center-play-btn": d,
            "enable-progress-gesture": d,
            "object-fit": v("contain"),
            poster: l,
            "show-mute-btn": f,
            bindPlay: l,
            bindPause: l,
            bindEnded: l,
            bindTimeUpdate: l,
            bindFullScreenChange: l,
            bindWaiting: l,
            bindError: l
          }, p),
          Canvas: Object.assign({
            "canvas-id": l,
            "disable-scroll": f,
            bindError: l
          }, h),
          Ad: {
            "unit-id": l,
            "ad-intervals": l,
            bindLoad: l,
            bindError: l,
            bindClose: l
          },
          WebView: {
            src: l,
            bindMessage: l,
            bindLoad: l,
            bindError: l
          },
          Block: {},
          Map: y,
          Slot: {
            name: l
          },
          SlotView: {
            name: l
          },
          NativeSlot: {
            name: l
          }
        },
        w = new Set(["input", "checkbox", "picker", "picker-view", "radio", "slider", "switch", "textarea"]);
      new Set(["input", "textarea"]), new Set(["progress", "icon", "rich-text", "input", "textarea", "slider", "switch", "audio", "ad", "official-account", "open-data", "navigation-bar"]), new Map([
          ["view", -1],
          ["catch-view", -1],
          ["cover-view", -1],
          ["static-view", -1],
          ["pure-view", -1],
          ["block", -1],
          ["text", -1],
          ["static-text", 6],
          ["slot", 8],
          ["slot-view", 8],
          ["label", 6],
          ["form", 4],
          ["scroll-view", 4],
          ["swiper", 4],
          ["swiper-item", 4]
        ]),
        function(e) {
          e.MINI = "mini", e.WEB = "web", e.RN = "rn", e.HARMONY = "harmony", e.QUICK = "quickapp"
        }(m || (m = {})), m.WEB, m.HARMONY, m.MINI, m.RN, m.QUICK;
      var T = function() {
        function e(t) {
          var n;
          (0, u.Z)(this, e), this.callbacks = null !== (n = null == t ? void 0 : t.callbacks) && void 0 !== n ? n : {}
        }
        return (0, s.Z)(e, [{
          key: "on",
          value: function(t, n, r) {
            var i, o, u;
            if (!n) return this;
            u = "symbol" === (0, a.Z)(t) ? [t] : t.split(e.eventSplitter), this.callbacks || (this.callbacks = {});
            for (var s = this.callbacks; i = u.shift();) {
              var c = s[i],
                l = c ? c.tail : {};
              l.next = o = {}, l.context = r, l.callback = n, s[i] = {
                tail: o,
                next: c ? c.next : l
              }
            }
            return this
          }
        }, {
          key: "once",
          value: function(e, t, n) {
            var r = this,
              i = function() {
                for (var o = arguments.length, a = new Array(o), u = 0; u < o; u++) a[u] = arguments[u];
                t.apply(r, a), r.off(e, i, n)
              };
            return this.on(e, i, n), this
          }
        }, {
          key: "off",
          value: function(t, n, r) {
            var i, o, u;
            if (!(o = this.callbacks)) return this;
            if (!(t || n || r)) return delete this.callbacks, this;
            for (u = "symbol" === (0, a.Z)(t) ? [t] : t ? t.split(e.eventSplitter) : Object.keys(o); i = u.shift();) {
              var s = o[i];
              if (delete o[i], s && (n || r))
                for (var c = s.tail;
                  (s = s.next) !== c;) {
                  var l = s.callback,
                    d = s.context;
                  (n && l !== n || r && d !== r) && this.on(i, l, d)
                }
            }
            return this
          }
        }, {
          key: "trigger",
          value: function(t) {
            var n, r, i, o;
            if (!(i = this.callbacks)) return this;
            o = "symbol" === (0, a.Z)(t) ? [t] : t.split(e.eventSplitter);
            for (var u = arguments.length, s = new Array(u > 1 ? u - 1 : 0), c = 1; c < u; c++) s[c - 1] = arguments[c];
            for (; n = o.shift();)
              if (r = i[n])
                for (var l = r.tail;
                  (r = r.next) !== l;) r.callback.apply(r.context || this, s);
            return this
          }
        }])
      }();

      function C(e) {
        return "string" == typeof e
      }

      function E(e) {
        return void 0 === e
      }

      function S(e) {
        return null === e
      }

      function P(e) {
        return null !== e && "object" === (0, a.Z)(e)
      }

      function x(e) {
        return !0 === e || !1 === e
      }

      function I(e) {
        return "function" == typeof e
      }

      function N(e) {
        return "number" == typeof e
      }
      T.eventSplitter = ",";
      var A, _ = Array.isArray,
        O = function() {
          return !1
        };
      ! function(e) {
        e[e.SINGLE = 0] = "SINGLE", e[e.MULTI = 1] = "MULTI", e[e.WATERFALL = 2] = "WATERFALL"
      }(A || (A = {}));
      var L = {
        app: ["onLaunch", "onShow", "onHide"],
        page: ["onLoad", "onUnload", "onReady", "onShow", "onHide", ["onPullDownRefresh", "onReachBottom", "onPageScroll", "onResize", "defer:onTabItemTap", "onTitleClick", "onOptionMenuClick", "onPopMenuClick", "onPullIntercept", "onAddToFavorites"],
          ["onShareAppMessage", "onShareTimeline"]
        ],
        component: ["attached", "detached"]
      };

      function R(e, t) {
        return {
          type: e,
          initial: t || null
        }
      }
      var Z = new(function(e) {
          function t(e, n) {
            var r;
            for (var o in (0, u.Z)(this, t), (r = (0, i.Z)(this, t, [n])).hooks = e, e) {
              var a = e[o].initial;
              I(a) && r.on(o, a)
            }
            return r
          }
          return (0, o.Z)(t, e), (0, s.Z)(t, [{
            key: "tapOneOrMany",
            value: function(e, t) {
              var n = this;
              (I(t) ? [t] : t).forEach((function(t) {
                return n.on(e, t)
              }))
            }
          }, {
            key: "tap",
            value: function(e, t) {
              var n = this.hooks[e],
                r = n.type,
                i = n.initial;
              r === A.SINGLE ? (this.off(e), this.on(e, I(t) ? t : t[t.length - 1])) : (i && this.off(e, i), this.tapOneOrMany(e, t))
            }
          }, {
            key: "call",
            value: function(e) {
              var t, n = this.hooks[e];
              if (n) {
                var r = n.type,
                  i = this.callbacks;
                if (i) {
                  var o = i[e];
                  if (o) {
                    for (var a = o.tail, u = o.next, s = arguments.length, c = new Array(s > 1 ? s - 1 : 0), l = 1; l < s; l++) c[l - 1] = arguments[l];
                    for (var d, f = c; u !== a;) {
                      if (d = null === (t = u.callback) || void 0 === t ? void 0 : t.apply(u.context || this, f), r === A.WATERFALL) {
                        var h = [d];
                        f = h
                      }
                      u = u.next
                    }
                    return d
                  }
                }
              }
            }
          }, {
            key: "isExist",
            value: function(e) {
              var t;
              return Boolean(null === (t = this.callbacks) || void 0 === t ? void 0 : t[e])
            }
          }])
        }(T))({
          getMiniLifecycle: R(A.SINGLE, (function(e) {
            return e
          })),
          getMiniLifecycleImpl: R(A.SINGLE, (function() {
            return this.call("getMiniLifecycle", L)
          })),
          getLifecycle: R(A.SINGLE, (function(e, t) {
            return e[t]
          })),
          getPathIndex: R(A.SINGLE, (function(e) {
            return "[".concat(e, "]")
          })),
          getEventCenter: R(A.SINGLE, (function(e) {
            return new e
          })),
          isBubbleEvents: R(A.SINGLE, (function(e) {
            return new Set(["touchstart", "touchmove", "touchcancel", "touchend", "touchforcechange", "tap", "longpress", "longtap", "transitionend", "animationstart", "animationiteration", "animationend"]).has(e)
          })),
          getSpecialNodes: R(A.SINGLE, (function() {
            return ["view", "text", "image"]
          })),
          onRemoveAttribute: R(A.SINGLE),
          batchedEventUpdates: R(A.SINGLE),
          mergePageInstance: R(A.SINGLE),
          modifyPageObject: R(A.SINGLE),
          createPullDownComponent: R(A.SINGLE),
          getDOMNode: R(A.SINGLE),
          modifyHydrateData: R(A.SINGLE),
          modifySetAttrPayload: R(A.SINGLE),
          modifyRmAttrPayload: R(A.SINGLE),
          onAddEvent: R(A.SINGLE),
          proxyToRaw: R(A.SINGLE, (function(e) {
            return e
          })),
          modifyMpEvent: R(A.MULTI),
          modifyMpEventImpl: R(A.SINGLE, (function(e) {
            try {
              this.call("modifyMpEvent", e)
            } catch (e) {
              console.warn("[Taro modifyMpEvent hook Error]: " + (null == e ? void 0 : e.message))
            }
          })),
          injectNewStyleProperties: R(A.SINGLE),
          modifyTaroEvent: R(A.MULTI),
          dispatchTaroEvent: R(A.SINGLE, (function(e, t) {
            t.dispatchEvent(e)
          })),
          dispatchTaroEventFinish: R(A.MULTI),
          modifyDispatchEvent: R(A.MULTI),
          initNativeApi: R(A.MULTI),
          patchElement: R(A.MULTI)
        }),
        D = {},
        B = [],
        M = function() {};

      function j(e) {
        return e.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase()
      }

      function U(e) {
        for (var t = "", n = !1, r = 0; r < e.length; r++) "-" !== e[r] ? (t += n ? e[r].toUpperCase() : e[r], n = !1) : n = !0;
        return t
      }

      function F(e) {
        return e.charAt(0).toUpperCase() + e.slice(1)
      }

      function H(e, t) {
        if (!e) throw new Error(t)
      }

      function W(e, t) {}
      Object.prototype.hasOwnProperty;
      var V = 1,
        z = (new Date).getTime().toString();

      function G(e) {
        return Object.keys(e).forEach((function(t) {
          t in k ? Object.assign(k[t], e[t]) : k[t] = e[t]
        })), k
      }

      function q(e) {
        var t = {},
          n = e.View,
          r = {
            "#text": {},
            StaticView: n,
            StaticImage: e.Image,
            StaticText: e.Text,
            PureView: n,
            CatchView: n
          };
        return e = Object.assign(Object.assign({}, e), r), Object.keys(e).sort((function(e, t) {
          var n = /^(Static|Pure|Catch)*(View|Image|Text)$/,
            r = n.test(e),
            i = n.test(t);
          return r && i ? e > t ? 1 : -1 : r ? -1 : i || e >= t ? 1 : -1
        })).forEach((function(n, r) {
          var i = {
            _num: String(r)
          };
          Object.keys(e[n]).filter((function(e) {
            return !/^bind/.test(e) && !["focus", "blur"].includes(e)
          })).sort().forEach((function(e, t) {
            i[U(e)] = "p" + t
          })), t[j(n)] = i
        })), t
      }

      function $(e, t) {
        var n = t || Z;
        Object.keys(e).forEach((function(t) {
          n.tap(t, e[t])
        }))
      }

      function K(e) {
        return function() {
          console.warn("小程序暂不支持 ".concat(e))
        }
      }

      function J(e, t) {
        if (["navigateTo", "redirectTo", "reLaunch", "switchTab"].indexOf(e) > -1) {
          var n = (t.url = t.url || "").indexOf("?") > -1,
            r = z + V++;
          t.url += (n ? "&" : "?") + "".concat("__key_", "=").concat(r)
        }
      }
      var Y = new Set(["addPhoneContact", "authorize", "canvasGetImageData", "canvasPutImageData", "canvasToTempFilePath", "checkSession", "chooseAddress", "chooseImage", "chooseInvoiceTitle", "chooseLocation", "chooseVideo", "clearStorage", "closeBLEConnection", "closeBluetoothAdapter", "closeSocket", "compressImage", "connectSocket", "createBLEConnection", "downloadFile", "exitMiniProgram", "getAvailableAudioSources", "getBLEDeviceCharacteristics", "getBLEDeviceServices", "getBatteryInfo", "getBeacons", "getBluetoothAdapterState", "getBluetoothDevices", "getClipboardData", "getConnectedBluetoothDevices", "getConnectedWifi", "getExtConfig", "getFileInfo", "getImageInfo", "getLocation", "getNetworkType", "getSavedFileInfo", "getSavedFileList", "getScreenBrightness", "getSetting", "getStorage", "getStorageInfo", "getSystemInfo", "getUserInfo", "getWifiList", "hideHomeButton", "hideShareMenu", "hideTabBar", "hideTabBarRedDot", "loadFontFace", "login", "makePhoneCall", "navigateBack", "navigateBackMiniProgram", "navigateTo", "navigateToBookshelf", "navigateToMiniProgram", "notifyBLECharacteristicValueChange", "hideKeyboard", "hideLoading", "hideNavigationBarLoading", "hideToast", "openBluetoothAdapter", "openDocument", "openLocation", "openSetting", "pageScrollTo", "previewImage", "queryBookshelf", "reLaunch", "readBLECharacteristicValue", "redirectTo", "removeSavedFile", "removeStorage", "removeTabBarBadge", "requestSubscribeMessage", "saveFile", "saveImageToPhotosAlbum", "saveVideoToPhotosAlbum", "scanCode", "sendSocketMessage", "setBackgroundColor", "setBackgroundTextStyle", "setClipboardData", "setEnableDebug", "setInnerAudioOption", "setKeepScreenOn", "setNavigationBarColor", "setNavigationBarTitle", "setScreenBrightness", "setStorage", "setTabBarBadge", "setTabBarItem", "setTabBarStyle", "showActionSheet", "showFavoriteGuide", "showLoading", "showModal", "showShareMenu", "showTabBar", "showTabBarRedDot", "showToast", "startBeaconDiscovery", "startBluetoothDevicesDiscovery", "startDeviceMotionListening", "startPullDownRefresh", "stopBeaconDiscovery", "stopBluetoothDevicesDiscovery", "stopCompass", "startCompass", "startAccelerometer", "stopAccelerometer", "showNavigationBarLoading", "stopDeviceMotionListening", "stopPullDownRefresh", "switchTab", "uploadFile", "vibrateLong", "vibrateShort", "writeBLECharacteristicValue"]);

      function Q(e) {
        return function() {
          var t, n = null === (t = e.getSystemInfoSync) || void 0 === t ? void 0 : t.call(e);
          if (!n) return !1;
          var r = n.platform.toLowerCase();
          return "android" === r || "devtools" === r
        }
      }

      function X(e) {
        return function(t) {
          var n, r = (t = t ? C(t) ? {
              url: t
            } : t : {}).success,
            i = t.fail,
            o = t.complete,
            a = new Promise((function(a, u) {
              t.success = function(e) {
                r && r(e), a(e)
              }, t.fail = function(e) {
                i && i(e), u(e)
              }, t.complete = function(e) {
                o && o(e)
              }, n = e.request(t)
            }));
          return ne(n, a), a.abort = function(e) {
            return e && e(), n && n.abort(), a
          }, a
        }
      }

      function ee(e, t) {
        var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {},
          i = n.needPromiseApis || [],
          o = new Set([].concat((0, r.Z)(i), (0, r.Z)(Y))),
          a = ["getEnv", "interceptors", "Current", "getCurrentInstance", "options", "nextTick", "eventCenter", "Events", "preload", "webpackJsonp"],
          u = new Set(n.isOnlyPromisify ? i : Object.keys(t).filter((function(e) {
            return -1 === a.indexOf(e)
          })));
        n.modifyApis && n.modifyApis(u), u.forEach((function(r) {
          if (o.has(r)) {
            var i = r;
            e[i] = function() {
              for (var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, r = arguments.length, o = new Array(r > 1 ? r - 1 : 0), a = 1; a < r; a++) o[a - 1] = arguments[a];
              var u = i;
              if ("string" == typeof e) return o.length ? t[u].apply(t, [e].concat(o)) : t[u](e);
              if (n.transformMeta) {
                var s = n.transformMeta(u, e);
                if (u = s.key, e = s.options, !t.hasOwnProperty(u)) return K(u)()
              }
              var c = null,
                l = Object.assign({}, e);
              J(u, e);
              var d = new Promise((function(r, i) {
                l.success = function(t) {
                  var i, o;
                  null === (i = n.modifyAsyncResult) || void 0 === i || i.call(n, u, t), null === (o = e.success) || void 0 === o || o.call(e, t), r("connectSocket" === u ? Promise.resolve().then((function() {
                    return c ? Object.assign(c, t) : t
                  })) : t)
                }, l.fail = function(t) {
                  var n;
                  null === (n = e.fail) || void 0 === n || n.call(e, t), i(t)
                }, l.complete = function(t) {
                  var n;
                  null === (n = e.complete) || void 0 === n || n.call(e, t)
                }, c = o.length ? t[u].apply(t, [l].concat(o)) : t[u](l)
              }));
              return ["uploadFile", "downloadFile"].includes(u) && (ne(c, d), d.progress = function(e) {
                return null == c || c.onProgressUpdate(e), d
              }, d.abort = function(e) {
                return null == e || e(), null == c || c.abort(), d
              }), d
            }
          } else {
            var a = r;
            if (n.transformMeta && (a = n.transformMeta(r, {}).key), !t.hasOwnProperty(a)) return void(e[r] = K(r));
            I(t[r]) ? e[r] = function() {
              for (var e = arguments.length, i = new Array(e), o = 0; o < e; o++) i[o] = arguments[o];
              return n.handleSyncApis ? n.handleSyncApis(r, t, i) : t[a].apply(t, i)
            } : e[r] = t[a]
          }
        })), !n.isOnlyPromisify && te(e, t, n)
      }

      function te(e, t) {
        var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
        e.canIUseWebp = Q(e), e.getCurrentPages = getCurrentPages || K("getCurrentPages"), e.getApp = getApp || K("getApp"), e.env = t.env || {};
        try {
          e.requirePlugin = requirePlugin || K("requirePlugin")
        } catch (t) {
          e.requirePlugin = K("requirePlugin")
        }
        var r = n.request || X(t);

        function i(e) {
          return r(e.requestParams)
        }
        var o = new e.Link(i);
        e.request = o.request.bind(o), e.addInterceptor = o.addInterceptor.bind(o), e.cleanInterceptors = o.cleanInterceptors.bind(o), e.miniGlobal = e.options.miniGlobal = t, e.getAppInfo = function() {
          return {
            platform: "mini",
            taroVersion: "3.6.15",
            designWidth: e.config.designWidth
          }
        }, e.createSelectorQuery = re(e, t, "createSelectorQuery", "exec"), e.createIntersectionObserver = re(e, t, "createIntersectionObserver", "observe")
      }

      function ne(e, t) {
        e && t && e && ["abort", "onHeadersReceived", "offHeadersReceived", "onProgressUpdate", "offProgressUpdate", "onChunkReceived", "offChunkReceived"].forEach((function(n) {
          n in e && (t[n] = e[n].bind(e))
        }))
      }

      function re(e, t, n, r) {
        return function() {
          var i = t[n](),
            o = i[r].bind(i);
          return i[r] = function() {
            for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
            e.nextTick((function() {
              return o.apply(void 0, n)
            }))
          }, i
        }
      }
    },
    1678: function(e, t, n) {
      var r = n(4886).hooks,
        i = n(3174).Z;
      r.isExist("initNativeApi") && r.call("initNativeApi", i), e.exports = i, e.exports.default = e.exports
    },
    3174: function(e, t, n) {
      "use strict";
      n.d(t, {
        Z: function() {
          return w
        }
      });
      var r = n(6522),
        i = n(4886),
        o = n(3295),
        a = n(3028),
        u = n(9249),
        s = n(7371),
        c = n(6666);

      function l(e) {
        return "function" == typeof e
      }

      function d(e) {
        return void 0 === e
      }

      function f(e) {
        return e && "object" === (0, r.Z)(e)
      }
      var h = function(e) {
        return !f(e)
      };

      function p(e) {
        throw new TypeError(e)
      }
      l(Object.assign) || (Object.assign = function(e) {
        null == e && p("Cannot convert undefined or null to object");
        for (var t = Object(e), n = 1; n < arguments.length; n++) {
          var r = arguments[n];
          if (null != r)
            for (var i in r) Object.prototype.hasOwnProperty.call(r, i) && (t[i] = r[i])
        }
        return t
      }), l(Object.defineProperties) || (Object.defineProperties = function(e, t) {
        function n(e) {
          function t(e, t) {
            return Object.prototype.hasOwnProperty.call(e, t)
          }
          h(e) && p("bad desc");
          var n = {};
          if (t(e, "enumerable") && (n.enumerable = !!e.enumerable), t(e, "configurable") && (n.configurable = !!e.configurable), t(e, "value") && (n.value = e.value), t(e, "writable") && (n.writable = !!e.writable), t(e, "get")) {
            var r = e.get;
            l(r) || d(r) || p("bad get"), n.get = r
          }
          if (t(e, "set")) {
            var i = e.set;
            l(i) || d(i) || p("bad set"), n.set = i
          }
          return ("get" in n || "set" in n) && ("value" in n || "writable" in n) && p("identity-confused descriptor"), n
        }
        h(e) && p("bad obj"), t = Object(t);
        for (var r = Object.keys(t), i = [], o = 0; o < r.length; o++) i.push([r[o], n(t[r[o]])]);
        for (var a = 0; a < i.length; a++) Object.defineProperty(e, i[a][0], i[a][1]);
        return e
      });
      var v = {
        WEAPP: "WEAPP",
        SWAN: "SWAN",
        ALIPAY: "ALIPAY",
        TT: "TT",
        QQ: "QQ",
        JD: "JD",
        WEB: "WEB",
        RN: "RN",
        HARMONY: "HARMONY",
        QUICKAPP: "QUICKAPP"
      };
      (0, o.gl)();
      var m = function() {
          function e(t, n, r) {
            (0, u.Z)(this, e), this.index = r || 0, this.requestParams = t, this.interceptors = n || []
          }
          return (0, s.Z)(e, [{
            key: "proceed",
            value: function(e) {
              if (this.requestParams = e, this.index >= this.interceptors.length) throw new Error("chain 参数错误, 请勿直接修改 request.chain");
              var t = this._getNextInterceptor()(this._getNextChain()),
                n = t.catch((function(e) {
                  return Promise.reject(e)
                }));
              return Object.keys(t).forEach((function(e) {
                return l(t[e]) && (n[e] = t[e])
              })), n
            }
          }, {
            key: "_getNextInterceptor",
            value: function() {
              return this.interceptors[this.index]
            }
          }, {
            key: "_getNextChain",
            value: function() {
              return new e(this.requestParams, this.interceptors, this.index + 1)
            }
          }]), e
        }(),
        g = function() {
          function e(t) {
            (0, u.Z)(this, e), this.taroInterceptor = t, this.chain = new m
          }
          return (0, s.Z)(e, [{
            key: "request",
            value: function(e) {
              var t = this.chain,
                n = this.taroInterceptor;
              return t.interceptors = t.interceptors.filter((function(e) {
                return e !== n
              })).concat(n), t.proceed((0, a.Z)({}, e))
            }
          }, {
            key: "addInterceptor",
            value: function(e) {
              this.chain.interceptors.push(e)
            }
          }, {
            key: "cleanInterceptors",
            value: function() {
              this.chain = new m
            }
          }]), e
        }(),
        b = Object.freeze({
          __proto__: null,
          timeoutInterceptor: function(e) {
            var t, n = e.requestParams,
              r = new Promise((function(r, i) {
                var o = setTimeout((function() {
                  o = null, i(new Error("网络链接超时,请稍后再试！"))
                }), n && n.timeout || 6e4);
                (t = e.proceed(n)).then((function(e) {
                  o && (clearTimeout(o), r(e))
                })).catch((function(e) {
                  o && clearTimeout(o), i(e)
                }))
              }));
            return !d(t) && l(t.abort) && (r.abort = t.abort), r
          },
          logInterceptor: function(e) {
            var t = e.requestParams,
              n = t.method,
              r = t.data,
              i = t.url;
            console.log("http ".concat(n || "GET", " --\x3e ").concat(i, " data: "), r);
            var o = e.proceed(t),
              a = o.then((function(e) {
                return console.log("http <-- ".concat(i, " result:"), e), e
              }));
            return l(o.abort) && (a.abort = o.abort), a
          }
        }),
        y = {
          640: 1.17,
          750: 1,
          828: .905
        };

      function k(e) {
        return function(t) {
          var n = t.designWidth,
            r = void 0 === n ? 750 : n,
            i = t.deviceRatio,
            o = void 0 === i ? y : i,
            a = t.baseFontSize,
            u = void 0 === a ? 20 : a,
            s = t.targetUnit,
            c = void 0 === s ? "rpx" : s,
            l = t.unitPrecision,
            d = void 0 === l ? 5 : l;
          e.config = e.config || {}, e.config.designWidth = r, e.config.deviceRatio = o, e.config.baseFontSize = u, e.config.targetUnit = c, e.config.unitPrecision = d
        }
      }
      var w = {
        Behavior: function(e) {
          return e
        },
        getEnv: function() {
          return v.WEAPP
        },
        ENV_TYPE: v,
        Link: g,
        interceptors: b,
        Current: i.Current,
        getCurrentInstance: i.getCurrentInstance,
        options: i.options,
        nextTick: i.nextTick,
        eventCenter: i.eventCenter,
        Events: i.Events,
        getInitPxTransform: k,
        interceptorify: function(e) {
          return new g((function(t) {
            return e(t.requestParams)
          }))
        }
      };
      w.initPxTransform = k(w), w.preload = function(e) {
        return function(t, n) {
          e.preloadData = f(t) ? t : (0, c.Z)({}, t, n)
        }
      }(i.Current), w.pxTransform = function(e) {
        return function(t) {
          var n = e.config || {},
            r = n.deviceRatio || y,
            i = n.baseFontSize,
            o = function() {
              var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0;
              return l(n.designWidth) ? n.designWidth(e) : n.designWidth || 750
            }(t);
          if (!(o in r)) throw new Error("deviceRatio 配置中不存在 ".concat(o, " 的设置！"));
          var a = ~~t,
            u = 1 / r[o];
          switch (n.targetUnit) {
            case "rem":
              u *= 2 * i;
              break;
            case "px":
              u *= 2
          }
          var s = a / u;
          return n.unitPrecision >= 0 && n.unitPrecision <= 100 && (s = Number(s.toFixed(n.unitPrecision))), s + n.targetUnit
        }
      }(w)
    }
  }
]);