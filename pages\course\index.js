! function() {
  "use strict";
  (wx.webpackJsonp = wx.webpackJsonp || []).push([
    [4240], {
      9755: function(n, e, c) {
        var a = c(4886),
          t = c(6234),
          o = c(3675),
          i = c(1678),
          r = c.n(i),
          s = c(2017),
          l = c(2784),
          g = c(4488),
          u = c(5185),
          d = c(7048),
          p = c(2322),
          f = function() {
            var n = (0, u.I0)(),
              e = (0, l.useState)(!1),
              c = (0, t.Z)(e, 2),
              a = c[0],
              i = c[1],
              f = r().getStorageSync("gradeId"),
              _ = (0, g.Z)(),
              m = _.url,
              h = _.getUrl;
            r().useTabItemTap((function(n) {
              (0, s.Z)({
                click_id: "click_78_08_01_01",
                userId: r().getStorageSync("stuId")
              }), console.log("course-tabItemTap", n)
            })), r().useDidShow((function() {
              n((0, d.c6)()).then((function(n) {
                if (1009 !== n.code) {
                  if (i(!0), 0 === n.code) {
                    var e = n.data,
                      c = e.realname,
                      a = e.nickname,
                      t = e.user_id,
                      o = r().getStorageSync("Authorization"),
                      s = "https://app.bcc.xiwang.com/learn/#/?grade=".concat(f, "&stuId=").concat(t, "&userName=").concat(c || a, "&token=").concat(o);
                    h(s)
                  }
                } else i(!1)
              })).catch((function(n) {
                i(!1), (0, s.Z)({
                  click_id: "catch_getUserInfo_error",
                  err: n
                })
              }))
            }));
            var k = function() {
              r().navigateTo({
                url: "/pages/login/index?backPage=".concat(encodeURIComponent("/pages/course/index"), "&backPageType=switchTab")
              })
            };
            return a ? m && (0, p.jsx)(o.kh, {
              src: m
            }) : (0, p.jsxs)(o.G7, {
              className: "login-middle-page",
              children: [(0, p.jsx)(o.G7, {
                className: "not_login_data",
                children: (0, p.jsxs)(o.G7, {
                  className: "login_opt",
                  children: [(0, p.jsx)(o.G7, {
                    className: "login_btn",
                    onClick: k,
                    children: "开始学习"
                  }), (0, p.jsx)(o.G7, {
                    className: "login_btn_introduction",
                    children: "登录后可体验完整课程、学习报告等更多功能哦~"
                  })]
                })
              }), (0, p.jsx)(o.G7, {
                className: "not_login_mask",
                onClick: k
              })]
            })
          };
        Page((0, a.createPageConfig)(f, "pages/course/index", {
          root: {
            cn: []
          }
        }, {
          navigationBarTitleText: "希望学·看课",
          backgroundColorTop: "#fff",
          pageOrientation: "portrait"
        } || {}))
      }
    },
    function(n) {
      n.O(0, [2107, 1216, 8592], (function() {
        return function(e) {
          return n(n.s = e)
        }(9755)
      })), n.O()
    }
  ])
}();