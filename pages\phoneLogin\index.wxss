.p-login {
    background: url(https://m.xiwang.com/resource/ksJZ_n2iz56eTI_l4wpjk-1732689103959.png) no-repeat;
    background-color: #f7f7f8;
    background-size: cover;
    bottom: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 100vh;
    left: 0;
    overflow: auto;
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    z-index: 10
}

.p-login .title {
    color: #333;
    font-family: PingFangSC-Medium,PingFang SC;
    font-size: 40rpx;
    font-weight: 500;
    margin: 160rpx auto 16rpx;
    width: 670rpx
}

.p-login .login-tips {
    color: #666;
    font-family: PingFang SC;
    font-size: 28rpx;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin: 0 auto 48rpx;
    width: 670rpx
}

.p-login .login-container-bg-code {
    background-image: url(https://m.xiwang.com/resource/G8DASWyk8BIthOYAIAndF-1732689016526.png)
}

.p-login .login-container-bg-pwd {
    background-image: url(https://m.xiwang.com/resource/QhK7wTsUsOmtNgDccKo8d-1732689078234.png)
}

.p-login .login-container {
    -ms-flex-pack: start;
    background-repeat: no-repeat;
    background-size: 670rpx 636rpx;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    height: 636rpx;
    -webkit-justify-content: flex-start;
    justify-content: flex-start;
    margin: 0 auto;
    padding-top: 120rpx;
    position: relative;
    width: 670rpx
}

.p-login .login-container,.p-login .login-container .login-type-con {
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}

.p-login .login-container .login-type-con {
    -ms-flex-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    position: absolute;
    top: 16rpx;
    width: 100%
}

.p-login .login-container .login-type-con .login-type {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    -webkit-align-items: center;
    align-items: center;
    color: #666;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    font-family: PingFang SC;
    font-size: 30rpx;
    -webkit-justify-content: center;
    justify-content: center;
    padding: 20rpx 0rpx;
    text-align: center;
    width: 50%
}

.p-login .login-container .login-type-con .login-type-active {
    color: #333;
    font-weight: 500
}

.p-login .login-container .login-input .nut-input-native {
    font-size: 28rpx;
    font-weight: 600
}

.p-login .login-container .login-input .nut-input-native .input-placeholder {
    font-size: 28rpx;
    font-weight: 400
}

.p-login .login-container .login-input .tel-input {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #333;
    font-family: PingFangSC-Regular,PingFang SC;
    font-weight: 400;
    padding: 0 44rpx
}

.p-login .login-container .login-input .phone-input,.p-login .login-container .login-input .tel-input {
    background: #f5f5f6;
    border-radius: 44rpx;
    height: 88rpx;
    line-height: 88rpx;
    margin-bottom: 32rpx;
    width: 606rpx
}

.p-login .login-container .login-input .phone-input {
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}

.p-login .login-container .login-input .phone-input .tel-input {
    padding-left: 10rpx
}

.p-login .login-container .login-input .phone-input .tel-input-pwd {
    padding-left: 44rpx
}

.p-login .login-container .login-input .phone-input .phone-code {
    color: #333;
    font-family: PingFangSC-Regular,PingFang SC;
    font-weight: 400;
    padding-left: 44rpx;
    position: relative;
    width: 160rpx
}

.p-login .login-container .login-input .phone-input .phone-code .phone-code-box {
    -ms-flex-pack: distribute;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-around;
    justify-content: space-around
}

.p-login .login-container .login-input .phone-input .phone-code .phone-code-box .updown-icon {
    height: 20rpx;
    width: 36rpx
}

.p-login .login-container .login-input .phone-input .phone-code .triangle {
    border-color: transparent transparent currentcolor;
    border-style: solid;
    border-width: 0 16rpx 16rpx;
    color: #fff;
    left: 66rpx;
    position: absolute;
    top: 84rpx;
    -webkit-transform: translate(-50%,-100%);
    -ms-transform: translate(-50%,-100%);
    transform: translate(-50%,-100%);
    z-index: 10
}

.p-login .login-container .login-input .phone-input .phone-code .phone-code-popup {
    -ms-flex-pack: center;
    background: #fff;
    border-radius: 10rpx;
    -webkit-box-shadow: 0 .05333rem .32rem rgba(50,50,51,.12);
    box-shadow: 0 .05333rem .32rem rgba(50,50,51,.12);
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    height: 180rpx;
    -webkit-justify-content: center;
    justify-content: center;
    left: 0;
    position: absolute;
    width: 376rpx;
    z-index: 10
}

.p-login .login-container .login-input .phone-input .phone-code .phone-code-popup .solid {
    border-top: 2rpx solid #f7f5f5;
    height: 2rpx;
    position: absolute;
    top: 66rpx;
    width: 200rpx
}

.p-login .login-container .login-input .phone-input .phone-code .phone-code-popup .phone-code-popup-span {
    font-size: 28rpx;
    height: 76rpx;
    line-height: 76rpx;
    padding-left: 32rpx;
    width: 340rpx
}

.p-login .login-container .login-input .phone-input .phone-code .phone-code-popup .phone-code-active {
    background: -webkit-gradient(linear,left top,right top,from(#fff),color-stop(105.88%,#fff3f2));
    background: -webkit-linear-gradient(left,#fff,#fff3f2 105.88%);
    background: linear-gradient(90deg,#fff,#fff3f2 105.88%);
    position: relative
}

.p-login .login-container .login-input .code-wrapper {
    -ms-flex-pack: justify;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    background: #f5f5f6;
    border-radius: 44rpx;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #666;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    font-family: PingFangSC-Regular,PingFang SC;
    font-weight: 400;
    height: 88rpx;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    line-height: 88rpx;
    padding: 0 44rpx;
    width: 606rpx
}

.p-login .login-container .login-input .code-wrapper .no-arrow {
    background: #f5f5f6;
    min-width: 200rpx;
    padding-left: 0rpx;
    padding-right: 0rpx
}

.p-login .login-container .login-input .code-wrapper .code-btn {
    -ms-flex-negative: 0;
    color: #999;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    text-align: right
}

.p-login .login-container .login-input .code-wrapper .code-pending {
    color: #999
}

.p-login .login-container .login-input .code-wrapper .code-ok {
    color: #fa3b2d
}

.p-login .login-container .login-input .code-wrapper .code-not-ok {
    color: rgba(250,59,45,.5)
}

.p-login .login-container .login-input .pwd-wrapper,.p-login .login-container .login-input .pwd-wrapper .eye {
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}

.p-login .login-container .login-input .pwd-wrapper .eye {
    -webkit-flex: none;
    -ms-flex: none;
    flex: none;
    margin-left: 16rpx;
    padding: 8rpx
}

.p-login .login-container .login-input .pwd-wrapper .eye .icon-img {
    height: 40rpx;
    width: 40rpx
}

.p-login .login-container .submit-btn {
    background: #ffafa9;
    border-radius: 44rpx;
    color: #fff;
    font-family: PingFangSC-Regular,PingFang SC;
    font-size: 30rpx;
    font-weight: 400;
    height: 88rpx;
    line-height: 88rpx;
    margin-top: 48rpx;
    text-align: center;
    width: 606rpx
}

.p-login .login-container .submit-ok {
    background: -webkit-gradient(linear,right top,left top,from(#ff3627),to(#ff664f));
    background: -webkit-linear-gradient(right,#ff3627,#ff664f);
    background: linear-gradient(270deg,#ff3627,#ff664f)
}

.p-login .login-container .login__protocol {
    margin-left: 40rpx;
    margin-top: 80rpx
}
