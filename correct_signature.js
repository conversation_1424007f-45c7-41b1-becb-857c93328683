const crypto = require('crypto');

// 模拟CryptoJS
const CryptoJS = {
    SHA1: function(data) {
        return {
            toString: function() {
                return crypto.createHash('sha1').update(data).digest('hex');
            }
        };
    },
    MD5: function(data) {
        return {
            toString: function() {
                return crypto.createHash('md5').update(data).digest('hex');
            }
        };
    }
};

// 模拟Base64
const Base64 = {
    encode: function(str) {
        return Buffer.from(str).toString('base64');
    }
};

// 修正后的签名生成对象
var signSynopsis = {
    createNonceStr: function() {
        return Math.random().toString(36).substr(2, 15);
    },

    createTimestamp: function() {
        // 修正：返回毫秒时间戳
        return parseInt((new Date).getTime());
    },

    paramsMerge: function(params) {
        params = params || {};
        var keys = Object.keys(params).sort();
        var filteredParams = {};
        
        keys.forEach(function(key) {
            if ("file_path" !== key) {
                filteredParams[key] = params[key];
            }
        });
        
        var paramStr = "";
        for (var key in filteredParams) {
            paramStr += key + "=" + filteredParams[key];
        }
        return paramStr;
    },

    sign: function(params) {
        var result = {
            timestamp: this.createTimestamp()
        };
        
        // 修正：使用 mini_1.19: 作为前缀
        var signKey = "mini_1.19:" + result.timestamp;
        var mergedParams = this.paramsMerge(params);
        
        // 尝试SHA1哈希
        var sha1Hash = CryptoJS.SHA1(signKey + mergedParams).toString();
        
        // 修正：使用 mini_1.19: 作为签名前缀
        var signature = "mini_1.19:" + sha1Hash;
        result.signature = Base64.encode(signature);
        
        return result;
    }
};

// 测试已知数据
console.log("=== 验证已知数据 ===");
const testParams = {
    symbol: "H7Vq89sLHpKJBcyPALZ40g1755837626550",
    password: "OyIeEKnApDquWth4uY8RjA1755837626550"
};

const knownTimestamp = 1755837626550;
const expectedSignature = "bWluaV8xLjE5OjM5ZmIwYjg2N2U1N2U2MzVlOGExMzQ0MzY0ZjlhMmZjZDQzZWFmOTU=";
const expectedHash = "39fb0b867e57e635e8a1344364f9a2fcd43eaf95";

// 手动构建签名过程
var signKey = "mini_1.19:" + knownTimestamp;
var mergedParams = signSynopsis.paramsMerge(testParams);
var fullString = signKey + mergedParams;

console.log("1. 时间戳:", knownTimestamp);
console.log("2. 签名前缀:", signKey);
console.log("3. 合并参数:", mergedParams);
console.log("4. 完整字符串:", fullString);

var sha1Hash = CryptoJS.SHA1(fullString).toString();
var md5Hash = CryptoJS.MD5(fullString).toString();

console.log("5. SHA1哈希:", sha1Hash);
console.log("6. MD5哈希:", md5Hash);
console.log("7. 预期哈希:", expectedHash);

console.log("\n=== 哈希匹配检查 ===");
console.log("SHA1匹配:", sha1Hash === expectedHash);
console.log("MD5匹配:", md5Hash === expectedHash);

// 如果都不匹配，尝试其他可能的组合
if (sha1Hash !== expectedHash && md5Hash !== expectedHash) {
    console.log("\n=== 尝试其他组合 ===");

    // 原始请求体格式
    var originalBody = "symbol=H7Vq89sLHpKJBcyPALZ40g1755837626550&password=OyIeEKnApDquWth4uY8RjA1755837626550";

    // 尝试不同的前缀和参数组合
    var prefixes = [
        "mini_1.19" + knownTimestamp,
        "mini_1.19:" + knownTimestamp,
        knownTimestamp.toString(),
        "mini_1.19",
        ""
    ];

    var paramFormats = [
        mergedParams,  // 当前格式
        originalBody,  // 原始请求体格式
        "symbol=" + testParams.symbol + "&password=" + testParams.password,
        testParams.symbol + testParams.password,  // 只有值
        testParams.password + testParams.symbol   // 交换顺序
    ];

    console.log("原始请求体:", originalBody);
    console.log("当前参数格式:", mergedParams);

    for (var i = 0; i < prefixes.length; i++) {
        for (var j = 0; j < paramFormats.length; j++) {
            var testString = prefixes[i] + paramFormats[j];
            var testSHA1 = CryptoJS.SHA1(testString).toString();
            var testMD5 = CryptoJS.MD5(testString).toString();

            if (testSHA1 === expectedHash || testMD5 === expectedHash) {
                console.log(`\n*** 找到匹配！***`);
                console.log(`前缀: "${prefixes[i]}"`);
                console.log(`参数: "${paramFormats[j]}"`);
                console.log(`完整字符串: "${testString}"`);
                console.log(`哈希算法: ${testSHA1 === expectedHash ? 'SHA1' : 'MD5'}`);
                console.log(`哈希值: ${testSHA1 === expectedHash ? testSHA1 : testMD5}`);
                break;
            }
        }
    }
}

// 生成新的签名示例
console.log("\n=== 生成新签名示例 ===");
var newResult = signSynopsis.sign(testParams);
console.log("新时间戳:", newResult.timestamp);
console.log("新签名:", newResult.signature);
