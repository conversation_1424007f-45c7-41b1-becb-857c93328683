const crypto = require('crypto');

// 模拟CryptoJS
const CryptoJS = {
    SHA1: function(data) {
        return {
            toString: function() {
                return crypto.createHash('sha1').update(data).digest('hex');
            }
        };
    },
    MD5: function(data) {
        return {
            toString: function() {
                return crypto.createHash('md5').update(data).digest('hex');
            }
        };
    }
};

// 模拟Base64
const Base64 = {
    encode: function(str) {
        return Buffer.from(str).toString('base64');
    }
};

// 修正后的签名生成对象
var signSynopsis = {
    createNonceStr: function() {
        return Math.random().toString(36).substr(2, 15);
    },

    createTimestamp: function() {
        // 修正：返回毫秒时间戳
        return parseInt((new Date).getTime());
    },

    paramsMerge: function(params) {
        params = params || {};
        var keys = Object.keys(params).sort();
        var filteredParams = {};
        
        keys.forEach(function(key) {
            if ("file_path" !== key) {
                filteredParams[key] = params[key];
            }
        });
        
        var paramStr = "";
        for (var key in filteredParams) {
            paramStr += key + "=" + filteredParams[key];
        }
        return paramStr;
    },

    sign: function(params) {
        var result = {
            timestamp: this.createTimestamp()
        };
        
        // 修正：使用 mini_1.19: 作为前缀
        var signKey = "mini_1.19:" + result.timestamp;
        var mergedParams = this.paramsMerge(params);
        
        // 尝试SHA1哈希
        var sha1Hash = CryptoJS.SHA1(signKey + mergedParams).toString();
        
        // 修正：使用 mini_1.19: 作为签名前缀
        var signature = "mini_1.19:" + sha1Hash;
        result.signature = Base64.encode(signature);
        
        return result;
    }
};

// 测试已知数据
console.log("=== 验证已知数据 ===");
const testParams = {
    symbol: "H7Vq89sLHpKJBcyPALZ40g1755837626550",
    password: "OyIeEKnApDquWth4uY8RjA1755837626550"
};

const knownTimestamp = 1755837626550;
const expectedSignature = "bWluaV8xLjE5OjM5ZmIwYjg2N2U1N2U2MzVlOGExMzQ0MzY0ZjlhMmZjZDQzZWFmOTU=";
const expectedHash = "39fb0b867e57e635e8a1344364f9a2fcd43eaf95";

// 手动构建签名过程
var signKey = "mini_1.19:" + knownTimestamp;
var mergedParams = signSynopsis.paramsMerge(testParams);
var fullString = signKey + mergedParams;

console.log("1. 时间戳:", knownTimestamp);
console.log("2. 签名前缀:", signKey);
console.log("3. 合并参数:", mergedParams);
console.log("4. 完整字符串:", fullString);

var sha1Hash = CryptoJS.SHA1(fullString).toString();
var md5Hash = CryptoJS.MD5(fullString).toString();

console.log("5. SHA1哈希:", sha1Hash);
console.log("6. MD5哈希:", md5Hash);
console.log("7. 预期哈希:", expectedHash);

console.log("\n=== 哈希匹配检查 ===");
console.log("SHA1匹配:", sha1Hash === expectedHash);
console.log("MD5匹配:", md5Hash === expectedHash);

// 如果都不匹配，尝试其他可能的组合
if (sha1Hash !== expectedHash && md5Hash !== expectedHash) {
    console.log("\n=== 尝试其他组合 ===");
    
    // 尝试不同的前缀
    var alternatives = [
        "mini_1.19" + knownTimestamp,  // 没有冒号
        knownTimestamp.toString(),      // 只有时间戳
        "mini_1.19:" + knownTimestamp,  // 原来的
    ];
    
    for (var i = 0; i < alternatives.length; i++) {
        var testString = alternatives[i] + mergedParams;
        var testSHA1 = CryptoJS.SHA1(testString).toString();
        var testMD5 = CryptoJS.MD5(testString).toString();
        
        console.log(`组合${i+1}: ${testString}`);
        console.log(`SHA1: ${testSHA1} (匹配: ${testSHA1 === expectedHash})`);
        console.log(`MD5: ${testMD5} (匹配: ${testMD5 === expectedHash})`);
        console.log("---");
        
        if (testSHA1 === expectedHash || testMD5 === expectedHash) {
            console.log("找到匹配的组合！");
            break;
        }
    }
}

// 生成新的签名示例
console.log("\n=== 生成新签名示例 ===");
var newResult = signSynopsis.sign(testParams);
console.log("新时间戳:", newResult.timestamp);
console.log("新签名:", newResult.signature);
