(wx.webpackJsonp = wx.webpackJsonp || []).push([
  [8592], {
    586: function(t, e, n) {
      "use strict";
      var r = n(6234),
        o = n(2784),
        i = n(1678),
        a = n.n(i),
        c = n(3675),
        u = n(2524),
        s = n.n(u),
        l = n(3517),
        f = n(2322),
        p = (0, o.forwardRef)((function(t, e) {
          var n = t.className,
            i = t.changed,
            u = (0, o.useState)(!1),
            p = (0, r.Z)(u, 2),
            d = p[0],
            h = p[1],
            g = (0, o.useRef)(!1);
          (0, o.useImperativeHandle)(e, (function() {
            return {
              agreeProtocol: g.current
            }
          }));
          var v = function(t, e) {
            t.stopPropagation(), a().navigateTo({
              url: "".concat(l.V6, "?url=").concat(encodeURIComponent(e))
            })
          };
          return (0, f.jsxs)(c.G7, {
            className: s()("protocol", n),
            onClick: function() {
              return g.current = !g.current, h((function(t) {
                return !t
              })), void(i && i(g.current))
            },
            children: [(0, f.jsx)(c.G7, {
              className: "protocol__radio",
              children: (0, f.jsx)(c.Y8, {
                className: "protocol__radio-agree",
                checked: d
              })
            }), (0, f.jsxs)(c.G7, {
              className: "protocol__container",
              children: [(0, f.jsx)(c.xv, {
                children: "我已阅读并同意"
              }), (0, f.jsx)(c.xv, {
                className: "protocol__text",
                onClick: function(t) {
                  return v(t, "https://xue.xiwang.com/touch-protocol?app_blid=30#/register")
                },
                children: "《希望学用户协议》"
              }), (0, f.jsx)(c.xv, {
                className: "protocol__text",
                onClick: function(t) {
                  return v(t, "https://xue.xiwang.com/touch-protocol?app_blid=30#/privacy")
                },
                children: "《用户个人信息保护政策》"
              }), (0, f.jsx)(c.xv, {
                className: "protocol__text",
                onClick: function(t) {
                  return v(t, "https://xue.xiwang.com/touch-protocol?app_blid=30#/children")
                },
                children: "《儿童个人信息保护规则》"
              })]
            })]
          })
        }));
      e.Z = p
    },
    8371: function(t, e, n) {
      "use strict";
      n.d(e, {
        cV: function() {
          return o
        }
      });
      var r = n(2784).createContext({}),
        o = (r.Provider, r.Consumer, function(t) {
          return t.navigateBack = "navigateBack", t.reLaunch = "reLaunch", t.redirectTo = "redirectTo", t.navigateTo = "navigateTo", t.switchTab = "switchTab", t.navigateToMiniProgram = "navigateToMiniProgram", t
        }({}))
    },
    3517: function(t, e, n) {
      "use strict";
      n.d(e, {
        Jl: function() {
          return r
        },
        HU: function() {
          return o
        },
        V6: function() {
          return i
        },
        a2: function() {
          return a
        },
        fQ: function() {
          return c
        }
      });
      var r = "https://api.xue.xiwang.com",
        o = "https://i.bcc.xiwang.com",
        i = "/pages/webView/index",
        a = "/pages/phoneLogin/index",
        c = {
          10001: "参数传空了",
          10002: "网络问题，请求消息列表失败",
          10003: "网络问题，订阅请求发送失败",
          10004: "参数类型错误",
          10005: "无法展示 UI，一般是小程序这个时候退后台了导致的",
          20001: "没有模板数据，一般是模板 ID 不存在 或者和模板类型不对应 导致的",
          20002: "模板消息类型 既有一次性的又有永久的，不可混用",
          20003: "模板消息数量超过上限",
          20004: "请在设置中打开接受通知",
          20005: "小程序被禁封",
          20013: "不允许通过该接口订阅设备消息"
        }
    },
    3057: function(t, e, n) {
      "use strict";
      var r = n(1678),
        o = n.n(r),
        i = n(3494);
      e.Z = function(t, e) {
        var n = (0, r.useRouter)().params,
          a = e || t;
        return {
          checkParams: function() {
            var e = !(arguments.length > 0 && void 0 !== arguments[0]) || arguments[0];
            return t.forEach((function(t) {
              if (!n[t]) return o().showToast({
                title: "请传参数".concat(t, "给该页面"),
                duration: 2e3,
                icon: "error",
                mask: !0,
                complete: function() {
                  if (e) var t = setTimeout((function() {
                    o().navigateBack(), clearTimeout(t)
                  }), 1500)
                }
              }), !1
            })), !0
          },
          searchStr: (0, i.pE)(n, a),
          params: n
        }
      }
    },
    4488: function(t, e, n) {
      "use strict";
      var r = n(6234),
        o = n(1678),
        i = n.n(o),
        a = n(2784);
      e.Z = function() {
        var t = (0, a.useState)(""),
          e = (0, r.Z)(t, 2),
          n = e[0],
          o = e[1];
        return {
          url: n,
          getUrl: function(t) {
            var e = i().getStorageSync("Authorization"),
              n = "".concat("https://w.xiwang.com", "/commonapi/wechat/api/tal_token");
            o(e ? "".concat(n, "?tal_token=").concat(e, "&redirect=").concat(encodeURIComponent(t)) : "".concat(n, "?tal_token=").concat(e, "&clear=1&redirect=").concat(encodeURIComponent(t)))
          }
        }
      }
    },
    2017: function(t, e, n) {
      "use strict";
      var r = n(1678),
        o = n.n(r),
        i = n(2095);
      i.Z.init({
        logstore: "xw-fe-activity-h5",
        business_type: "school-kanke",
        env_type: "production"
      });
      var a = {};
      e.Z = function() {
        var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        i.Z.setUserInfo(o().getStorageSync("stuId") || "default");
        var e = {
          click_id: ""
        };
        Object.assign(e, a, t), console.log("埋点", e), i.Z.sendImmediate(e)
      }
    },
    2725: function(t, e, n) {
      "use strict";
      n.d(e, {
        xP: function() {
          return d
        },
        PT: function() {
          return h
        },
        TZ: function() {
          return g
        },
        Hg: function() {
          return v
        },
        x4: function() {
          return m
        },
        um: function() {
          return y
        },
        yl: function() {
          return b
        },
        rB: function() {
          return w
        },
        Td: function() {
          return S
        }
      });
      var r = n(2723),
        o = n(4795),
        i = n(1678),
        a = n.n(i),
        c = n(909),
        u = n.n(c),
        s = n(3517),
        l = n(3092),
        f = n(2017),
        p = 0,
        d = function() {
          u().config({
            entity: 2,
            env: "production",
            client_id: "514255",
            mini_version: "1.0.0",
            mini_appid: "wxd2606008715f7ba5",
            loginout_callback: function() {
              console.log("错误处理")
            },
            init_callback: {
              complete: function() {
                console.log("sdk初始化完成")
              },
              success: function() {
                console.log("sdk初始化成功")
              },
              fail: function() {
                console.log("sdk初始化失败")
              }
            }
          })
        },
        h = function(t) {
          return new Promise(function() {
            var e = (0, o.Z)((0, r.Z)().mark((function e(n, o) {
              var i, c, f, p, d, h;
              return (0, r.Z)().wrap((function(e) {
                for (;;) switch (e.prev = e.next) {
                  case 0:
                    return e.next = 2, l.Z.get("".concat(s.Jl, "/login/LoginV1/getToken"), {
                      code: t
                    });
                  case 2:
                    if (i = e.sent, c = i.stat, f = i.data, 1 !== c) {
                      e.next = 15;
                      break
                    }
                    return a().setStorageSync("oauth_open_id", f.oauth_open_id), a().setStorageSync("stuId", null == f || null === (p = f.user_info) || void 0 === p ? void 0 : p.user_id), a().setStorageSync("Authorization", null == f ? void 0 : f.tal_token), n({
                      stuId: null == f || null === (d = f.user_info) || void 0 === d ? void 0 : d.user_id
                    }), e.next = 11, u().writeBackTalToken(f.tal_token);
                  case 11:
                    h = e.sent, console.log("回写小程序登录态 writeTalToken :>> ", h), e.next = 16;
                    break;
                  case 15:
                    o({
                      stat: c,
                      msg: "鉴权失败"
                    });
                  case 16:
                  case "end":
                    return e.stop()
                }
              }), e)
            })));
            return function(t, n) {
              return e.apply(this, arguments)
            }
          }())
        },
        g = function() {
          return new Promise((function(t, e) {
            u().loginCodeByAuth({
              data: {
                bindType: 1
              },
              success: function(e) {
                0 == e.data.errcode && (p = (new Date).getTime(), ["phone", "userinfo"].includes(e.data.data.action) ? t({
                  phoneLoginStatus: !0,
                  data: e.data.data
                }) : "logined" === e.data.data.action && h(e.data.data.code).then((function(n) {
                  var r = n.stuId;
                  t({
                    stuId: void 0 === r ? "" : r,
                    data: e.data.data,
                    phoneLoginStatus: !1
                  })
                })))
              },
              fail: function(t) {
                e(t), console.log(t)
              }
            })
          }))
        },
        v = function(t) {
          return new Promise((function(e, n) {
            if ((new Date).getTime() - p > 3e5) return a().showModal({
              title: "授权失效",
              content: "请重新授权",
              showCancel: !1,
              success: function() {
                a().removeStorageSync("status"), a().removeStorageSync("Authorization"), a().navigateBack()
              }
            }), !1;
            t.detail.errMsg && "getPhoneNumber:ok" == t.detail.errMsg ? u().loginPhoneByAuth({
              data: {
                bindType: 1,
                iv: t.detail.iv,
                encryptedData: t.detail.encryptedData
              },
              success: function() {
                g().then((function(t) {
                  e(t)
                }))
              },
              fail: function(t) {
                console.log(t), n(t)
              },
              complete: function() {}
            }) : n({
              stat: 0,
              msg: "授权失败"
            })
          }))
        },
        m = function(t, e) {
          return console.log("talUserCenter", u()), new Promise((function(n, r) {
            u().loginSendSMSCode({
              data: {
                phone_code: e,
                phone: t,
                bindType: 1
              },
              success: function(e) {
                var r = e.data,
                  o = r.errcode,
                  i = r.errmsg;
                0 === o ? n(!0) : a().showToast({
                  title: i,
                  icon: "none",
                  duration: 2e3
                }), (0, f.Z)({
                  click_id: "loginSmS",
                  res: e,
                  tel: String(t).replace(/\s/g, "")
                })
              },
              fail: function(e) {
                (0, f.Z)({
                  click_id: "loginSmS",
                  res: e,
                  tel: String(t).replace(/\s/g, "")
                }), r(e)
              },
              complete: function(t) {}
            })
          }))
        },
        y = function(t, e, n) {
          return new Promise((function(i, c) {
            u().loginSms({
              data: {
                phone: t,
                phone_code: n,
                sms_code: e,
                bindType: 1
              },
              success: function() {
                var t = (0, o.Z)((0, r.Z)().mark((function t(e) {
                  var n, o, c, u, s, l, f;
                  return (0, r.Z)().wrap((function(t) {
                    for (;;) switch (t.prev = t.next) {
                      case 0:
                        if (n = e.data, o = n.errcode, c = n.data, u = n.errmsg, 0 !== o) {
                          t.next = 10;
                          break
                        }
                        return s = c.code, t.next = 5, h(s);
                      case 5:
                        l = t.sent, f = l.stuId, i({
                          stuId: f
                        }), t.next = 11;
                        break;
                      case 10:
                        a().showToast({
                          title: u,
                          icon: "none",
                          duration: 2e3
                        });
                      case 11:
                      case "end":
                        return t.stop()
                    }
                  }), t)
                })));
                return function(e) {
                  return t.apply(this, arguments)
                }
              }(),
              fail: function(t) {
                c(t)
              },
              complete: function(t) {}
            })
          }))
        },
        b = function(t, e) {
          return new Promise((function(n, i) {
            u().loginPassword({
              data: {
                symbol: t,
                password: e
              },
              success: function() {
                var t = (0, o.Z)((0, r.Z)().mark((function t(e) {
                  var o, i, c, u, s, l, f;
                  return (0, r.Z)().wrap((function(t) {
                    for (;;) switch (t.prev = t.next) {
                      case 0:
                        if (o = e.data, i = o.errcode, c = o.data, u = o.errmsg, console.log("loginPassword----", e), 0 !== i) {
                          t.next = 11;
                          break
                        }
                        return s = c.code, t.next = 6, h(s);
                      case 6:
                        l = t.sent, f = l.stuId, n({
                          stuId: f
                        }), t.next = 12;
                        break;
                      case 11:
                        a().showToast({
                          title: u,
                          icon: "none",
                          duration: 2e3
                        });
                      case 12:
                      case "end":
                        return t.stop()
                    }
                  }), t)
                })));
                return function(e) {
                  return t.apply(this, arguments)
                }
              }(),
              fail: function(t) {
                console.log(t), i(t)
              },
              complete: function(t) {}
            })
          }))
        },
        w = function(t) {
          return new Promise((function(e, n) {
            u().loginOut({
              data: {
                tal_token: t
              },
              success: function(t) {
                a().removeStorageSync("oauth_open_id"), a().removeStorageSync("stuId"), a().removeStorageSync("Authorization"), a().removeStorageSync("talUserInfo"), e(t.data)
              },
              fail: function(t) {
                console.log(t), n(t)
              }
            })
          }))
        },
        S = function(t, e) {
          return new Promise((function(n, r) {
            u().loginMove({
              data: {
                client_id: t,
                redirect: e
              },
              success: function(t) {
                console.log("[ res ]-333", t);
                var e = t.data;
                200 === t.statusCode && e.errcode, n(t.data)
              },
              fail: function(t) {
                console.log(t), r(t)
              },
              complete: function(t) {}
            })
          }))
        }
    },
    372: function(t, e, n) {
      "use strict";
      n.d(e, {
        ZS: function() {
          return i
        }
      });
      var r = n(1678),
        o = n.n(r),
        i = "/school",
        a = o().getAccountInfoSync().miniProgram.envVersion;
      e.ZP = function(t) {
        var e = "";
        return t.includes("//") || (e = "release" === a ? "https://w.xiwang.com" : "https://wgray.xue.xiwang.com"), e
      }
    },
    3092: function(t, e, n) {
      "use strict";
      n.d(e, {
        Z: function() {
          return y
        }
      });
      var r = n(9249),
        o = n(7371),
        i = n(1678),
        a = n.n(i),
        c = n(6224),
        u = 200,
        s = 401,
        l = 403,
        f = 404,
        p = 500,
        d = 502,
        h = 0,
        g = 0,
        v = [function(t) {
          h = Date.now();
          var e = t.requestParams;
          return t.proceed(e).then((function(t) {
            var n, r, o;
            return g = Date.now(), c.Z.api({
              api: null == e ? void 0 : e.url,
              success: !0,
              time: g - h,
              code: null == t || null === (n = t.data) || void 0 === n ? void 0 : n.code,
              msg: (null == t || null === (r = t.data) || void 0 === r ? void 0 : r.msg) || "",
              begin: h,
              traceId: null == t || null === (o = t.data) || void 0 === o ? void 0 : o.trace_id
            }), t.statusCode === f ? Promise.reject({
              desc: "请求资源不存在"
            }) : t.statusCode === d ? Promise.reject({
              desc: "服务端出现了问题"
            }) : t.statusCode === l ? Promise.reject({
              desc: "没有权限访问"
            }) : t.statusCode === s ? Promise.reject({
              desc: "需要鉴权"
            }) : t.statusCode === p ? Promise.reject({
              desc: "服务器错误"
            }) : t.statusCode === u ? t.data : void 0
          })).catch((function(t) {
            return g = Date.now(), c.Z.api({
              api: null == e ? void 0 : e.url,
              success: !1,
              time: g - h,
              msg: t || "",
              begin: h
            }), Promise.reject(t)
          }))
        }, a().interceptors.timeoutInterceptor],
        m = n(372),
        y = new((0, o.Z)((function t() {
          (0, r.Z)(this, t)
        }), [{
          key: "baseOptions",
          value: function(t) {
            var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "GET";
            a().cleanInterceptors(), v.forEach((function(t) {
              return a().addInterceptor(t)
            }));
            var n = t.url,
              r = t.data,
              o = (0, m.ZP)(n),
              i = "application/json;charset=UTF-8";
            i = t.contentType || i;
            var c = {
              url: "".concat(o).concat(n),
              data: r,
              method: e,
              timeout: 5e4,
              header: {
                "content-type": i,
                Authorization: a().getStorageSync("Authorization"),
                cookie: "tal_token=" + a().getStorageSync("Authorization")
              }
            };
            return a().request(c)
          }
        }, {
          key: "get",
          value: function(t) {
            var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
              n = {
                url: t,
                data: e
              };
            return this.baseOptions(n, "GET")
          }
        }, {
          key: "post",
          value: function(t, e, n) {
            var r = {
              url: t,
              data: e,
              contentType: n
            };
            return this.baseOptions(r, "POST")
          }
        }, {
          key: "put",
          value: function(t) {
            var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
              n = {
                url: t,
                data: e
              };
            return this.baseOptions(n, "PUT")
          }
        }, {
          key: "delete",
          value: function(t) {
            var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
              n = {
                url: t,
                data: e
              };
            return this.baseOptions(n, "DELETE")
          }
        }]))
    },
    8659: function(t, e, n) {
      "use strict";
      n.d(e, {
        p8: function() {
          return i
        },
        wr: function() {
          return a
        },
        zf: function() {
          return c
        },
        KQ: function() {
          return u
        },
        fA: function() {
          return s
        },
        ty: function() {
          return l
        },
        nA: function() {
          return f
        },
        gS: function() {
          return p
        },
        Y6: function() {
          return d
        },
        t4: function() {
          return h
        },
        SA: function() {
          return g
        },
        tK: function() {
          return v
        },
        Tz: function() {
          return m
        },
        Wc: function() {
          return y
        },
        k4: function() {
          return b
        },
        XW: function() {
          return w
        },
        AI: function() {
          return S
        },
        or: function() {
          return _
        },
        FD: function() {
          return x
        }
      });
      var r = n(3092),
        o = (n(3517), n(372)),
        i = function() {
          return r.Z.get(o.ZS + "/look_course/get_user_info")
        },
        a = function(t) {
          return r.Z.get(o.ZS + "/look_course/get_openid", t)
        },
        c = function() {
          return r.Z.get(o.ZS + "/look_course/get_course_info")
        },
        u = function(t) {
          return r.Z.get(o.ZS + "/look_course/get_wechat_qrcode", t)
        },
        s = function(t) {
          return r.Z.get(o.ZS + "/look_course/get_user_center_info", t)
        },
        l = function(t) {
          return r.Z.get(o.ZS + "/look_course/get_inschool_practice_info", t)
        },
        f = function() {
          return r.Z.get(o.ZS + "/front/card_activity/grade_list")
        },
        p = function(t) {
          return r.Z.post(o.ZS + "/look_course/update_user_info", t)
        },
        d = function() {
          return r.Z.get(o.ZS + "/look_course/get_address_file")
        },
        h = function() {
          return r.Z.get(o.ZS + "/mini_program/template_list")
        },
        g = function() {
          var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
          return r.Z.post(o.ZS + "/look_course/send_book_status", t)
        },
        v = function() {
          var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
          return r.Z.get("https://api.aidoutang.com/geminiapi/applet/teacher/qrcode", t)
        },
        m = function(t) {
          return r.Z.post("https://api.aidoutang.com/geminiapi/offers/wechat/pay", t)
        },
        y = function(t) {
          return r.Z.get("https://api.aidoutang.com/geminiapi/order/paid/status", t)
        },
        b = function(t) {
          return r.Z.post("https://wgray.xue.xiwang.com/school/front/uniapp/wechat/unbind", t)
        },
        w = function() {
          return r.Z.get("https://w.xiwang.com/activity/api/v2/group/info?s_group_id=740382669", {})
        },
        S = function(t) {
          return r.Z.post("https://api.aidoutang.com/geminiapi/order/4s/bind", t)
        },
        _ = function(t) {
          return r.Z.post("https://studentlive.bcc.xiwang.com/v1/student/linkMic/apply", t)
        },
        x = function(t) {
          return r.Z.post(o.ZS + "/front/school_mini/user/activitys", t)
        }
    },
    6093: function(t, e, n) {
      "use strict";
      n.d(e, {
        PT: function() {
          return r
        },
        dF: function() {
          return o
        },
        mu: function() {
          return i
        },
        Bm: function() {
          return a
        },
        hU: function() {
          return c
        },
        N2: function() {
          return u
        },
        ed: function() {
          return s
        },
        nX: function() {
          return l
        },
        DD: function() {
          return f
        },
        Hp: function() {
          return p
        },
        Tb: function() {
          return d
        },
        _8: function() {
          return h
        },
        Lo: function() {
          return g
        },
        Ng: function() {
          return v
        }
      });
      var r = "SET_QUESTION",
        o = "SET_INTERACTION_DATA",
        i = "SET_MODAL_STATUS",
        a = "SUBMIT_RESULT",
        c = "CLEAR_DATA",
        u = "NOTICE_RED_PACKAGE",
        s = "NOTICE_COURSEWARE",
        l = "SET_SHOPPING_BAGS",
        f = "SET_TIPS",
        p = "SET_FORCE_CLOSE_QUESTION",
        d = "SET_IS_ANSWER",
        h = "SET_HAVE_QUESTION",
        g = "SET_VOTE_DATA",
        v = "SET_REQUEST_COIN"
    },
    7931: function(t, e, n) {
      "use strict";
      n.d(e, {
        c: function() {
          return r
        }
      });
      var r = "SET_LOADING"
    },
    6318: function(t, e, n) {
      "use strict";
      n.d(e, {
        ad: function() {
          return r
        },
        XG: function() {
          return o
        },
        yS: function() {
          return i
        },
        zg: function() {
          return a
        },
        mE: function() {
          return c
        },
        s0: function() {
          return u
        }
      });
      var r = "SET_PLAYER_OPTION",
        o = "SET_PLAYER_STATUS",
        i = "CHANGE_PLAYER_TYPE",
        a = "SET_CONTROLLER_STATE",
        c = "SET_CURRENT_TIME",
        u = "SET_MUTED"
    },
    7581: function(t, e, n) {
      "use strict";
      n.d(e, {
        Z: function() {
          return W
        }
      });
      var r = n(8717),
        o = n(3292),
        i = n(3028),
        a = n(7931),
        c = {
          showLoading: !0
        },
        u = "DISABLE",
        s = "NOTICE_CHAT",
        l = "INIT_CHAT",
        f = "SEND_MSG_BY_IRC",
        p = "CLEAN_ALL_DATA_CHAT",
        d = "DESTROY_CHAT",
        h = "SEND_PEER_MESSAGE",
        g = "CHAT_MASK_SWITCH",
        v = "DISABLE_STUDENT_SPEAK",
        m = {
          isDisabled: !1,
          list: [],
          ircLastMessage: null,
          chatInstance: null,
          ownInformation: null,
          chatMaskSwitch: !1,
          disableStudentSpeak: !1
        },
        y = "INIT_DATA",
        b = "SET_INTERACTIONS_LIST",
        w = "INIT_MODULE",
        S = "CLEAN_ALL_DATA_BASE",
        _ = {
          initData: null,
          interactConfig: null,
          interactionsList: [],
          initModuleData: []
        },
        x = n(6093),
        T = {
          requestCoin: !0,
          ircRedPackageData: null,
          ircInteractionData: null,
          voteData: null,
          interactionData: null,
          questionDataSource: null,
          submitResult: null,
          tipsDataSource: null,
          forceCloseQuestion: !1,
          isAnswer: 0,
          modalStatus: {
            isShowAnswerSheet: !1,
            isShowFeedbackPopup: !1,
            isShoppingBags: !1,
            isShowShoppingState: !1,
            isShowOriginalVoiceAssess: !1,
            isShowSpeechAssessmentModal: !1,
            isShowMikeStatus: !0,
            isShowInteractionListDrawer: !1
          },
          isHaveQuestion: !1,
          shoppingBagsData: null
        },
        k = n(6318),
        E = {
          currentTime: 0,
          playerStatus: 0,
          playerType: null,
          controllerShow: !1,
          playerOption: {},
          muted: !1
        },
        $ = n(7048),
        I = "SET_UPDATED_TIME",
        A = {
          updatedTime: 0
        },
        Z = "SET_ANSWER_DATA",
        O = "SET_SHARE_SWITCH",
        P = {
          anserData: {},
          shareSwitch: !1
        },
        j = "SET_ROUTER_PARAMS",
        D = "SET_SUBJECT",
        C = "SET_SUBMIT_RESULT",
        L = "SET_FILL_BLANKS",
        N = "SET_TEMPORARY_STU_ANSWER",
        R = "CLEAR_INTERGATED_DATA",
        U = {
          routerParamsData: null,
          subjectDataSource: null,
          submitResult: [],
          temporaryStuAnswerData: null,
          fillBlanks: {
            fillBlanksStatus: !1,
            answer: ""
          }
        },
        M = "SET_LINK_MIC_INFO",
        H = "SET_LINK_MIC_STUDENT_LIST",
        B = "SET_HAND_NUM",
        q = {
          linkMicInfo: {},
          linkMicStudentList: null
        },
        F = (0, r.UY)({
          loadingStatus: function() {
            var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : c,
              e = arguments.length > 1 ? arguments[1] : void 0;
            switch (e.type) {
              case a.c:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  showLoading: e.payload
                });
              default:
                return t
            }
          },
          chat: function() {
            var t, e, n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : m,
              r = arguments.length > 1 ? arguments[1] : void 0;
            switch (r.type) {
              case l:
                return (0, i.Z)((0, i.Z)({}, n), {}, {
                  chatInstance: r.payload
                });
              case f:
                var o = r.payload,
                  a = o.ircRooms,
                  c = o.content;
                return null === (t = n.chatInstance) || void 0 === t || null === (t = t.RoomChatManager) || void 0 === t || t.sendRoomMessage(a, JSON.stringify(c), 99), (0, i.Z)((0, i.Z)({}, n), {}, {
                  ownInformation: c
                });
              case h:
                var y = r.payload;
                null === (e = n.chatInstance) || void 0 === e || null === (e = e.PeerChatManager) || void 0 === e || e.sendPeerMessage([{
                  nickname: y.nickname,
                  psid: y.psid
                }], JSON.stringify(y.content), 99);
              case s:
                return (0, i.Z)((0, i.Z)({}, n), {}, {
                  ircLastMessage: r.payload
                });
              case u:
                return (0, i.Z)((0, i.Z)({}, n), {}, {
                  isDisabled: r.payload
                });
              case d:
                return n.chatInstance && n.chatInstance.logout(), (0, i.Z)((0, i.Z)({}, n), {}, {
                  chatInstance: null
                });
              case p:
                return {
                  isDisabled: !1, list: [], ircLastMessage: null, chatInstance: null, ownInformation: null
                };
              case g:
                return (0, i.Z)((0, i.Z)({}, n), {}, {
                  chatMaskSwitch: r.payload
                });
              case v:
                return (0, i.Z)((0, i.Z)({}, n), {}, {
                  disableStudentSpeak: r.payload
                });
              default:
                return n
            }
          },
          base: function() {
            var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : _,
              e = arguments.length > 1 ? arguments[1] : void 0;
            switch (e.type) {
              case y:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  initData: e.payload
                });
              case "SET_INTERACT_CONFIG":
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  interactConfig: e.payload
                });
              case b:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  interactionsList: e.payload
                });
              case w:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  initModuleData: e.payload
                });
              case S:
                return {
                  initData: null, interactionsList: [], initModuleData: []
                };
              default:
                return t
            }
          },
          interactions: function() {
            var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : T,
              e = arguments.length > 1 ? arguments[1] : void 0;
            switch (e.type) {
              case x.N2:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  ircRedPackageData: e.payload
                });
              case x.ed:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  ircInteractionData: e.payload
                });
              case x.PT:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  questionDataSource: e.payload
                });
              case x.dF:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  interactionData: e.payload
                });
              case x.mu:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  modalStatus: (0, i.Z)((0, i.Z)({}, t.modalStatus), e.payload)
                });
              case x.Bm:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  submitResult: e.payload
                });
              case x.DD:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  tipsDataSource: e.payload
                });
              case x.Hp:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  forceCloseQuestion: e.payload
                });
              case x.hU:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  interactionData: null,
                  questionDataSource: null,
                  submitResult: null,
                  ircInteractionData: null,
                  modalStatus: {
                    isShowAnswerSheet: !1,
                    isShowFeedbackPopup: !1,
                    isShowOriginalVoiceAssess: !1,
                    isShowSpeechAssessmentModal: !1,
                    isShowMikeStatus: !0
                  }
                });
              case x.nX:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  shoppingBagsData: e.payload
                });
              case x.Tb:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  isAnswer: e.payload
                });
              case x._8:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  isHaveQuestion: e.payload
                });
              case x.Ng:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  requestCoin: e.payload
                });
              case x.Lo:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  voteData: e.payload
                });
              default:
                return t
            }
          },
          player: function() {
            var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : E,
              e = arguments.length > 1 ? arguments[1] : void 0;
            switch (e.type) {
              case k.mE:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  currentTime: e.payload
                });
              case k.XG:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  playerStatus: e.payload
                });
              case k.ad:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  playerOption: e.payload
                });
              case k.yS:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  playerType: e.payload
                });
              case k.zg:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  controllerShow: e.payload
                });
              case k.s0:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  muted: e.payload
                });
              default:
                return t
            }
          },
          user: $.ZP,
          shoppingBag: function() {
            var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : A,
              e = arguments.length > 1 ? arguments[1] : void 0;
            switch (e.type) {
              case I:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  updatedTime: e.payload
                });
              default:
                return t
            }
          },
          integratedskills: function() {
            var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : U,
              e = arguments.length > 1 ? arguments[1] : void 0;
            switch (e.type) {
              case j:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  routerParamsData: e.payload
                });
              case D:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  subjectDataSource: e.payload
                });
              case C:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  submitResult: e.payload
                });
              case L:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  fillBlanks: (0, i.Z)((0, i.Z)({}, U.fillBlanks), e.payload)
                });
              case N:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  temporaryStuAnswerData: e.payload
                });
              case R:
                return {
                  routerParamsData: null, subjectDataSource: null, submitResult: [], temporaryStuAnswerData: null, fillBlanks: {
                    fillBlanksStatus: !1,
                    answer: ""
                  }
                };
              default:
                return t
            }
          },
          preparation: function() {
            var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : P,
              e = arguments.length > 1 ? arguments[1] : void 0;
            switch (e.type) {
              case Z:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  anserData: e.payload
                });
              case O:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  shareSwitch: e.payload
                });
              default:
                return t
            }
          },
          linkMic: function() {
            var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : q,
              e = arguments.length > 1 ? arguments[1] : void 0;
            switch (e.type) {
              case M:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  linkMicInfo: e.payload
                });
              case H:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  linkMicStudentList: e.payload
                });
              case B:
                return (0, i.Z)((0, i.Z)({}, t), {}, {
                  linkMicInfo: (0, i.Z)((0, i.Z)({}, t.linkMicInfo), {}, {
                    handNum: e.payload
                  })
                });
              default:
                return t
            }
          }
        }),
        z = r.qC,
        G = [o.Z],
        J = z(r.md.apply(void 0, G)),
        W = (0, r.MT)(F, J)
    },
    7048: function(t, e, n) {
      "use strict";
      n.d(e, {
        ZP: function() {
          return l
        },
        c6: function() {
          return f
        }
      });
      var r = n(2723),
        o = n(4795),
        i = n(3028),
        a = n(8659),
        c = {
          userInfo: {}
        },
        u = "userInfoUpdate",
        s = function(t) {
          return {
            type: u,
            payload: t
          }
        };

      function l() {
        var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : c,
          e = arguments.length > 1 ? arguments[1] : void 0;
        switch (e.type) {
          case u:
            return (0, i.Z)((0, i.Z)({}, t), {}, {
              userInfo: e.payload
            });
          default:
            return t
        }
      }

      function f() {
        return function() {
          var t = (0, o.Z)((0, r.Z)().mark((function t(e) {
            var n;
            return (0, r.Z)().wrap((function(t) {
              for (;;) switch (t.prev = t.next) {
                case 0:
                  return t.next = 2, (0, a.p8)();
                case 2:
                  return 0 === (n = t.sent).code && e(s(n.data)), t.abrupt("return", n);
                case 5:
                case "end":
                  return t.stop()
              }
            }), t)
          })));
          return function(e) {
            return t.apply(this, arguments)
          }
        }()
      }
    },
    7298: function(t, e, n) {
      "use strict";
      n.d(e, {
        jW: function() {
          return l
        },
        Hj: function() {
          return f
        },
        ZZ: function() {
          return p
        }
      });
      var r = n(3028),
        o = n(1678),
        i = n.n(o),
        a = n(7491),
        c = n.n(a),
        u = n(7897),
        s = {
          miniAppID: "wxd2606008715f7ba5",
          reportURL: "https://dj.saasz.vdyoo.com/appid/c.gif",
          appid: "1005700",
          common: {
            eventid: "xwx_market_miniapp"
          },
          cacheUploader: {
            batch: 1,
            interval: 2e3
          },
          network: {
            open: !0,
            sample: 1,
            filter: function(t, e) {
              return e.length > 2e4 || ["static0.xesimg.com", "xue.xiwang.com", "aliyuncs.com", "log.saasz.vdyoo.com/log", "chatconf.saasw.vdyoo.com/v1/server/time", "time.xueersi.com/api/timestamp", "passport.vdyoo.com/v1/mini"].some((function(e) {
                return t.indexOf(e) > -1
              }))
            }
          },
          runtime: {
            open: !0,
            sample: 1,
            filter: function(t) {
              return "" === t || ["role is not host", "[Client]", "Cannot read properties", "null is not an object", "getPrivacySetting", "routeDone with a webviewId", "Warning:", "lottie", "[object Object]", "set width out of range", "图片预加载失败", "网络链接超时", "127.0.0.1", "IrcCore", "requestPayment:fail cancel"].some((function(e) {
                if (t.indexOf(e) > -1) return f({
                  eventtype: "filterError",
                  error: t
                }), !0
              }))
            }
          },
          performance: {
            open: !0,
            sample: 1
          }
        },
        l = function() {
          var t = new(c())(s);
          wx.xesLogSdk = t,
            function() {
              var t = function() {
                i().getNetworkType({
                  success: function(t) {
                    wx.xesLogSdk.setCommonParams({
                      networkType: JSON.stringify(t)
                    })
                  }
                })
              };
              t(), setInterval(t, 5e3)
            }();
          var e = i().getStorageSync("stuId");
          e && (console.log("初始化获取到userid", e), wx.xesLogSdk.setCommonParams({
            userid: e
          }))
        },
        f = function(t) {
          var e;
          null === (e = wx.xesLogSdk) || void 0 === e || e.report((0, r.Z)((0, r.Z)({}, t), {}, {
            s_t: (0, u.Ei)()
          }))
        },
        p = function(t) {
          if (t) {
            var e = i().getSystemInfoSync();
            wx.xesLogSdk.setCommonParams({
              courseid: t.stuLiveInfo.courseId,
              liveid: t.planInfo.id,
              teacherid: t.teacherInfo.id,
              coachid: t.counselorInfo.id,
              classid: t.stuLiveInfo.classId,
              subjectid: t.planInfo.subjectIds,
              gradeid: t.planInfo.gradeIds,
              userid: t.stuInfo.id,
              teacherrole: "-",
              livepattern: t.planInfo.pattern,
              device: e.brand,
              model: e.model,
              plantform: e.platform,
              isplayback: "-",
              livetype: 3,
              business_type: "xwx"
            })
          }
        }
    },
    7491: function(t, e, n) {
      var r, o, i, a = n(7425).default;
      ! function(n, c) {
        "object" == a(e) ? c(e) : (o = [e], void 0 === (i = "function" == typeof(r = c) ? r.apply(e, o) : r) || (t.exports = i))
      }(0, (function(t) {
        "use strict";

        function e() {
          e = function() {
            return t
          };
          var t = {},
            n = Object.prototype,
            r = n.hasOwnProperty,
            o = Object.defineProperty || function(t, e, n) {
              t[e] = n.value
            },
            i = "function" == typeof Symbol ? Symbol : {},
            c = i.iterator || "@@iterator",
            u = i.asyncIterator || "@@asyncIterator",
            s = i.toStringTag || "@@toStringTag";

          function l(t, e, n) {
            return Object.defineProperty(t, e, {
              value: n,
              enumerable: !0,
              configurable: !0,
              writable: !0
            }), t[e]
          }
          try {
            l({}, "")
          } catch (t) {
            l = function(t, e, n) {
              return t[e] = n
            }
          }

          function f(t, e, n, r) {
            var i = e && e.prototype instanceof h ? e : h,
              a = Object.create(i.prototype),
              c = new $(r || []);
            return o(a, "_invoke", {
              value: x(t, n, c)
            }), a
          }

          function p(t, e, n) {
            try {
              return {
                type: "normal",
                arg: t.call(e, n)
              }
            } catch (t) {
              return {
                type: "throw",
                arg: t
              }
            }
          }
          t.wrap = f;
          var d = {};

          function h() {}

          function g() {}

          function v() {}
          var m = {};
          l(m, c, (function() {
            return this
          }));
          var y = Object.getPrototypeOf,
            b = y && y(y(I([])));
          b && b !== n && r.call(b, c) && (m = b);
          var w = v.prototype = h.prototype = Object.create(m);

          function S(t) {
            ["next", "throw", "return"].forEach((function(e) {
              l(t, e, (function(t) {
                return this._invoke(e, t)
              }))
            }))
          }

          function _(t, e) {
            function n(o, i, c, u) {
              var s = p(t[o], t, i);
              if ("throw" !== s.type) {
                var l = s.arg,
                  f = l.value;
                return f && "object" == a(f) && r.call(f, "__await") ? e.resolve(f.__await).then((function(t) {
                  n("next", t, c, u)
                }), (function(t) {
                  n("throw", t, c, u)
                })) : e.resolve(f).then((function(t) {
                  l.value = t, c(l)
                }), (function(t) {
                  return n("throw", t, c, u)
                }))
              }
              u(s.arg)
            }
            var i;
            o(this, "_invoke", {
              value: function(t, r) {
                function o() {
                  return new e((function(e, o) {
                    n(t, r, e, o)
                  }))
                }
                return i = i ? i.then(o, o) : o()
              }
            })
          }

          function x(t, e, n) {
            var r = "suspendedStart";
            return function(o, i) {
              if ("executing" === r) throw new Error("Generator is already running");
              if ("completed" === r) {
                if ("throw" === o) throw i;
                return {
                  value: void 0,
                  done: !0
                }
              }
              for (n.method = o, n.arg = i;;) {
                var a = n.delegate;
                if (a) {
                  var c = T(a, n);
                  if (c) {
                    if (c === d) continue;
                    return c
                  }
                }
                if ("next" === n.method) n.sent = n._sent = n.arg;
                else if ("throw" === n.method) {
                  if ("suspendedStart" === r) throw r = "completed", n.arg;
                  n.dispatchException(n.arg)
                } else "return" === n.method && n.abrupt("return", n.arg);
                r = "executing";
                var u = p(t, e, n);
                if ("normal" === u.type) {
                  if (r = n.done ? "completed" : "suspendedYield", u.arg === d) continue;
                  return {
                    value: u.arg,
                    done: n.done
                  }
                }
                "throw" === u.type && (r = "completed", n.method = "throw", n.arg = u.arg)
              }
            }
          }

          function T(t, e) {
            var n = e.method,
              r = t.iterator[n];
            if (void 0 === r) return e.delegate = null, "throw" === n && t.iterator.return && (e.method = "return", e.arg = void 0, T(t, e), "throw" === e.method) || "return" !== n && (e.method = "throw", e.arg = new TypeError("The iterator does not provide a '" + n + "' method")), d;
            var o = p(r, t.iterator, e.arg);
            if ("throw" === o.type) return e.method = "throw", e.arg = o.arg, e.delegate = null, d;
            var i = o.arg;
            return i ? i.done ? (e[t.resultName] = i.value, e.next = t.nextLoc, "return" !== e.method && (e.method = "next", e.arg = void 0), e.delegate = null, d) : i : (e.method = "throw", e.arg = new TypeError("iterator result is not an object"), e.delegate = null, d)
          }

          function k(t) {
            var e = {
              tryLoc: t[0]
            };
            1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e)
          }

          function E(t) {
            var e = t.completion || {};
            e.type = "normal", delete e.arg, t.completion = e
          }

          function $(t) {
            this.tryEntries = [{
              tryLoc: "root"
            }], t.forEach(k, this), this.reset(!0)
          }

          function I(t) {
            if (t) {
              var e = t[c];
              if (e) return e.call(t);
              if ("function" == typeof t.next) return t;
              if (!isNaN(t.length)) {
                var n = -1,
                  o = function e() {
                    for (; ++n < t.length;)
                      if (r.call(t, n)) return e.value = t[n], e.done = !1, e;
                    return e.value = void 0, e.done = !0, e
                  };
                return o.next = o
              }
            }
            return {
              next: A
            }
          }

          function A() {
            return {
              value: void 0,
              done: !0
            }
          }
          return g.prototype = v, o(w, "constructor", {
            value: v,
            configurable: !0
          }), o(v, "constructor", {
            value: g,
            configurable: !0
          }), g.displayName = l(v, s, "GeneratorFunction"), t.isGeneratorFunction = function(t) {
            var e = "function" == typeof t && t.constructor;
            return !!e && (e === g || "GeneratorFunction" === (e.displayName || e.name))
          }, t.mark = function(t) {
            return Object.setPrototypeOf ? Object.setPrototypeOf(t, v) : (t.__proto__ = v, l(t, s, "GeneratorFunction")), t.prototype = Object.create(w), t
          }, t.awrap = function(t) {
            return {
              __await: t
            }
          }, S(_.prototype), l(_.prototype, u, (function() {
            return this
          })), t.AsyncIterator = _, t.async = function(e, n, r, o, i) {
            void 0 === i && (i = Promise);
            var a = new _(f(e, n, r, o), i);
            return t.isGeneratorFunction(n) ? a : a.next().then((function(t) {
              return t.done ? t.value : a.next()
            }))
          }, S(w), l(w, s, "Generator"), l(w, c, (function() {
            return this
          })), l(w, "toString", (function() {
            return "[object Generator]"
          })), t.keys = function(t) {
            var e = Object(t),
              n = [];
            for (var r in e) n.push(r);
            return n.reverse(),
              function t() {
                for (; n.length;) {
                  var r = n.pop();
                  if (r in e) return t.value = r, t.done = !1, t
                }
                return t.done = !0, t
              }
          }, t.values = I, $.prototype = {
            constructor: $,
            reset: function(t) {
              if (this.prev = 0, this.next = 0, this.sent = this._sent = void 0, this.done = !1, this.delegate = null, this.method = "next", this.arg = void 0, this.tryEntries.forEach(E), !t)
                for (var e in this) "t" === e.charAt(0) && r.call(this, e) && !isNaN(+e.slice(1)) && (this[e] = void 0)
            },
            stop: function() {
              this.done = !0;
              var t = this.tryEntries[0].completion;
              if ("throw" === t.type) throw t.arg;
              return this.rval
            },
            dispatchException: function(t) {
              if (this.done) throw t;
              var e = this;

              function n(n, r) {
                return a.type = "throw", a.arg = t, e.next = n, r && (e.method = "next", e.arg = void 0), !!r
              }
              for (var o = this.tryEntries.length - 1; o >= 0; --o) {
                var i = this.tryEntries[o],
                  a = i.completion;
                if ("root" === i.tryLoc) return n("end");
                if (i.tryLoc <= this.prev) {
                  var c = r.call(i, "catchLoc"),
                    u = r.call(i, "finallyLoc");
                  if (c && u) {
                    if (this.prev < i.catchLoc) return n(i.catchLoc, !0);
                    if (this.prev < i.finallyLoc) return n(i.finallyLoc)
                  } else if (c) {
                    if (this.prev < i.catchLoc) return n(i.catchLoc, !0)
                  } else {
                    if (!u) throw new Error("try statement without catch or finally");
                    if (this.prev < i.finallyLoc) return n(i.finallyLoc)
                  }
                }
              }
            },
            abrupt: function(t, e) {
              for (var n = this.tryEntries.length - 1; n >= 0; --n) {
                var o = this.tryEntries[n];
                if (o.tryLoc <= this.prev && r.call(o, "finallyLoc") && this.prev < o.finallyLoc) {
                  var i = o;
                  break
                }
              }
              i && ("break" === t || "continue" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null);
              var a = i ? i.completion : {};
              return a.type = t, a.arg = e, i ? (this.method = "next", this.next = i.finallyLoc, d) : this.complete(a)
            },
            complete: function(t, e) {
              if ("throw" === t.type) throw t.arg;
              return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), d
            },
            finish: function(t) {
              for (var e = this.tryEntries.length - 1; e >= 0; --e) {
                var n = this.tryEntries[e];
                if (n.finallyLoc === t) return this.complete(n.completion, n.afterLoc), E(n), d
              }
            },
            catch: function(t) {
              for (var e = this.tryEntries.length - 1; e >= 0; --e) {
                var n = this.tryEntries[e];
                if (n.tryLoc === t) {
                  var r = n.completion;
                  if ("throw" === r.type) {
                    var o = r.arg;
                    E(n)
                  }
                  return o
                }
              }
              throw new Error("illegal catch attempt")
            },
            delegateYield: function(t, e, n) {
              return this.delegate = {
                iterator: I(t),
                resultName: e,
                nextLoc: n
              }, "next" === this.method && (this.arg = void 0), d
            }
          }, t
        }

        function n(t) {
          return (n = "function" == typeof Symbol && "symbol" == a(Symbol.iterator) ? function(t) {
            return a(t)
          } : function(t) {
            return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : a(t)
          })(t)
        }

        function r(t, e) {
          for (var n = 0; n < e.length; n++) {
            var r = e[n];
            r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(t, (o = r.key, "symbol" == a(i = function(t, e) {
              if ("object" != a(t) || null === t) return t;
              var n = t[Symbol.toPrimitive];
              if (void 0 !== n) {
                var r = n.call(t, e);
                if ("object" != a(r)) return r;
                throw new TypeError("@@toPrimitive must return a primitive value.")
              }
              return String(t)
            }(o, "string")) ? i : String(i)), r)
          }
          var o, i
        }

        function o(t, e) {
          return function(t) {
            if (Array.isArray(t)) return t
          }(t) || function(t, e) {
            var n = null == t ? null : "undefined" != typeof Symbol && t[Symbol.iterator] || t["@@iterator"];
            if (null != n) {
              var r, o, i, a, c = [],
                u = !0,
                s = !1;
              try {
                if (i = (n = n.call(t)).next, 0 === e) {
                  if (Object(n) !== n) return;
                  u = !1
                } else
                  for (; !(u = (r = i.call(n)).done) && (c.push(r.value), c.length !== e); u = !0);
              } catch (t) {
                s = !0, o = t
              } finally {
                try {
                  if (!u && null != n.return && (a = n.return(), Object(a) !== a)) return
                } finally {
                  if (s) throw o
                }
              }
              return c
            }
          }(t, e) || i(t, e) || function() {
            throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
          }()
        }

        function i(t, e) {
          if (t) {
            if ("string" == typeof t) return c(t, e);
            var n = Object.prototype.toString.call(t).slice(8, -1);
            return "Object" === n && t.constructor && (n = t.constructor.name), "Map" === n || "Set" === n ? Array.from(t) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? c(t, e) : void 0
          }
        }

        function c(t, e) {
          (null == e || e > t.length) && (e = t.length);
          for (var n = 0, r = new Array(e); n < e; n++) r[n] = t[n];
          return r
        }

        function u(t, e) {
          var n = "undefined" != typeof Symbol && t[Symbol.iterator] || t["@@iterator"];
          if (!n) {
            if (Array.isArray(t) || (n = i(t)) || e && t && "number" == typeof t.length) {
              n && (t = n);
              var r = 0,
                o = function() {};
              return {
                s: o,
                n: function() {
                  return r >= t.length ? {
                    done: !0
                  } : {
                    done: !1,
                    value: t[r++]
                  }
                },
                e: function(t) {
                  throw t
                },
                f: o
              }
            }
            throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
          }
          var a, c = !0,
            u = !1;
          return {
            s: function() {
              n = n.call(t)
            },
            n: function() {
              var t = n.next();
              return c = t.done, t
            },
            e: function(t) {
              u = !0, a = t
            },
            f: function() {
              try {
                c || null == n.return || n.return()
              } finally {
                if (u) throw a
              }
            }
          }
        }

        function s(t) {
          return t && Math.random() >= t
        }

        function l() {
          try {
            return wx
          } catch (t) {}
        }

        function f(t) {
          var e = /at\s+(.*)\s+\((.*):(\d*):(\d*)\)/gi.exec(t) || /at\s+()(.*):(\d*):(\d*)/gi.exec(t) || /(.*)@(.*):(\d*):(\d*)/gi.exec(t);
          if (e && 5 === e.length) {
            var n = o(e, 5);
            n[0];
            var r = n[1],
              i = n[2],
              a = n[3],
              c = n[4];
            return {
              func: r,
              url: i,
              line: Number(a),
              col: Number(c),
              fileName: i.substring(i.lastIndexOf("/") + 1, i.length)
            }
          }
        }

        function p() {
          var t = v() || {},
            e = t.platform,
            n = t.system,
            r = t.language,
            o = t.model,
            i = t.version;
          return "Mozilla/5.0 (".concat(e, "; U; ").concat(n, "; ").concat(r, "; M; ").concat(o, ") MiniProgram/").concat(i)
        }
        var d = Object.prototype.toString;

        function h(t) {
          return function(e) {
            return d.call(e) === "[object ".concat(t, "]")
          }
        }
        var g = {
          isNumber: h("Number"),
          isString: h("String"),
          isBoolean: h("Boolean"),
          isNull: h("Null"),
          isUndefined: h("Undefined"),
          isSymbol: h("Symbol"),
          isFunction: h("Function"),
          isObject: h("Object"),
          isArray: h("Array"),
          isProcess: h("process"),
          isWindow: h("Window")
        };

        function v() {
          try {
            return l().getSystemInfoSync()
          } catch (t) {
            throw new Error("调用失败")
          }
        }

        function m() {
          var t = l();
          return new Promise((function(e, n) {
            t.getNetworkType({
              success: function(t) {
                e(t.networkType)
              },
              fail: function(t) {
                n(t)
              }
            })
          }))
        }
        var y = function(t) {
            (function(t) {
              var e = l().request,
                n = t.method,
                r = t.url,
                o = t.data,
                i = t.header,
                a = t.dataType;
              return new Promise((function(t, c) {
                e({
                  method: n,
                  url: r,
                  data: o,
                  header: i,
                  dataType: a,
                  success: function(e) {
                    t(e)
                  },
                  fail: function(t) {
                    c(t)
                  }
                })
              }))
            })({
              method: "POST",
              url: t.url,
              data: t.data,
              header: t.header,
              dataType: "json"
            }).then((function(e) {
              t.success && t.success(e)
            })).catch((function(e) {
              t.fail && t.fail(e)
            }))
          },
          b = l();

        function w(t) {
          var e, n = "function" == typeof(e = t.uv) ? e() : e || "";
          if (n) return b.setStorageSync("fe_log_uvid", n), n;
          var r = b.getStorageSync("fe_log_uvid") || "";
          if (r && /^\$[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/.test(r)) return r;
          var o = "$" + "xxxxxxxx-xxxx-4xxx-Nxxx-xxxxxxxxxxxx".replace(/[xN]/g, (function(t) {
            var e = 16 * Math.random() | 0;
            return ("x" === t ? e : 3 & e | 8).toString(16)
          }));
          return b.setStorageSync("fe_log_uvid", o), o
        }
        var S = Object.defineProperty,
          _ = Object.defineProperties,
          x = Object.getOwnPropertyDescriptors,
          T = Object.getOwnPropertySymbols,
          k = Object.prototype.hasOwnProperty,
          E = Object.prototype.propertyIsEnumerable,
          $ = function(t, e, n) {
            return e in t ? S(t, e, {
              enumerable: !0,
              configurable: !0,
              writable: !0,
              value: n
            }) : t[e] = n
          },
          I = function(t, e) {
            for (var n in e || (e = {})) k.call(e, n) && $(t, n, e[n]);
            if (T) {
              var r, o = u(T(e));
              try {
                for (o.s(); !(r = o.n()).done;) n = r.value, E.call(e, n) && $(t, n, e[n])
              } catch (t) {
                o.e(t)
              } finally {
                o.f()
              }
            }
            return t
          },
          A = function(t, e) {
            return _(t, x(e))
          },
          Z = l();

        function O(t, e, n) {
          return !(!t || "function" != typeof t || !t(e, n))
        }

        function P(t) {
          var e, n = t.res,
            r = t.data,
            o = t.sTime,
            i = t.fnSuccess,
            a = t.options,
            c = t.ignoreURL,
            u = t.opts,
            s = Date.now();
          if (r.elapsedTime = s - o, r.request = JSON.stringify(a.data), r.response = JSON.stringify(n.data), r.status = n.statusCode, 200 === n.statusCode ? (r.statusText = "OK", r.type = "Performance-Ajax") : r.type = "Ajax", c.test(r.resourceUrl) || O(null == (e = u.network) ? void 0 : e.filter, r.resourceUrl, JSON.stringify(n)) || i && i(r), "function" == typeof a.success) return a.success(n)
        }

        function j(t) {
          var e, n = t.err,
            r = t.data,
            o = t.sTime,
            i = t.fnFail,
            a = t.options,
            c = t.ignoreURL,
            u = t.opts,
            s = Date.now();
          if (r.elapsedTime = s - o, r.request = JSON.stringify(a.data), r.response = JSON.stringify(n), r.status = "", r.statusText = "", r.type = "Ajax", c.test(r.resourceUrl) || O(null == (e = u.network) ? void 0 : e.filter, r.resourceUrl, JSON.stringify(n)) || i && i(r), "function" == typeof a.fail) return a.fail(n)
        }

        function D(t, e) {
          var n, r, o = e.method,
            i = e.fn,
            a = e.args[0],
            c = {
              currenthref: L().currentHref,
              col: "",
              line: "",
              mode: "stack",
              msg: "",
              stack: [],
              name: "",
              url: "",
              fileName: "",
              type: "Script"
            },
            u = [];
          "onUnhandledRejection" === o && (null == (n = null == a ? void 0 : a.reason) ? void 0 : n.stack) ? (u = a.reason.stack.split("\n"), c.msg = a.reason.message) : "onError" === o && a && (u = a.split("\n"), c.msg = u[1] || "", c.name = (null == (r = u[2]) ? void 0 : r.split(":")[0]) || "");
          for (var s = [], l = 0; l < u.length; l++) {
            var p = f(u[l]);
            p && s.push(A(I({}, p), {
              args: [],
              context: null
            }))
          }
          var d = s[0] || {},
            h = d.line,
            g = d.col,
            v = d.url;
          c.line = h || "", c.col = g || "", c.url = v || "", c.fileName = v || "", c.stack = s, !U(t.runtime.filter, c.msg) && i && i(c)
        }

        function C(t, e) {
          var n = {
            currenthref: t[0].path,
            type: "PV"
          };
          e && e(n)
        }

        function L() {
          var t = getCurrentPages(),
            e = t[t.length - 1] || {},
            n = e.route,
            r = e.options,
            o = "";
          for (var i in r) o += "".concat(i, "=").concat(r[i], "&");
          return {
            currentHref: n + ((o = o.slice(0, -1)) ? "?" + o : ""),
            currentRoute: n,
            currentOptions: r
          }
        }

        function N(t) {
          if (!g.isFunction(getCurrentPages)) return "";
          var e = getCurrentPages();
          if (!e.length) return "App";
          t = t || 1;
          var n, r, o, i = e[e.length - t];
          return n = i.route, r = i.options, o = [], Object.keys(r).forEach((function(t) {
            o.push("".concat(t, "=").concat(r[t]))
          })), -1 !== n.indexOf("?") ? "".concat(n, "&").concat(o.join("&")) : "".concat(n, "?").concat(o.join("&"))
        }

        function R(t, e, n, r) {
          if (void 0 !== t && (e in t || r)) {
            var o = n(t[e]);
            "function" == typeof o && (t[e] = o)
          }
        }
        var U = function(t, e) {
          return !(!t || "function" != typeof t || !t(e))
        };

        function M(t, n) {
          return r = this, o = e().mark((function r() {
            var o, i, a, c, u, s, l, f, d, h, g, y, b, S, _, x, T, k, E, $, I, A, Z, O, P, j, D, C, N;
            return e().wrap((function(e) {
              for (;;) switch (e.prev = e.next) {
                case 0:
                  a = "";
                  try {
                    a = (null == (i = null == (o = null == __wxConfig ? void 0 : __wxConfig.global) ? void 0 : o.window) ? void 0 : i.navigationBarTitleText) || ""
                  } catch (t) {
                    a = ""
                  }
                  return c = L(), u = c.currentRoute, s = "https://servicewechat.com/".concat(n.miniAppID, "/").concat(u), l = p(), f = (new Date).getTime(), d = w(n), h = v(), g = h.version, y = h.screenWidth, b = h.system, S = h.windowHeight, _ = h.model, x = h.wifiEnabled, T = h.memorySize, k = h.screenHeight, E = h.windowWidth, $ = h.language, I = h.brand, A = h.platform, Z = h.SDKVersion, O = h.enableDebug, P = {
                    version: g,
                    screenWidth: y,
                    system: b,
                    windowHeight: S,
                    model: _,
                    wifiEnabled: x,
                    memorySize: T,
                    screenHeight: k,
                    windowWidth: E,
                    language: $,
                    brand: I,
                    platform: A,
                    SDKVersion: Z,
                    enableDebug: O
                  }, e.next = 11, m();
                case 11:
                  return j = e.sent, t.eventid = n.common.eventid, t.extracommon = n.common, D = wx.getAccountInfoSync(), C = D.miniProgram, N = {
                    data: t,
                    pageid: a,
                    referrer: s,
                    ua: l,
                    clits: f,
                    appid: n.appid,
                    version: "0.7.1",
                    appVersion: (null == C ? void 0 : C.version) || "-",
                    uvid: d,
                    rsd: l + f,
                    miniAppID: n.miniAppID,
                    sys: P,
                    effectiveType: j
                  }, e.abrupt("return", N);
                case 17:
                case "end":
                  return e.stop()
              }
            }), r)
          })), new Promise((function(t, e) {
            var n = function(t) {
                try {
                  a(o.next(t))
                } catch (t) {
                  e(t)
                }
              },
              i = function(t) {
                try {
                  a(o.throw(t))
                } catch (t) {
                  e(t)
                }
              },
              a = function(e) {
                return e.done ? t(e.value) : Promise.resolve(e.value).then(n, i)
              };
            a((o = o.apply(r, null)).next())
          }));
          var r, o
        }
        var H = l();

        function B(t) {
          try {
            var e = H.getStorageSync("fe_log_cache"),
              n = JSON.stringify(t);
            return e ? (e.push(n), H.setStorageSync("fe_log_cache", e), e) : (H.setStorageSync("fe_log_cache", [n]), [n])
          } catch (t) {
            return []
          }
        }
        var q = l(),
          F = null;

        function z(t, e, n) {
          t.data = "".concat("content=", "[").concat(e, "]"), t.header = {
            "X-Log-TimeStamp": (new Date).getTime().toString(),
            "X-Log-Appid": n.appid,
            "X-Log-Referer": "servicewechat.com",
            "content-type": "text/plain;charset=UTF-8"
          }, y(t)
        }

        function G(t, n) {
          return r = this, o = e().mark((function r() {
            var o, i, a, c;
            return e().wrap((function(e) {
              for (;;) switch (e.prev = e.next) {
                case 0:
                  return a = function(t, e, n) {
                    z(t, e, n), q.setStorageSync("fe_log_cache", [])
                  }, (o = {}).url = n.reportURL, e.next = 5, M(t.data, n);
                case 5:
                  if (i = e.sent, n.debugger && console.log("----------log-save----------".concat(i.data.type, "----------"), i), "report" !== i.data.type) {
                    e.next = 10;
                    break
                  }
                  return z(o, JSON.stringify(i), n), e.abrupt("return");
                case 10:
                  if (c = B(i) || [], clearTimeout(F), !(c.length < n.cacheUploader.batch)) {
                    e.next = 15;
                    break
                  }
                  return c.length > 0 && (F = setTimeout((function() {
                    a(o, c, n)
                  }), n.cacheUploader.interval)), e.abrupt("return", !1);
                case 15:
                  a(o, c, n);
                case 16:
                case "end":
                  return e.stop()
              }
            }), r)
          })), new Promise((function(t, e) {
            var n = function(t) {
                try {
                  a(o.next(t))
                } catch (t) {
                  e(t)
                }
              },
              i = function(t) {
                try {
                  a(o.throw(t))
                } catch (t) {
                  e(t)
                }
              },
              a = function(e) {
                return e.done ? t(e.value) : Promise.resolve(e.value).then(n, i)
              };
            a((o = o.apply(r, null)).next())
          }));
          var r, o
        }

        function J(t) {
          return function(e) {
            G({
              method: "POST",
              data: Object.assign(e, t.param),
              headers: {}
            }, t)
          }
        }
        var W = Object.defineProperty,
          K = Object.defineProperties,
          V = Object.getOwnPropertyDescriptors,
          X = Object.getOwnPropertySymbols,
          Q = Object.prototype.hasOwnProperty,
          Y = Object.prototype.propertyIsEnumerable,
          tt = function(t, e, n) {
            return e in t ? W(t, e, {
              enumerable: !0,
              configurable: !0,
              writable: !0,
              value: n
            }) : t[e] = n
          },
          et = l();

        function nt(t) {
          var e, n, r;
          t.debugger && console.log("初始化配置：", t);
          var o, i = J(t);
          (null == (e = null == t ? void 0 : t.runtime) ? void 0 : e.open) && (function(t, e) {
            var n;
            console.error = (n = console.error, function() {
              for (var r = arguments.length, o = new Array(r), i = 0; i < r; i++) o[i] = arguments[i];
              var a = {
                currenthref: L().currentHref,
                mode: "stack",
                msg: o.join(", "),
                name: "consoleError",
                type: "Script"
              };
              !U(t.runtime.filter, a.msg) && e && e(a), n.apply(this, o)
            })
          }(t, (function(e) {
            var n;
            !s(null == (n = null == t ? void 0 : t.runtime) ? void 0 : n.sample) && i(e)
          })), function(t, e) {
            if (App) {
              var n = App;
              App = function(r) {
                return ["onError", "onUnhandledRejection"].forEach((function(n) {
                  R(r, n, (function(r) {
                    return function() {
                      for (var o = arguments.length, i = new Array(o), a = 0; a < o; a++) i[a] = arguments[a];
                      r && r.apply(this, i), D(t, {
                        method: n,
                        fn: e,
                        args: i
                      })
                    }
                  }), !0)
                })), n(r)
              }
            } else console.log("当前环境不支持 App.onError")
          }(t, (function(e) {
            var n;
            !s(null == (n = null == t ? void 0 : t.runtime) ? void 0 : n.sample) && i(e)
          }))),
          function(t) {
            if (App) {
              var e = App;
              App = function(n) {
                return ["onLaunch"].forEach((function(e) {
                  R(n, e, (function(e) {
                    return function() {
                      for (var n = arguments.length, r = new Array(n), o = 0; o < n; o++) r[o] = arguments[o];
                      e && e.apply(this, r), C(r, t)
                    }
                  }), !0)
                })), e(n)
              }
            } else console.log("当前环境不支持 App.onLaunch")
          }((function(t) {
            i(t)
          })), o = function(t) {
            i(t)
          }, ["switchTab", "reLaunch", "redirectTo", "navigateTo", "navigateBack", "navigateToMiniProgram", "routeFail"].forEach((function(t) {
            var e = Z[t];
            Object.defineProperty(Z, t, {
              writable: !0,
              enumerable: !0,
              configurable: !0,
              value: function() {
                var n = arguments.length <= 0 ? void 0 : arguments[0],
                  r = {
                    currenthref: "navigateBack" === t ? N(null == n ? void 0 : n.delta) : n.url,
                    type: "PV"
                  };
                o && o(r);
                var i = I({}, n);
                return e.call(this, i)
              }
            })
          })), (null == (n = null == t ? void 0 : t.network) ? void 0 : n.open) && function(t, e, n) {
            ["request", "downloadFile", "uploadFile"].forEach((function(r) {
              var o = Z[r];
              Object.defineProperty(Z, r, {
                writable: !0,
                enumerable: !0,
                configurable: !0,
                value: function() {
                  var r, i = arguments.length <= 0 ? void 0 : arguments[0],
                    a = Date.now(),
                    c = {
                      currenthref: L().currentHref,
                      resourceUrl: i.url,
                      method: null == (r = i.method) ? void 0 : r.toUpperCase()
                    },
                    u = new RegExp(t.reportURL),
                    s = function(n) {
                      P({
                        res: n,
                        data: c,
                        sTime: a,
                        fnSuccess: e,
                        options: i,
                        ignoreURL: u,
                        opts: t
                      })
                    },
                    l = function(e) {
                      j({
                        err: e,
                        data: c,
                        sTime: a,
                        fnFail: n,
                        options: i,
                        ignoreURL: u,
                        opts: t
                      })
                    },
                    f = A(I({}, i), {
                      success: s,
                      fail: l
                    });
                  return o.call(this, f)
                }
              })
            }))
          }(t, (function(e) {
            var n;
            !s(null == (n = null == t ? void 0 : t.network) ? void 0 : n.sample) && i(e)
          }), (function(e) {
            var n;
            !s(null == (n = null == t ? void 0 : t.network) ? void 0 : n.sample) && i(e)
          })), (null == (r = null == t ? void 0 : t.performance) ? void 0 : r.open) && function(t) {
            et.getPerformance().createObserver((function(e) {
              var n, r = {};
              e.getEntries().forEach((function(t) {
                for (var e in t) "name" === e && (r[t[e]] = t.duration)
              }));
              var o, i, a = (o = function(t, e) {
                for (var n in e || (e = {})) Q.call(e, n) && tt(t, n, e[n]);
                if (X) {
                  var r, o = u(X(e));
                  try {
                    for (o.s(); !(r = o.n()).done;) n = r.value, Y.call(e, n) && tt(t, n, e[n])
                  } catch (t) {
                    o.e(t)
                  } finally {
                    o.f()
                  }
                }
                return t
              }({}, r), i = {
                currenthref: null == (n = L()) ? void 0 : n.currentHref,
                type: "Performance-OL",
                performanceType: r.appLaunch ? "launch" : "runtime"
              }, K(o, V(i)));
              t && t(a)
            })).observe({
              entryTypes: ["render", "script", "navigation"]
            })
          }((function(e) {
            var n;
            !s(null == (n = null == t ? void 0 : t.performance) ? void 0 : n.sample) && i(e)
          }))
        }
        var rt = Object.defineProperty,
          ot = Object.defineProperties,
          it = Object.getOwnPropertyDescriptors,
          at = Object.getOwnPropertySymbols,
          ct = Object.prototype.hasOwnProperty,
          ut = Object.prototype.propertyIsEnumerable,
          st = function(t, e, n) {
            return e in t ? rt(t, e, {
              enumerable: !0,
              configurable: !0,
              writable: !0,
              value: n
            }) : t[e] = n
          },
          lt = function(t, e) {
            for (var n in e || (e = {})) ct.call(e, n) && st(t, n, e[n]);
            if (at) {
              var r, o = u(at(e));
              try {
                for (o.s(); !(r = o.n()).done;) n = r.value, ut.call(e, n) && st(t, n, e[n])
              } catch (t) {
                o.e(t)
              } finally {
                o.f()
              }
            }
            return t
          },
          ft = function(t, e) {
            return ot(t, it(e))
          },
          pt = {
            miniAppID: {
              required: !0,
              type: "string"
            },
            reportURL: {
              required: !1,
              type: "string"
            },
            appid: {
              required: !0,
              type: "string"
            },
            common: {
              required: !0,
              type: "object",
              rule: {
                eventid: {
                  required: !0,
                  type: "string"
                }
              }
            },
            cacheUploader: {
              required: !1,
              type: "object",
              rule: {
                batch: {
                  required: !1,
                  type: "number"
                },
                interval: {
                  required: !1,
                  type: "number"
                }
              }
            },
            network: {
              required: !1,
              type: "object",
              rule: {
                open: {
                  required: !1,
                  type: "boolean"
                },
                sample: {
                  required: !1,
                  type: "number"
                }
              }
            },
            runtime: {
              required: !1,
              type: "object",
              rule: {
                open: {
                  required: !1,
                  type: "boolean"
                },
                sample: {
                  required: !1,
                  type: "number"
                }
              }
            },
            performance: {
              required: !1,
              type: "object",
              rule: {
                open: {
                  required: !1,
                  type: "boolean"
                },
                sample: {
                  required: !1,
                  type: "number"
                }
              }
            }
          },
          dt = function() {
            function t(e) {
              ! function(t, e) {
                if (!(t instanceof e)) throw new TypeError("Cannot call a class as a function")
              }(this, t), this._init(e), this.report = this.report.bind(this), this.setCommonParams = this.setCommonParams.bind(this)
            }
            var e, o;
            return e = t, (o = [{
              key: "setOption",
              value: function(t) {
                var e, n, r, o, i, a, c, u, s, l, f, p, d, h, g, v, m, y, b, S, _, x;
                return {
                  miniAppID: t.miniAppID,
                  reportURL: null != (e = null == t ? void 0 : t.reportURL) ? e : "https://dj.xesimg.com/appid/c.gif",
                  common: {
                    eventid: t.common.eventid
                  },
                  cacheUploader: {
                    batch: null != (r = null == (n = t.cacheUploader) ? void 0 : n.batch) ? r : 5,
                    interval: null != (i = null == (o = t.cacheUploader) ? void 0 : o.interval) ? i : 1e4
                  },
                  network: {
                    open: null != (c = null == (a = t.network) ? void 0 : a.open) && c,
                    sample: null != (s = null == (u = t.network) ? void 0 : u.sample) ? s : 1,
                    filter: null != (f = null == (l = t.network) ? void 0 : l.filter) ? f : null
                  },
                  performance: {
                    open: null != (d = null == (p = t.network) ? void 0 : p.open) && d,
                    sample: null != (g = null == (h = t.network) ? void 0 : h.sample) ? g : 1
                  },
                  runtime: {
                    open: null == (m = null == (v = t.network) ? void 0 : v.open) || m,
                    sample: null != (b = null == (y = t.network) ? void 0 : y.sample) ? b : 1,
                    filter: null != (_ = null == (S = t.runtime) ? void 0 : S.filter) ? _ : null
                  },
                  param: null != (x = t.param) ? x : {},
                  uv: w(t),
                  appid: t.appid,
                  debugger: ""
                }
              }
            }, {
              key: "_init",
              value: function(t) {
                return function t(e, r) {
                  var o = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "";
                  for (var i in e) {
                    var a = e[i],
                      c = r[i],
                      u = o ? "".concat(o, ".").concat(i) : i;
                    if (a.required && null == c) return console.error("Validation failed: ".concat(u, " is required")), !1;
                    if (null != c) {
                      if (n(c) !== a.type) return console.error("Validation failed: ".concat(u, " should be of type ").concat(a.type)), !1;
                      if ("object" === a.type && a.rule && !t(a.rule, c, u)) return !1
                    }
                  }
                  return !0
                }(pt, t) ? (this.opts = this.setOption(t), this.opts.debugger && console.warn("------开发环境已开启调试模式，生产环境不会打印日志------"), function() {
                  try {
                    return wx
                  } catch (t) {
                    return !1
                  }
                }() ? void nt(this.opts) : (console.warn("XesLoggerSDK: 当前仅支持微信小程序环境"), !1)) : (console.warn("参数校验失败，请参考手册：https://app.xesv5.com/doc/pages/fedata/fe-log-miniapp-sdk/access.html"), !1)
              }
            }, {
              key: "setCommonParams",
              value: function(t) {
                t && "object" === n(t) && (this.opts.param = lt(lt({}, this.opts.param), t))
              }
            }, {
              key: "report",
              value: function() {
                var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                if (t && "object" === n(t)) {
                  var e = J(this.opts),
                    r = Object.keys(t).reduce((function(e, r) {
                      return e[r] = "object" === n(t[r]) || "function" == typeof t[r] ? JSON.stringify(t[r]) : t[r], e
                    }), {});
                  e(ft(lt({}, r), {
                    type: "report"
                  }))
                }
              }
            }]) && r(e.prototype, o), Object.defineProperty(e, "prototype", {
              writable: !1
            }), t
          }();
        t.default = dt, t.optionRules = pt, Object.defineProperty(t, "__esModule", {
          value: !0
        })
      }))
    },
    7871: function(t, e, n) {
      "use strict";
      var r = n(9249),
        o = n(7371),
        i = n(6666),
        a = new((0, o.Z)((function t() {
          (0, r.Z)(this, t), (0, i.Z)(this, "eventMap", void 0), this.eventMap = {}
        }), [{
          key: "subscribe",
          value: function(t, e) {
            var n = this;
            return this.eventMap[t] || (this.eventMap[t] = []), this.eventMap[t].push(e), console.log("%c subscribe ".concat(t), "color: red"),
              function() {
                n.unsubscribe(t, e)
              }
          }
        }, {
          key: "notify",
          value: function(t) {
            for (var e = arguments.length, n = new Array(e > 1 ? e - 1 : 0), r = 1; r < e; r++) n[r - 1] = arguments[r];
            this.eventMap[t] && this.eventMap[t].forEach((function(t) {
              t.apply(void 0, n)
            }))
          }
        }, {
          key: "subscribeOnce",
          value: function(t, e) {
            var n = this,
              r = function() {
                e.apply(void 0, arguments), n.unsubscribe(t, r)
              };
            this.subscribe(t, r)
          }
        }, {
          key: "unsubscribe",
          value: function(t, e) {
            this.eventMap[t] && (e ? this.eventMap[t] = this.eventMap[t].filter((function(t) {
              return t !== e
            })) : delete this.eventMap[t], console.log("%c unsubscribe ".concat(t), "color: green"))
          }
        }, {
          key: "clear",
          value: function() {
            this.eventMap = {}
          }
        }]));
      e.Z = a
    },
    7897: function(t, e, n) {
      "use strict";
      n.d(e, {
        wM: function() {
          return s
        },
        yO: function() {
          return l
        },
        WW: function() {
          return o
        },
        GQ: function() {
          return i
        },
        es: function() {
          return f
        },
        zF: function() {
          return p
        },
        Ei: function() {
          return d
        }
      });
      var r = n(7871),
        o = function() {
          return !1
        },
        i = function() {
          return !1
        },
        a = null,
        c = 0,
        u = null,
        s = function(t, e) {
          t = t.replace(/-/g, "/"), e = e.replace(/-/g, "/"), o = function(t) {
            return function() {
              var e = !1;
              return d() >= new Date(t).getTime() && (e = !0), e
            }
          }(t), i = function(t) {
            return function() {
              var e = !1;
              return d() >= new Date(t).getTime() && (e = !0), e
            }
          }(e)
        };

      function l(t, e) {
        console.log("初始化时间校准函数"), t = t.replace(/-/g, "/"), a = function(t, e) {
          return function(e, n, o) {
            var i = d(),
              a = ((i - new Date(t).getTime()) / 1e3).toFixed(0);
            if (n && a > n) return console.log("视频已播放完，结束～", a, n), void o(null);
            parseInt(a) - e > 3 && (console.log("需要快进校准时间"), u && i - u < 1500 && (console.log("连续触发时间校准，需要重新创建视频上下文！"), r.Z.notify("reCreateVideoContext")), u = i, o && o(parseInt(a)))
          }
        }(t)
      }

      function f() {
        return a
      }

      function p(t) {
        if (t) {
          var e = (new Date).getTime();
          c = Math.round(e / 1e3 - t), console.log("calcaulate diff time:", c)
        }
      }

      function d() {
        var t = (new Date).getTime();
        return 0 !== c && (t -= 1e3 * c), t
      }
    },
    3494: function(t, e, n) {
      "use strict";
      n.d(e, {
        Mi: function() {
          return a
        },
        pE: function() {
          return c
        },
        w1: function() {
          return u
        },
        yC: function() {
          return s
        }
      });
      var r = n(1361),
        o = n(1678),
        i = n.n(o),
        a = function() {
          return "https://ac.xiwang.com/school-kanke"
        },
        c = function(t, e) {
          var n, o = "",
            i = (0, r.Z)(e);
          try {
            for (i.s(); !(n = i.n()).done;) {
              var a = n.value;
              t[a] && (o += "&".concat(a, "=").concat(t[a]))
            }
          } catch (t) {
            i.e(t)
          } finally {
            i.f()
          }
          return o = o.substr(1)
        },
        u = function(t) {
          return "android" === i().getSystemInfoSync().platform ? t / 1e3 : t
        },
        s = function(t, e) {
          for (var n = t.split("."), r = e.split("."), o = Math.max(n.length, r.length); n.length < o;) n.push("0");
          for (; r.length < o;) r.push("0");
          for (var i = 0; i < o; i++) {
            var a = parseInt(n[i]),
              c = parseInt(r[i]);
            if (a > c) return 1;
            if (a < c) return -1
          }
          return 0
        }
    },
    6224: function(t, e, n) {
      "use strict";
      var r = n(9593),
        o = n.n(r),
        i = n(1678),
        a = n.n(i),
        c = a().getAccountInfoSync().miniProgram.envVersion,
        u = o().init({
          pid: "b2yyrpml12@235075e5ec8bdd0",
          uid: a().getStorageSync("stuId") || "default",
          environment: {
            develop: "local",
            release: "prod",
            trial: "gray"
          } [c],
          region: "cn",
          behavior: !0,
          disableHook: !0,
          sample: 2,
          ignore: {
            ignoreApis: [function(t) {
              return !!(t && t.indexOf("/mini_program/template_list") >= 0) || !!(t && t.indexOf("login/LoginV1/getToken") >= 0) || !!(t && t.indexOf("school/look_course/get_user_info") >= 0)
            }]
          }
        });
      e.Z = u
    },
    9593: function(t, e, n) {
      "use strict";
      var r = n(4886).window,
        o = n(4886).location,
        i = n(4886).navigator,
        a = n(7425).default;
      Date.now = Date.now || function() {
        return (new Date).getTime()
      };
      var c = Date.now(),
        u = function() {},
        s = {
          noop: u,
          warn: function() {
            var t = "object" == ("undefined" == typeof console ? "undefined" : a(console)) ? console.warn : u;
            try {
              var e = {
                warn: t
              };
              e.warn.call(e)
            } catch (t) {
              return u
            }
            return t
          }(),
          key: "__bl",
          selfErrKey: "ARMS_SDK_ERROR",
          selfErrPage: "ARMSSDK",
          win: "object" == (void 0 === r ? "undefined" : a(r)) && r.document ? r : void 0,
          regionMap: {
            cn: "https://arms-retcode.aliyuncs.com/r.png?",
            sg: "https://arms-retcode-sg.aliyuncs.com/r.png?",
            sg_2: "https://retcode-sg-lazada.arms.aliyuncs.com/r.png?",
            daily: "http://arms-retcode-daily.alibaba.net/r.png?",
            daily_2: "https://arms-retcode-daily.alibaba.net/r.png?",
            us: "https://retcode-us-west-1.arms.aliyuncs.com/r.png?",
            tw: "https://arms-retcode.orientalgame.com.tw/r.png?",
            tw_sg: "https://arms-retcode-sg.orientalgame.com.tw/r.png?",
            hz_finance: "https://arms-retcode-hz-finance.aliyuncs.com/r.png?"
          },
          defaultImgUrl: "https://arms-retcode.aliyuncs.com/r.png?",
          $a2: function(t) {
            if (Object.create) return Object.create(t);
            var e = function() {};
            return e.prototype = t, new e
          },
          each: function(t, e) {
            var n = 0,
              r = t.length;
            if (this.T(t, "Array"))
              for (; n < r && !1 !== e.call(t[n], t[n], n); n++);
            else
              for (n in t)
                if (!1 === e.call(t[n], t[n], n)) break;
            return t
          },
          $a3: function(t, e, n) {
            if ("function" != typeof t) return n;
            try {
              return t.apply(this, e)
            } catch (t) {
              return n
            }
          },
          T: function(t, e) {
            var n = Object.prototype.toString.call(t).substring(8).replace("]", "");
            return e ? n === e : n
          },
          $a4: function(t, e) {
            if (!t) return "";
            if (!e) return t;
            var n = this,
              r = n.T(e);
            return "Function" === r ? n.$a3(e, [t], t) : "Array" === r ? (this.each(e, (function(e) {
              t = n.$a4(t, e)
            })), t) : "Object" === r ? t.replace(e.rule, e.target || "") : t.replace(e, "")
          },
          $a5: function(t, e) {
            if (!t || !e) return !1;
            if ((this.isString(e) || e.source || "Function" === this.T(e)) && (e = [e]), !this.isArray(e)) return this.warn("[arms] invalid rules of ignore config, (list of) String/RegExp/Funcitons are available"), !1;
            for (var n, r = [], o = 0, i = e.length; o < i; o++)
              if (n = e[o], this.isString(n)) r.push(n.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1"));
              else if (n && n.source) r.push(n.source);
            else if (n && "Function" === this.T(n) && !0 === this.$a3(n, [t], !1)) return !0;
            var a = new RegExp(r.join("|"), "i");
            return !!(r.length && a.test && a.test(t))
          },
          J: function(t) {
            if (!t || "string" != typeof t) return t;
            var e = null;
            try {
              e = JSON.parse(t)
            } catch (t) {}
            return e
          },
          pick: function(t) {
            return 1 === t || 1 === Math.ceil(Math.random() * t)
          },
          $a6: function(t) {
            if ("sample" in t) {
              var e = t.sample,
                n = e;
              e && /^\d+(\.\d+)?%$/.test(e) && (n = parseInt(100 / parseFloat(e))), 0 < n && 1 > n && (n = parseInt(1 / n)), n >= 1 && n <= 100 ? t.sample = n : delete t.sample
            }
            return t
          },
          on: function(t, e, n, r, o) {
            return t.addEventListener ? (o = o || !1, t.addEventListener(e, (function i(a) {
              r && t.removeEventListener(e, i, o), n.call(this, a)
            }), o)) : t.attachEvent && t.attachEvent("on" + e, (function o(i) {
              r && t.detachEvent("on" + e, o), n.call(this, i)
            })), this
          },
          off: function(t, e, n) {
            return n ? (t.removeEventListener ? t.removeEventListener(e, n) : t.detachEvent && t.detachEvent(e, n), this) : this
          },
          delay: function(t, e) {
            return -1 === e ? (t(), null) : setTimeout(t, e || 0)
          },
          ext: function(t) {
            for (var e = 1, n = arguments.length; e < n; e++) {
              var r = arguments[e];
              for (var o in r) Object.prototype.hasOwnProperty.call(r, o) && (t[o] = r[o])
            }
            return t
          },
          sub: function(t, e) {
            var n = {};
            return this.each(t, (function(t, r) {
              -1 !== e.indexOf(r) && (n[r] = t)
            })), n
          },
          uu: function() {
            for (var t, e, n = 20, r = new Array(n), o = Date.now().toString(36).split(""); n-- > 0;) e = (t = 36 * Math.random() | 0).toString(36), r[n] = t % 3 ? e : e.toUpperCase();
            for (var i = 0; i < 8; i++) r.splice(3 * i + 2, 0, o[i]);
            return r.join("")
          },
          seq: function() {
            return (c++).toString(36)
          },
          decode: function(t) {
            try {
              t = decodeURIComponent(t)
            } catch (t) {}
            return t
          },
          encode: function(t, e) {
            try {
              t = e ? encodeURIComponent(t).replace(/\(/g, "%28").replace(/\)/g, "%29") : encodeURIComponent(t)
            } catch (t) {}
            return t
          },
          serialize: function(t) {
            t = t || {};
            var e = [];
            for (var n in t) Object.prototype.hasOwnProperty.call(t, n) && void 0 !== t[n] && e.push(n + "=" + this.encode(t[n], "msg" === n));
            return e.join("&")
          },
          $a7: function(t, e) {
            if (!t || "string" != typeof t) return !1;
            var n = /arms-retcode[\w-]*\.aliyuncs/.test(t);
            return !n && e && (n = /(\.png)|(\.gif)|(alicdn\.com)|(mmstat\.com)/.test(t)), !n
          },
          $a8: function(t) {
            return !(!t || !t.message || /failed[\w\s]+fetch/i.test(t.message))
          },
          $a9: function(t) {
            return t && "string" == typeof t ? t.replace(/^(https?:)?\/\//, "").replace(/\?.*$/, "") : ""
          },
          $aa: function(t) {
            return t && "string" == typeof t ? t.replace(/\?.*$/, "") : ""
          },
          $ab: function(t) {
            return function() {
              return t + "() { [native code] }"
            }
          },
          checkSameOrigin: function(t, e) {
            if (!e || !t) return !1;
            var n = "//" + e.split("/")[2];
            return t === e || t.slice(0, e.length + 1) === e + "/" || t === n || t.slice(0, n.length + 1) === n + "/" || !/^(\/\/|http:|https:).*/.test(t)
          },
          getRandIP: function() {
            for (var t = [], e = 0; e < 4; e++) {
              var n = Math.floor(256 * Math.random());
              t[e] = (n > 15 ? "" : "0") + n.toString(16)
            }
            return t.join("").replace(/^0/, "1")
          },
          getSortNum: function(t) {
            return t ? (t += 1) >= 1e3 && t <= 9999 ? t : t < 1e3 ? t + 1e3 : t % 1e4 + 1e3 : 1e3
          },
          getRandNum: function(t) {
            return t && "string" == typeof t ? t.length < 5 ? this.getNum(5) : t.substring(t.length - 5) : this.getNum(5)
          },
          getNum: function(t) {
            for (var e = [], n = 0; n < t; n++) {
              var r = Math.floor(16 * Math.random());
              e[n] = r.toString(16)
            }
            return e.join("")
          },
          getCurDomain: function() {
            return o && o.hostname || ""
          },
          parseFetchHeaders: function(t) {
            if (!t) return {};
            var e = {};
            try {
              if ("function" == typeof t.keys)
                for (var n = t.keys(), r = n.next(); !r.done;) {
                  var o = r.value;
                  e[o] = t.get(o), r = n.next()
                } else e = t
            } catch (t) {
              e = {}
            }
            return e
          },
          parseXhrHeaders: function(t) {
            if (!t && "string" != typeof t) return {};
            var e = {};
            try {
              e = t.split("\r\n").reduce((function(t, e) {
                var n = e.split(": ");
                return t[n[0]] = n[1], t
              }), {})
            } catch (t) {
              e = {}
            }
            return e
          },
          getQuerys: function(t) {
            if (!t) return "";
            var e = {},
              n = [],
              r = "",
              o = "";
            try {
              var i = [];
              if (t.indexOf("?") >= 0 && (i = t.substring(t.indexOf("?") + 1, t.length).split("&")), i.length > 0)
                for (var a in i) r = (n = i[a].split("="))[0], o = n[1], e[r] = o
            } catch (t) {
              e = {}
            }
            return e
          },
          getFetchSnapshot: function(t, e, n) {
            var r, o;
            try {
              var i = (t && "string" != typeof t[0] ? t[0].url : t[0]) || "",
                a = (t && "string" != typeof t[0] ? t[0] : t[1]) || {},
                c = "POST" === a.method.toUpperCase() ? a.body : this.getQuerys(i);
              r = {
                originApi: i,
                method: a.method || "unknown",
                params: c,
                response: e || "",
                reqHeaders: this.parseFetchHeaders(a.headers || null),
                resHeaders: this.parseFetchHeaders(n)
              }, o = "function" == typeof encodeURIComponent && JSON && encodeURIComponent(JSON.stringify(r)) || "{}"
            } catch (t) {
              o = "{}"
            }
            return o
          },
          getXhrSnapshot: function(t, e, n) {
            if (!t || !e || !n) return {};
            var r, o;
            try {
              var i = "";
              "" === n.responseType || "text" === n.responseType ? i = n.responseText : "document" === n.responseType && (i = n.responseXML), r = {
                originApi: t,
                method: e,
                params: this.getQuerys(t),
                response: i,
                reqHeaders: {},
                resHeaders: this.parseXhrHeaders("function" == typeof n.getAllResponseHeaders && n.getAllResponseHeaders() || "")
              }, o = "function" == typeof encodeURIComponent && JSON && encodeURIComponent(JSON.stringify(r)) || "{}"
            } catch (t) {
              o = "{}"
            }
            return o
          },
          isRobot: function() {
            var t = ["nuhk", "googlebot/", "googlebot-image", "yammybot", "openbot", "slurp", "msnbot", "ask jeeves/teoma", "ia_archiver", "baiduspider", "bingbot/", "adsbot"];
            if (!i || "string" != typeof i.userAgent) return !1;
            try {
              for (var e = i.userAgent.toLowerCase(), n = 0; n < t.length; n++) {
                var r = t[n];
                if (e.lastIndexOf(r) >= 0) return !0
              }
            } catch (t) {
              this.warn("[arms] useragent parse error")
            }
            return !1
          },
          isFunction: function(t) {
            return "function" == typeof t
          },
          isPlainObject: function(t) {
            return "[object Object]" === Object.prototype.toString.call(t)
          },
          isString: function(t) {
            return "[object String]" === Object.prototype.toString.call(t)
          },
          isArray: function(t) {
            return "[object Array]" === Object.prototype.toString.call(t)
          },
          joinRegExp: function(t) {
            for (var e, n = [], r = 0, o = t.length; r < o; r++) e = t[r], this.isString(e) ? n.push(e.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1")) : e && e.source && n.push(e.source);
            return new RegExp(n.join("|"), "i")
          },
          reWriteMethod: function(t, e, n) {
            if (null !== t) {
              var r = t[e];
              t[e] = n(r)
            }
          },
          $ac: function(t, e) {
            return !(!t && !e || !new RegExp(this.selfErrKey, "i").test(t) && !this.$a5(e, [/retcode.alicdn.com\/retcode\/bl.js/, /g.alicdn.com\/retcode\/cloud-sdk\/bl.js/, /laz-g-cdn.alicdn.com\/retcode\/cloud-sdk\/bl.js/, /local.taobao.com:8880\/build\/bl/]))
          },
          $ad: function(t) {
            return {
              msg: t,
              message: this.selfErrKey
            }
          },
          $ae: function(t, e, n) {
            var r = {};
            try {
              r = this.isPlainObject(t) ? this.ext({
                key: t.key || "default",
                val: t.val || t.value || n
              }, t, {
                begin: Date.now()
              }) : {
                key: t || "default",
                val: e || n,
                begin: Date.now()
              }
            } catch (t) {
              this.warn("[retcode] baseLog error: " + t)
            }
            return r
          }
        },
        l = "aokcdqn3ly@e629dabd48a9933",
        f = function(t, e) {
          var n;
          if ("error" !== e.t || !(n = t.$af[0]) || "error" !== n.t || e.msg !== n.msg) {
            if ("behavior" === e.t) {
              var r = t.$af && t.$af.length;
              if (r > 0 && "behavior" === t.$af[r - 1].t) {
                var o = e.behavior || [];
                t.$af[r - 1].behavior.concat(o)
              } else t.$af.push(e)
            } else t.$af.unshift(e);
            return t.$ag((function() {
              t.$ah = s.delay((function() {
                t.$ai()
              }), t.$af[0] && "error" === t.$af[0].t ? 3e3 : -1)
            })), !0
          }
          n.times++
        },
        p = function(t) {
          return this.ver = "1.8.31", this._conf = s.ext({}, p.dftCon), this.$aj = {}, this.$af = [], this.$ak = [], this.sdkFlag = !0, this.hash = s.seq(), this.$al(), this.setConfig(t), this.rip = s.getRandIP(), this.record = 999, this["EagleEye-TraceID"] = this.getTraceId()["EagleEye-TraceID"], this._common = {}, this
        };
      p.dftCon = {
        sample: 1,
        pvSample: 1,
        tag: "",
        imgUrl: "https://arms-retcode.aliyuncs.com/r.png?",
        region: null,
        ignore: {
          ignoreUrls: [],
          ignoreApis: [],
          ignoreErrors: [/^Script error\.?$/],
          ignoreResErrors: []
        },
        release: void 0,
        environment: "prod"
      }, p.prototype = {
        constructor: p,
        $ag: function(t) {
          return t()
        },
        $am: function() {
          var t = this._conf.page;
          return s.$a3(t, [], t + "")
        },
        setPage: function() {},
        setConfig: function(t) {
          t && "object" == a(t) && (s.$a6(t), t = this.$an(t), this._conf = s.ext({}, this._conf, t))
        },
        $an: function(t) {
          var e = t.region,
            n = t.imgUrl;
          if (e) {
            var r = s.regionMap[e];
            return t.imgUrl = r || s.defaultImgUrl, t
          }
          return n && (t.imgUrl = n), t
        },
        $ao: function(t) {
          if (this.getConfig("debug")) return !0;
          var e = s.regionMap,
            n = !1;
          for (var r in e)
            if (e[r] === t) {
              n = !0;
              break
            } return !n && s.warn("[retcode] invalid url: " + t), n
        },
        $ap: function() {},
        $aq: function(t) {
          ! function(t, e) {
            "object" == a(t) && (t = s.serialize(t));
            var n = e + t;
            r && r.navigator && "function" == typeof r.navigator.sendBeacon ? r.navigator.sendBeacon(n, "&post_res=") : s.warn("[arms] navigator.sendBeacon not surported")
          }(t, this.getConfig("imgUrl"))
        },
        $ar: function() {},
        $as: function() {
          return {}
        },
        setCommonInfo: function(t) {
          t && "object" == a(t) && (this._common = s.ext({}, this._common, t))
        },
        $al: function() {
          this.pageview = s.uu(), this.sBegin = Date.now()
        },
        $at: function() {
          if (this.username) return this.username;
          var t = this._conf,
            e = t && t.setUsername;
          if ("function" == typeof e) try {
            var n = e();
            "string" == typeof n && (n = n.substr(0, 40), this.username = n)
          } catch (t) {
            s.warn("[arms] setUsername fail", t)
          }
          return this.username
        },
        getTraceId: function() {
          var t = this.rip,
            e = Date.now(),
            n = s.getSortNum(this.record),
            r = t + e + n + s.getRandNum(this._conf.pid);
          return this["EagleEye-TraceID"] = r, this.record = n, {
            "EagleEye-TraceID": r
          }
        },
        getUberTraceId: function(t) {
          var e = this.rip,
            n = Date.now(),
            r = s.getSortNum(this.record),
            o = s.getRandNum(this._conf.pid),
            i = e + n + r + s.getNum(2) + o,
            a = i.substring(0, 16);
          return {
            "uber-trace-id": i + ":" + a + ":0:" + (t = t ? "1" : "0"),
            traceId: i
          }
        },
        getPageviewId: function() {
          return {
            "EagleEye-SessionID": this.pageview
          }
        },
        getConfig: function(t) {
          return t ? this._conf[t] : s.ext({}, this._conf)
        },
        $au: function(t) {
          return 1 === t || ("boolean" == typeof this.$aj[t] || (this.$aj[t] = s.pick(t)), this.$aj[t])
        },
        $ai: function(t) {
          var e;
          clearTimeout(this.$ah), this.$ah = null;
          for (var n = this._conf && "function" == typeof this._conf.sendRequest; e = this.$af.pop();) "res" === e.t ? this.$ar(e, "res") : "error" === e.t ? this.$ar(e, "err") : "api" === e.t ? this.$ar(e, "apiSnapshot") : "behavior" === e.t ? this.$ar(e, "behavior") : "health" === e.t && !n && r && r.navigator && "function" == typeof r.navigator.sendBeacon ? this.$aq(e) : this.$ap(e);
          return t && this.$av(), this
        },
        $av: function() {
          var t;
          for (clearTimeout(this.$aw), this.$aw = null; t = this.$ak.pop();) this.$ar(t, "err");
          return this
        },
        _lg: function(t, e, n, r) {
          var o = this._conf,
            i = this.$am(),
            a = o.ignore || {},
            c = a.ignoreErrors,
            u = a.ignoreResErrors,
            l = a.ignoreUrls,
            p = a.ignoreApis;
          if (this._isRobot) return this;
          if (s.$a5(i, l) || s.$a5(s.decode(i), l)) return this;
          if ("error" === t && (s.$a5(e.msg, c) || s.$a5(s.decode(e.msg), c))) return this;
          if ("resourceError" === t && (s.$a5(e.src, u) || s.$a5(s.decode(e.src), u))) return this;
          if ("api" === t && (s.$a5(e.api, p) || s.$a5(s.decode(e.api), p))) return this;
          if (!this.$ao(o.imgUrl)) return this;
          if (!e || o.disabled || !o.pid) return this;
          if (0 === r) return this;
          var d = e.dl;
          return delete e.dl, e = s.ext({
            t: t,
            times: 1,
            page: i,
            tag: o.tag || "",
            release: o.release || "",
            environment: o.environment,
            begin: Date.now(),
            c1: o.c1,
            c2: o.c2,
            c3: o.c3
          }, e, this.$as(), this._common, {
            pid: o.pid,
            _v: this.ver,
            pv_id: this.pageview,
            username: this.$at(),
            sampling: n || 1,
            dl: d,
            z: s.seq()
          }), 1 === r ? f(this, e) : n && !this.$au(n) ? this : f(this, e)
        },
        _self: function(t, e, n) {
          var r = this,
            o = r._conf;
          if ("error" !== t) return r;
          if (!r.$ao(o.imgUrl)) return r;
          if (!e || o.disabled || !o.pid) return r;
          if (n && !r.$au(n)) return r;
          e = s.ext({
            t: t,
            times: 1,
            page: s.selfErrPage,
            tag: o.pid,
            begin: Date.now()
          }, e, {
            pid: l,
            _v: r.ver,
            sampling: n || 1,
            z: s.seq()
          });
          var i = r.$ak[0];
          if (i) {
            i.times++;
            try {
              i.err && e.err && i.err.msg_raw && e.err.msg_raw && i.err.msg_raw.split("&").indexOf(e.err.msg_raw) < 0 && i.err.msg_raw.length < 1e3 && (i.err.msg_raw += "&" + e.err.msg_raw)
            } catch (t) {}
          } else r.$ak.unshift(e), r.$ag((function() {
            r.sdkFlag && (r.sdkFlag = !1, r.$aw = s.delay((function() {
              r.$av()
            }), 1e4))
          }))
        },
        custom: function(t, e) {
          if (!t || "object" != a(t)) return this;
          var n = !1,
            r = {
              begin: Date.now()
            };
          return s.each(t, (function(t, e) {
            return !(n = e && e.length <= 20) && s.warn("[retcode] invalid key: " + e), r["x-" + e] = t, n
          })), n ? this._lg("custom", r, e || 1) : this
        }
      };
      var d = p,
        h = ["api", "success", "time", "code", "msg", "trace", "traceId", "begin", "pv_id", "sid", "seq", "domain", "flag"],
        g = function(t) {
          var e = (t.key || "default").split("::");
          return e.length > 1 ? s.ext(t, {
            group: e[0],
            key: e[1]
          }) : s.ext(t, {
            group: "default_group",
            key: e[0]
          })
        },
        v = function(t) {
          var e;
          d.call(this, t);
          try {
            e = "object" == ("undefined" == typeof performance ? "undefined" : a(performance)) ? performance.timing.fetchStart : Date.now()
          } catch (t) {
            e = Date.now()
          }
          return this._startTime = e, this
        };
      v.prototype = s.$a2(d.prototype), s.ext(d.dftCon, {
        startTime: null
      }), s.ext(v.prototype, {
        constructor: v,
        _super: d,
        sum: function(t, e, n) {
          try {
            var r = s.$ae(t, e, 1);
            return this._lg("sum", g(r), n)
          } catch (t) {
            s.warn("[retcode] can not get parseStatData: " + t)
          }
        },
        avg: function(t, e, n) {
          try {
            var r = s.$ae(t, e, 0);
            return this._lg("avg", g(r), n)
          } catch (t) {
            s.warn("[retcode] can not get parseStatData: " + t)
          }
        },
        percent: function(t, e, n, r) {
          try {
            return this._lg("percent", g({
              key: t,
              subkey: e,
              val: n || 0,
              begin: Date.now()
            }), r)
          } catch (t) {
            s.warn("[retcode] can not get parseStatData: " + t)
          }
        },
        msg: function(t, e) {
          if (t && !(t.length > 180)) return this.custom({
            msg: t
          }, e)
        },
        error: function(t, e) {
          if (!t) return s.warn("[retcode] invalid param e: " + t), this;
          1 === arguments.length ? ("string" == typeof t && (t = {
            message: t
          }, e = {}), "object" == a(t) && (e = t = t.error || t)) : ("string" == typeof t && (t = {
            message: t
          }), "object" != a(e) && (e = {}));
          var n = t.name || "CustomError",
            r = t.message || "",
            i = t.stack || "";
          e = e || {};
          var c = "object" == (void 0 === o ? "undefined" : a(o)) && "string" == typeof o.href && o.href.substring(0, 500) || "";
          if (s.$ac(r, e.filename)) {
            var u = /^Script error\.?$/,
              l = t.msg || t.message;
            if (s.$a5(l, u) || s.$a5(s.decode(l), u)) return this;
            var f = {
              msg: s.selfErrKey,
              err: {
                msg_raw: s.encode(t.msg || t.message)
              }
            };
            return this._self("error", f, 1)
          }
          for (var p = {
              begin: Date.now(),
              cate: n,
              msg: r && r.substring(0, 1e3),
              stack: i && i.substring(0, 1e3),
              file: s.$aa(e.filename || ""),
              line: e.lineno || "",
              col: e.colno || "",
              err: {
                msg_raw: s.encode(r),
                stack_raw: s.encode(i)
              },
              dl: c
            }, d = ["tag", "c1", "c2", "c3"], h = 0; h < d.length; h++) {
            var g = d[h];
            e[g] && (p[g] = e[g])
          }
          var v = (this.getConfig("ignore") || {}).ignoreErrors;
          return s.$a5(p.msg, v) || s.$a5(s.decode(p.msg), v) ? this : (this.$az && this.$az("error", p), this._lg("error", p, 1))
        },
        behavior: function(t) {
          if (t) {
            var e = "object" == a(t) && t.behavior ? t : {
              behavior: t
            };
            return this.$az && this.$az("behavior", e), this._lg("behavior", e, 1)
          }
        },
        api: function(t, e, n, r, i, c, u, l, f, p, d, g) {
          if (!t) return s.warn("[retcode] api is null"), this;
          if (t = "string" == typeof t ? {
              api: t,
              success: e,
              time: n,
              code: r,
              msg: i,
              begin: c,
              traceId: u,
              pv_id: l,
              apiSnapshot: f,
              domain: p,
              flag: g
            } : s.sub(t, h), !s.$a7(t.api, !0)) return this;
          t.code = t.code || "";
          var v = t.msg || "";
          if (v = "string" == typeof v ? v.substring(0, 1e3) : v, t.msg = v, t.success = t.success ? 1 : 0, t.time = +t.time, t.begin = t.begin, t.traceId = t.traceId || "", t.pv_id = t.pv_id || "", t.domain = t.domain || "", t.flag = t.flag, t.dl = "object" == (void 0 === o ? "undefined" : a(o)) && "string" == typeof o.href && o.href.substring(0, 500) || "", t.success ? t.apiSnapshot && delete t.apiSnapshot : t.apiSnapshot = f, d && (t.traceOrigin = d), !t.api || isNaN(t.time)) return s.warn("[retcode] invalid time or api"), this;
          var m = (this.getConfig("ignore") || {}).ignoreApis;
          return s.$a5(t.api, m) || s.$a5(s.decode(t.api), m) ? this : (this.$az && this.$az("api", t), this._lg("api", t, t.success && this.getConfig("sample"), t.flag))
        },
        speed: function(t, e, n) {
          var r = this,
            o = this.getConfig("startTime") || this._startTime;
          return /^s(\d|1[0])$/.test(t) ? (e = "number" != typeof e ? Date.now() - o : e >= o ? e - o : e, r.$ax = r.$ax || {}, r.$ax[t] = e, r.$ax.begin = o, clearTimeout(r.$ay), r.$ay = setTimeout((function() {
            n || (r.$ax.page = r.$am(!0)), r._lg("speed", r.$ax), r.$ax = null
          }), 5e3), r) : (s.warn("[retcode] invalid point: " + t), r)
        },
        performance: function(t) {
          if (t && "object" == a(t) && !this.$b0) {
            var e = {},
              n = {},
              r = this.getConfig("autoSendPerf");
            if (t.autoSend && r) return n = s.ext(this.$b1 || {}, t), this.$b0 = !0, this._lg("perf", n, this.getConfig("sample"));
            if (t.autoSend && !r) return delete t.autoSend, this.$b1 ? (n = s.ext(this.$b1 || {}, t), this.$b0 = !0, this._lg("perf", n, this.getConfig("sample"))) : void(this.$b1 = t);
            for (var o in t)(/^t([1-9]|1[0])$/.test(o) || "ctti" === o || "cfpt" === o) && (e[o] = t[o]);
            if (!0 === t.autoSend || !r && (r || this.$b1)) return !0 !== t.autoSend && !1 === r && this.$b1 ? (e = s.ext(this.$b1 || {}, e), this.$b0 = !0, this._lg("perf", e, this.getConfig("sample"))) : void 0;
            this.$b1 = s.ext(this.$b1 || {}, e)
          }
        },
        resource: function(t, e) {
          if (!t || !s.isPlainObject(t)) return s.warn("[arms] invalid param data: " + t), this;
          var n = Object.keys(t),
            r = ["begin", "dom", "load", "res", "dl"],
            o = !1;
          for (var i in r)
            if (n.indexOf(r[i]) < 0) {
              o = !0;
              break
            } if (o) return s.warn("[arms] lack param data: " + t), this;
          var a = {
            begin: t.begin || Date.now(),
            dom: t.dom || "",
            load: t.load || "",
            res: s.isArray(t.res) ? JSON.stringify(t.res) : JSON.stringify([]),
            dl: t.dl || ""
          };
          return this._lg("res", a, e)
        },
        event: function(t, e) {
          if ("object" == a(t) && t && t.key) {
            var n = {},
              r = ["key", "success", "time", "c1", "c2", "c3"];
            for (var o in t) r.indexOf(o) > -1 && (n[o] = t[o]);
            n.success = !1 === t.success ? 0 : 1, this._lg("event", n, e)
          }
        }
      }), v._super = d, v._root = d, d.Reporter = v;
      var m = v,
        y = function(t) {
          t && t.pid || s.warn("[arms] pid is a required prop to instatiate MiniProgramLogger");
          var e = this;
          return m.call(e, t), e.appBegin = Date.now(), e._health = {
            errcount: 0,
            apisucc: 0,
            apifail: 0
          }, e.DEFAUT_PAGE_PATH = "[app]", e.isSendPerf = !1, e.$az = function(t, n) {
            "error" === t ? e._health.errcount++ : "api" === t && e._health[n.success ? "apisucc" : "apifail"]++
          }, "function" == typeof e.$b2 && e.$b2(), e._conf && e._conf.behavior && "function" == typeof e.$b3 && e.$b3(), "function" == typeof e.$b4 && e.$b4(), this
        };
      y.prototype = s.$a2(m.prototype), s.ext(m._root.dftCon, {
        uid: null,
        setUsername: null,
        disableHook: !1,
        enableLinkTrace: !1,
        behavior: !1,
        enableConsole: !1,
        sendRequest: function() {},
        getCurrentPage: function() {}
      }), s.ext(y.prototype, {
        constructor: y,
        _super: m,
        setPage: function(t, e) {
          var n = this,
            r = n.$b5;
          if (!1 !== e) {
            if (!t || t === r) return n;
            n.$b5 = t, clearTimeout(n.$b6), n.$al(), n.$b6 = setTimeout((function() {
              n.$b7()
            }), 10)
          }
          n._conf.page = t
        },
        $ag: function(t) {
          t()
        },
        $ap: function(t, e) {
          if (this.getConfig("debug")) "undefined" != typeof console && console && "function" == typeof console.log && console.log("[arms] [DEBUG MODE] log data", t);
          else {
            var n = this.getConfig("imgUrl");
            "object" == a(t) && (t = s.serialize(t));
            var r = n + t;
            e && (r += "&post_res=");
            var o = this._conf.sendRequest;
            if ("function" == typeof o) try {
              o(r, e)
            } catch (t) {
              s.warn("[arms] error in $ap", t)
            }
          }
        },
        $ar: function(t, e) {
          var n = {};
          n[e] = t[e], delete t[e], this.$ap(t, n)
        },
        $am: function() {
          var t = this._conf.page;
          if ("string" == typeof t && t) return s.$a3(t, [], t + "");
          var e = this._conf.getCurrentPage;
          if ("function" == typeof e) try {
            var n = e();
            if (n && "string" == typeof n) return n
          } catch (t) {
            s.warn("[arms] error in $am", t)
          }
          return "string" == typeof e && e ? e : this.DEFAUT_PAGE_PATH
        },
        $b8: function() {
          this.setCommonInfo({
            sid: s.uu()
          })
        },
        setConfig: function(t) {
          if (t && "object" == a(t)) {
            s.$a6(t), t = this.$an(t);
            var e = this._conf;
            this._conf = s.ext({}, this._conf, t);
            var n = "disableHook";
            n in t && e[n] !== t[n] && (t[n] ? "function" == typeof this.removeHook && this.removeHook() : "function" == typeof this.addHook && this.addHook())
          }
        },
        appLaunch: function() {
          this.appBegin = Date.now()
        },
        pageShow: function() {
          var t = this;
          t.$al(), t.$b9(), clearTimeout(t.$b6), t.$ba(), t.$b6 = setTimeout((function() {
            t.$b7()
          }), 50), t.pvPage = t.$am()
        },
        pageHide: function() {
          this.$ba()
        },
        addHook: function() {
          return this
        },
        removeHook: function() {
          return this
        },
        hookApp: function(t) {
          var e = this,
            n = {
              onLaunch: function() {
                var n = 1 === arguments.length ? [arguments[0]] : Array.apply(null, arguments),
                  r = t.onLaunch;
                try {
                  e.appLaunch()
                } catch (t) {
                  s.warn("[arms] error in hookApp:onLaunch", t)
                }
                if ("function" == typeof r) return r.apply(this, n)
              },
              onError: function(n) {
                var r = 1 === arguments.length ? [arguments[0]] : Array.apply(null, arguments),
                  o = t.onError;
                try {
                  e.error(n), e.getConfig("behavior") && "function" == typeof e.reportBehavior && e.reportBehavior()
                } catch (n) {
                  s.warn("[arms] error in hookApp:onError", n)
                }
                if ("function" == typeof o) return o.apply(this, r)
              }
            };
          return s.ext({}, t, n)
        },
        hookPage: function(t) {
          var e = this,
            n = {
              onShow: function() {
                var n = 1 === arguments.length ? [arguments[0]] : Array.apply(null, arguments),
                  r = t.onShow;
                try {
                  e.pageShow()
                } catch (t) {
                  s.warn("[arms] error in hookPage:pageShow", t)
                }
                if ("function" == typeof r) return r.apply(this, n)
              },
              onHide: function() {
                var n = 1 === arguments.length ? [arguments[0]] : Array.apply(null, arguments),
                  r = t.onHide;
                try {
                  e.pageHide()
                } catch (t) {
                  s.warn("[arms] error in hookPage:onHide", t)
                }
                if ("function" == typeof r) return r.apply(this, n)
              },
              onUnload: function() {
                var n = 1 === arguments.length ? [arguments[0]] : Array.apply(null, arguments),
                  r = t.onUnload;
                try {
                  e.pageHide()
                } catch (t) {
                  s.warn("[arms] error in hookPage:onUnload", t)
                }
                if ("function" == typeof r) return r.apply(this, n)
              }
            };
          return s.ext({}, t, n)
        },
        $b2: function() {},
        $b4: function() {
          this.setCommonInfo({
            app: "mini_common",
            uid: this._conf.uid
          })
        },
        $b7: function() {
          var t = this;
          t.$ag((function() {
            t._lg("pv", {}, t.getConfig("pvSample"))
          }))
        },
        $b9: function() {
          var t = this;
          t.isSendPerf || (t.$ag((function() {
            var e = {
              fpt: Date.now() - t.appBegin
            };
            t._lg("perf", e, t.getConfig("sample"))
          })), t.isSendPerf = !0)
        },
        $ba: function() {
          this.$bb(), this.$ax && (this._lg("speed", this.$ax), this.$ax = null, clearTimeout(this.$ay)), this.$ai(!0)
        },
        $bb: function() {
          if (this.pvPage) {
            var t = s.ext({}, this._health);
            t.healthy = t.errcount > 0 ? 0 : 1, t.begin = Date.now();
            var e = t.begin - this.sBegin;
            t.page = this.pvPage, t.stay = e, this._lg("health", t, 1), this._health = {
              errcount: 0,
              apisucc: 0,
              apifail: 0
            }, this.pvPage = null
          }
        }
      });
      var b = null,
        w = function(t) {
          return b || (b = new y(t || {})), b
        };
      y.createExtraInstance = function(t) {
        return t && "object" == a(t) ? (t.disableHook = !0, t.behavior = !1) : t = {
          disableHook: !0,
          behavior: !1
        }, new y(t)
      }, y.init = w, y.singleton = w, y._super = m, y._root = m._root, m.MiniProgramLogger = y;
      var S = y,
        _ = "ARMS_STORAGE_MINIPROGRAM_WX_UID_KEY",
        x = function(t) {
          return S.call(this, t), this.$be(), this
        };
      x.prototype = s.$a2(S.prototype), s.ext(S._root.dftCon, {
          sendRequest: function(t, e) {
            if ("undefined" != typeof wx && wx && "function" == typeof wx.request) try {
              var n, r = "GET";
              e && (r = "POST", n = JSON.stringify(e)), wx.request({
                url: t,
                method: r,
                data: n,
                fail: function(t) {
                  s.warn("[arms] sendRequest fail", t)
                }
              })
            } catch (t) {
              s.warn("[arms] error in conf sendRequest", t)
            }
          },
          getCurrentPage: function() {
            if ("function" == typeof getCurrentPages) try {
              var t = getCurrentPages() || [],
                e = t[t.length - 1];
              return e && e.route || null
            } catch (t) {
              s.warn("[arms] error in conf getCurrentPage", t)
            }
          }
        }), s.ext(x.prototype, {
          constructor: x,
          _super: S,
          $b4: function() {
            this.setCommonInfo({
              app: "mini_wx"
            }), this.$bf(), this.$bg(), this.$bh(), this.$b8()
          },
          $bh: function() {
            if (this._conf && this._conf.uid) this.setCommonInfo({
              uid: this._conf.uid
            });
            else if ("undefined" != typeof wx && wx && "function" == typeof wx.getStorageSync) try {
              var t = wx.getStorageSync(_);
              if (t && "string" == typeof t) this.setCommonInfo({
                uid: t
              });
              else if ("function" == typeof wx.setStorageSync) {
                var e = s.uu();
                wx.setStorageSync(_, e), this.setCommonInfo({
                  uid: e
                })
              }
            } catch (t) {
              s.warn("[arms] error in $bh", t)
            }
          },
          $bf: function() {
            if ("undefined" != typeof wx && wx && "function" == typeof wx.getSystemInfoSync) try {
              var t = wx.getSystemInfoSync();
              "object" == a(t) && this.setCommonInfo({
                sr: (t.screenWidth || 0) + "x" + (t.screenHeight || 0),
                vp: (t.windowWidth || 0) + "x" + (t.windowHeight || 0),
                dpr: t.pixelRatio,
                ul: t.language
              })
            } catch (t) {
              s.warn("[arms] error in $bf", t)
            }
          },
          $bg: function() {
            var t = this;
            if ("undefined" != typeof wx && wx && "function" == typeof wx.getNetworkType) try {
              wx.getNetworkType({
                success: function(e) {
                  e && "string" == typeof e.networkType && t.setCommonInfo({
                    ct: e.networkType
                  })
                },
                fail: function(t) {
                  s.warn("[arms] $bg getNetworkType fail", t)
                }
              })
            } catch (t) {
              s.warn("[arms] error in $bg", t)
            }
          },
          $be: function() {
            var t = this;
            if ("object" == ("undefined" == typeof wx ? "undefined" : a(wx)) && wx && "function" == typeof wx.getPerformance) {
              t.$b9 = function() {};
              try {
                wx.getPerformance().createObserver((function(e) {
                  var n = e.getEntries(),
                    r = {};
                  if (Array.isArray(n) && n.length > 0)
                    for (var o = 0; o < n.length; o++) {
                      var i = n[o];
                      switch (i.name) {
                        case "appLaunch":
                          r.launch = i.duration;
                          break;
                        case "firstRender":
                          r.fpt = i.duration
                      }
                    }
                  Object.keys(r).length > 0 && t._lg("perf", r, t.getConfig("sample"))
                })).observe({
                  entryTypes: ["navigation", "render"]
                })
              } catch (t) {
                s.warn("[ARMS] $be error :", t)
              }
            }
          },
          hookApp: function(t) {
            var e = this,
              n = {
                onError: function(n) {
                  var r = 1 === arguments.length ? [arguments[0]] : Array.apply(null, arguments),
                    o = t.onError;
                  try {
                    if (n && "object" == a(n) && e.error(n), n && "string" == typeof n) {
                      var i = n.split("\n"),
                        c = "",
                        u = "";
                      i.length > 1 && (c = i[0] && i[0].length < 100 ? i[0] : i[0].substring(0, 100), u = i[1]), e.error({
                        name: c,
                        message: u || n,
                        stack: n
                      })
                    }
                  } catch (n) {
                    s.warn("[arms] error in hookApp:onError", n)
                  }
                  if ("function" == typeof o) return o.apply(this, r)
                },
                onLaunch: function() {
                  var n = 1 === arguments.length ? [arguments[0]] : Array.apply(null, arguments),
                    r = t.onLaunch;
                  try {
                    e.appLaunch()
                  } catch (t) {
                    s.warn("[arms] error in hookApp:onLaunch", t)
                  }
                  if ("function" == typeof r) return r.apply(this, n)
                }
              };
            return s.ext({}, t, n)
          }
        }),
        function(t) {
          var e = s,
            n = null,
            r = {};
          e.ext(t.prototype, {
            addHook: function() {
              return this.isHookInstantiated || (function() {
                var t = this;
                if ("undefined" != typeof wx && wx && "function" == typeof wx.request) {
                  n = wx;
                  var o = {
                    request: function(n) {
                      var r = (new Date).getTime();
                      if (n && "object" == a(n) && n[0]) {
                        var o, i, c = n[0],
                          u = e.$a9(c.url),
                          s = c.success,
                          l = c.fail,
                          f = c && c.header;
                        f && "object" == a(f) || (f = {});
                        var p = {};
                        if (t.getConfig("enableLinkTrace")) {
                          var d = f["EagleEye-pAppName"];
                          if (o = f["EagleEye-TraceID"], i = f["EagleEye-SessionID"], o || (o = t.getTraceId()["EagleEye-TraceID"], p["EagleEye-TraceID"] = o), i || (i = t.getPageviewId()["EagleEye-SessionID"], p["EagleEye-SessionID"] = i), !d) {
                            var h = t.getConfig("pid");
                            p["EagleEye-pAppName"] = h
                          }
                        }
                        c.success = function() {
                          var a = (new Date).getTime();
                          if (e.$a7(u, !0)) {
                            var c = arguments && arguments[0] && arguments[0].statusCode || 200;
                            t.api({
                              api: n[0].url,
                              success: !0,
                              time: a - r,
                              code: c,
                              begin: r,
                              traceId: o,
                              pv_id: i
                            })
                          }
                          s && s.apply(t, [].slice.call(arguments))
                        }, c.fail = function() {
                          var c = (new Date).getTime();
                          if (e.$a7(u, !0)) {
                            var s = "";
                            arguments && arguments[0] && "object" == a(arguments[0]) && (s = (s = JSON.stringify(arguments[0])).substring(0, 1e3));
                            var f = arguments && arguments[0] && arguments[0].statusCode || "FAILED";
                            t.api({
                              api: n[0].url,
                              success: !1,
                              time: c - r,
                              code: f,
                              msg: s,
                              begin: r,
                              traceId: o,
                              pv_id: i
                            })
                          }
                          l && l.apply(t, [].slice.call(arguments))
                        }, c.header = e.ext({}, f, p)
                      }
                    }
                  };
                  for (var i in wx)
                    if (o[i]) {
                      var c = i.toString();
                      r[c] = function() {
                        return o[c](arguments), n[c].apply(n, [].slice.call(arguments))
                      }
                    } else r[i] = n[i];
                  wx = r
                }
              }.call(this), this.isHookInstantiated = !0), this
            },
            removeHook: function() {
              return this.isHookInstantiated ? (function() {
                "undefined" != typeof wx && wx && n && (wx = n, n = null)
              }.call(this), this.isHookInstantiated = !1, this) : this
            },
            $b2: function() {
              return this.$bc || (this.getConfig("disableHook") || this.addHook(), this.$bc = !0), this
            }
          })
        }(x), "undefined" != typeof wx && wx && function(t, e) {
          var n = null,
            r = "",
            o = function(t) {
              return function() {
                if (t && n) {
                  var e = n,
                    r = t + "timmer";
                  e[r] && (clearTimeout(e[r]), e[r] = void 0), e[r] = setTimeout((function() {
                    "function" == typeof e.addBehavior && e.addBehavior({
                      type: "ui.default",
                      data: {
                        level: t
                      }
                    })
                  }), 100)
                }
              }
            },
            i = function(t) {
              var e = [],
                n = s.reWriteMethod,
                r = {
                  consoleBehavior: function() {
                    if (console) {
                      var t = ["debug", "info", "warn", "log", "error"],
                        e = null;
                      if (Function && (e = Function.prototype.apply || Function.apply), "function" == typeof e)
                        for (var r = this, o = 0; o < t.length; o++) {
                          var i = t[o];
                          "function" == typeof console[i] && n(console, i, (function(t) {
                            var n = i;
                            return function() {
                              for (var o = arguments.length, i = new Array(o), a = 0; a < o; a++) i[a] = arguments[a];
                              var c = {
                                type: "console",
                                data: {
                                  level: n,
                                  message: i
                                }
                              };
                              r && "function" == typeof r.addBehavior && r.addBehavior(c), "function" == typeof t && e.call(t, console, i)
                            }
                          }))
                        }
                    }
                  }
                };
              return s.ext(t.prototype, {
                addBehavior: function(t) {
                  if (this.getConfig("behavior") && t && "object" == a(t)) {
                    var n = "";
                    this._conf && "function" == typeof this._conf.getCurrentPage && (n = this._conf.getCurrentPage());
                    var r = null;
                    try {
                      r = Date.now()
                    } catch (t) {
                      return void s.warn("[arms] error in Date.now", t)
                    }
                    var o = {},
                      i = t.data || {};
                    if (t.type) o = i;
                    else {
                      if ("string" != typeof i.name || "string" != typeof i.message) return;
                      o.name = i.name.substr(0, 20), o.message = i.message.substr(0, 200)
                    }
                    o.message && (o.message = s.encode(o.message));
                    var c = {
                      type: t.type || "custom",
                      data: o || {},
                      timestamp: t.timestamp || r,
                      page: t.page || n
                    };
                    return e.push(c), e = e.slice(-100)
                  }
                },
                getBehavior: function() {
                  return e || []
                },
                setBehavior: function(t) {
                  return t && (e = t), e
                },
                reportBehavior: function() {
                  var t = this;
                  t.getConfig("behavior") && (t.$bd && (clearTimeout(t.$bd), t.$bd = void 0), t.$bd = setTimeout((function() {
                    e && e.length > 0 && ("function" == typeof t.behavior && t.behavior(e), e = [])
                  }), 0))
                },
                $b3: function() {}
              }), r
            }(t) || {};
          s.ext(t.prototype, {
            $b3: function() {
              if (!this.hasInitBehavior && !n && void 0 !== e && e) {
                try {
                  this.getConfig("enableConsole") && "function" == typeof i.consoleBehavior && i.consoleBehavior.call(this), e && ("function" == typeof e.onKeyboardHeightChange && e.onKeyboardHeightChange(o("KeyboardHeightChange")), "function" == typeof e.onPageNotFound && e.onPageNotFound(o("PageNotFound")), "function" == typeof e.onAppShow && e.onAppShow(o("AppShow")), "function" == typeof e.onAppHide && e.onAppHide(o("AppHide"))), e && "function" == typeof e.onAppRoute && e.onAppRoute((function(t) {
                    var e = {
                      type: "navigation",
                      data: {
                        level: t && t.openType || "unknown",
                        from: r || "unknown",
                        to: t && t.path || "unknown"
                      }
                    };
                    n && "function" == typeof n.addBehavior && n.addBehavior(e), r = t && t.path || ""
                  })), e && "function" == typeof e.onError && e.onError(this.reportBehavior.bind(this))
                } catch (t) {
                  s.warn("[arms] error in initBehavior", t)
                }
                return n = this, this.hasInitBehavior = !0, this
              }
            }
          })
        }(x, wx);
      var T = null,
        k = function(t) {
          return T || (T = new x(t || {})), T
        };
      x.createExtraInstance = function(t) {
        return t && "object" == a(t) ? (t.disableHook = !0, t.behavior = !1) : t = {
          disableHook: !0,
          behavior: !1
        }, new x(t)
      }, x.init = k, x.singleton = k, x._super = S, x._root = S._root, S.WXLogger = x;
      var E = x;
      t.exports = E
    }
  }
]);