.center-user {
    background-color: #f7f7f8;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 100vh;
    overflow: auto;
    padding-top: 30rpx
}

.center-user-info {
    background-color: #fff;
    border-radius: 12rpx;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin: 0 24rpx;
    padding: 40rpx
}

.center-user-info__avatar {
    border-radius: 50%;
    overflow: hidden
}

.center-user-info__avatar,.center-user-info__img {
    height: 128rpx;
    width: 128rpx
}

.center-user-info .user-info-detail {
    margin-left: 40rpx;
    text-align: left
}

.center-user-info .user-info-detail .not-login {
    margin-top: 40rpx
}

.center-user-info .user-info-detail-name {
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    color: #333;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    font-family: PingFang SC;
    font-size: 32rpx;
    font-weight: 600
}

.center-user-info .user-info-detail-name__text {
    display: inline-block;
    max-width: 300rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.center-user-info .user-info-detail__grade,.center-user-info .user-info-detail__phone {
    color: #666;
    font-size: 28rpx;
    margin-top: 4rpx
}

.center-user-info__icon {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='16' height='16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m6 12 4-4-4-4' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat;
    background-size: 100% 100%;
    color: #333;
    display: inline-block;
    height: 32rpx;
    margin-left: 12rpx;
    width: 32rpx
}

.center-user .out-link-box {
    -ms-flex-pack: justify;
    background-color: #fff;
    border-radius: 12rpx;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin: 30rpx 24rpx 0rpx;
    padding: 34rpx 98rpx
}

.center-user .out-link-box .out-link-item {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: center;
    justify-content: center
}

.center-user .out-link-box .out-link-item__img {
    height: 52rpx;
    width: 52rpx
}

.center-user .out-link-box .out-link-item__text {
    color: #333;
    font-family: PingFang SC;
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    height: 26rpx;
    line-height: 26rpx;
    margin-top: 18rpx;
    text-align: center;
    text-transform: none
}

.center-user .out-link-box-1 {
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    margin: 24rpx
}

.center-user .out-link-box-1,.center-user .out-link-box-1 .out-link-item {
    -ms-flex-pack: justify;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between
}

.center-user .out-link-box-1 .out-link-item {
    background: -webkit-linear-gradient(318deg,#fffdf5,#fff);
    background: linear-gradient(132deg,#fffdf5,#fff);
    border-radius: 24rpx 24rpx 24rpx 24rpx;
    height: 144rpx;
    width: 340rpx
}

.center-user .out-link-box-1 .out-link-item .left-box {
    margin-left: 36rpx
}

.center-user .out-link-box-1 .out-link-item .left-box .title {
    color: #333;
    font-family: PingFang SC;
    font-size: 32rpx;
    font-style: normal;
    font-weight: 500;
    height: 44rpx;
    text-align: left;
    text-transform: none;
    width: 128rpx
}

.center-user .out-link-box-1 .out-link-item .left-box .subtitle {
    color: #d09c49;
    font-family: PingFang SC;
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    height: 34rpx;
    text-align: left;
    text-transform: none;
    width: 120rpx
}

.center-user .out-link-box-1 .out-link-item .right-icon {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    -webkit-align-items: center;
    align-items: center;
    background: -webkit-gradient(linear,left top,left bottom,from(#fffae8),to(rgba(255,250,232,.5)));
    background: -webkit-linear-gradient(top,#fffae8,rgba(255,250,232,.5));
    background: linear-gradient(180deg,#fffae8,rgba(255,250,232,.5));
    border-radius: 176rpx 176rpx 176rpx 176rpx;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: 112rpx;
    -webkit-justify-content: center;
    justify-content: center;
    margin-right: 24rpx;
    opacity: .7;
    width: 112rpx
}

.center-user .out-link-box-1 .out-link-item .right-icon .img {
    height: 112rpx;
    width: 112rpx
}

.center-user .out-link-box-1 .out-link-item_kapai {
    background: -webkit-linear-gradient(319deg,#f5fbff,#fff);
    background: linear-gradient(131deg,#f5fbff,#fff)
}

.center-user-container {
    background-color: #fff;
    border-radius: 12rpx;
    margin: 30rpx 24rpx 0rpx
}

.center-user-sline {
    border-top: 2rpx solid #eee;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 2rpx;
    margin: 0 40rpx;
    -webkit-transform: scaleY(.5);
    -ms-transform: scaleY(.5);
    transform: scaleY(.5)
}

.center-user-about,.center-user-service {
    padding: 40rpx
}

.center-user-about,.center-user-about__text,.center-user-service,.center-user-service__text {
    -ms-flex-align: center;
    -ms-flex-pack: justify;
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between
}

.center-user-about__text,.center-user-service__text {
    color: #333;
    font-family: PingFang SC;
    font-size: 28rpx;
    font-weight: 400
}

.center-user-about .img-icon,.center-user-service .img-icon {
    height: 36rpx;
    margin-right: 20rpx;
    width: 36rpx
}

.center-user-about__icon,.center-user-service__icon {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23D8D8D8' fill-opacity='.01' d='M0 0h12v12H0z'/%3E%3Cpath d='m4.881 2.072 3.748 3.716a.3.3 0 0 1 0 .424L4.88 9.95' stroke='%23A6A7AB' stroke-width='1.8' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat;
    background-size: 100% 100%;
    color: #333;
    display: inline-block;
    height: 24rpx;
    width: 24rpx
}

.wrong-tips__btn {
    background: -webkit-linear-gradient(353deg,#ff664f 4.33%,#fa3b2d 90.21%);
    background: linear-gradient(97deg,#ff664f 4.33%,#fa3b2d 90.21%);
    border-radius: 44rpx;
    bottom: 60rpx;
    color: #fff;
    font-size: 28rpx;
    font-weight: 500;
    height: 80rpx;
    left: 50%;
    line-height: 80rpx;
    margin: 0 auto 0 -200rpx;
    position: fixed;
    text-align: center;
    width: 400rpx
}
